# -*- coding: utf-8 -*-
"""
ML-T1 (أمؤلي-T1) - نموذج الذكاء الاصطناعي المتطور والمتعلم ذاتياً
نظام ذكي يتعلم ويفكر ويناقش مع نفسه ويولد الأسئلة بنفسه
"""

import re
import random
import time
import json
import os
from datetime import datetime
from collections import defaultdict
import hashlib

# استيراد جامع المعرفة من الإنترنت
try:
    from internet_knowledge_collector import InternetKnowledgeCollector
    INTERNET_AVAILABLE = True
except ImportError:
    INTERNET_AVAILABLE = False
    print("⚠️ جامع المعرفة من الإنترنت غير متاح")

class MLT1_AI:
    """نموذج ML-T1 (أمؤلي-T1) - الذكاء الاصطناعي المتعلم ذاتياً"""
    
    def __init__(self):
        # معلومات النموذج
        self.model_name = "ML-T1 (أمؤلي-T1)"
        self.version = "1.0.0"
        self.creation_date = datetime.now()
        
        # نظام التعلم الذاتي
        self.knowledge_memory = self.load_knowledge_memory()
        self.learning_patterns = defaultdict(list)
        self.conversation_context = []
        self.learned_facts = {}
        self.generated_questions = []
        
        # نظام التفكير الذاتي
        self.thinking_depth = 3  # مستوى عمق التفكير
        self.self_discussion_enabled = True
        self.creative_mode = True
        
        # ذاكرة المحادثات والتعلم
        self.session_memory = {
            'conversations': [],
            'learned_concepts': set(),
            'user_preferences': {},
            'discussion_topics': [],
            'generated_insights': []
        }
        
        # نظام توليد الأسئلة الذكي
        self.question_templates = {
            'معرفة_عامة': [
                "ما هو {concept}؟",
                "كيف يعمل {concept}؟", 
                "ما أهمية {concept}؟",
                "ما علاقة {concept} بـ {related_concept}؟"
            ],
            'تاريخي': [
                "من هو {person}؟",
                "متى حدث {event}؟",
                "ما تأثير {event} على {context}؟",
                "كيف غير {person} مجال {field}؟"
            ],
            'علمي': [
                "كيف يؤثر {concept} على {target}؟",
                "ما الفرق بين {concept1} و {concept2}؟",
                "لماذا يحدث {phenomenon}؟",
                "ما مستقبل {technology}؟"
            ],
            'فلسفي': [
                "لماذا {concept} مهم؟",
                "كيف نفهم {abstract_concept}؟",
                "ما معنى {philosophical_term}؟",
                "هل {statement} صحيح؟"
            ]
        }
        
        # إعدادات التعلم المتقدم
        self.learning_settings = {
            'auto_learn': True,
            'deep_analysis': True,
            'pattern_recognition': True,
            'creative_thinking': True,
            'self_improvement': True,
            'internet_learning': INTERNET_AVAILABLE
        }

        # جامع المعرفة من الإنترنت
        if INTERNET_AVAILABLE:
            self.internet_collector = InternetKnowledgeCollector()
            print("🌐 جامع المعرفة من الإنترنت متاح!")
        else:
            self.internet_collector = None
    
    def load_knowledge_memory(self):
        """تحميل ذاكرة المعرفة من ملف"""
        try:
            if os.path.exists('ml_t1_memory.json'):
                with open('ml_t1_memory.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        
        # قاعدة معرفة أساسية للبداية
        return {
            'concepts': {},
            'relationships': {},
            'learned_facts': {},
            'patterns': {},
            'insights': []
        }
    
    def save_knowledge_memory(self):
        """حفظ ذاكرة المعرفة في ملف"""
        try:
            with open('ml_t1_memory.json', 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_memory, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الذاكرة: {e}")
    
    def learn_from_input(self, user_input):
        """التعلم من إدخال المستخدم"""
        # استخراج المفاهيم الجديدة
        concepts = self.extract_concepts(user_input)
        
        for concept in concepts:
            if concept not in self.knowledge_memory['concepts']:
                self.knowledge_memory['concepts'][concept] = {
                    'first_seen': datetime.now().isoformat(),
                    'frequency': 1,
                    'contexts': [user_input],
                    'related_concepts': []
                }
                print(f"🧠 تعلمت مفهوماً جديداً: {concept}")
            else:
                self.knowledge_memory['concepts'][concept]['frequency'] += 1
                self.knowledge_memory['concepts'][concept]['contexts'].append(user_input)
        
        # تحليل الأنماط
        self.analyze_patterns(user_input, concepts)
        
        # حفظ التعلم
        self.save_knowledge_memory()

    def check_casual_response(self, user_input):
        """التحقق من الردود العامية والتحيات"""
        user_input_clean = user_input.strip().lower()

        # التحقق من وجود ردود عامية في الذاكرة
        if 'casual_responses' in self.knowledge_memory:
            casual_responses = self.knowledge_memory['casual_responses']

            # البحث عن تطابق مباشر
            if user_input_clean in casual_responses:
                responses = casual_responses[user_input_clean]
                response = random.choice(responses)
                return f"😊 {response}"

            # البحث عن تطابق جزئي
            for trigger, responses in casual_responses.items():
                if trigger in user_input_clean or user_input_clean in trigger:
                    response = random.choice(responses)
                    return f"😊 {response}"

        # التحقق من الردود في الحقائق
        if 'facts' in self.knowledge_memory:
            for fact_id, fact_data in self.knowledge_memory['facts'].items():
                if fact_data.get('type') == 'casual_response':
                    trigger = fact_data.get('trigger', '').lower()
                    if trigger == user_input_clean or trigger in user_input_clean:
                        responses = fact_data.get('responses', [])
                        if responses:
                            response = random.choice(responses)
                            return f"😊 {response}"

        return None

    def extract_concepts(self, text):
        """استخراج المفاهيم من النص"""
        # إزالة كلمات الوصل والضمائر
        stop_words = {'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هو', 'هي', 'أن', 'كان', 'كانت', 'هذا', 'هذه', 'ذلك', 'تلك'}
        
        # تنظيف النص واستخراج الكلمات المهمة
        words = re.findall(r'\b[\u0600-\u06FF]+\b', text)
        concepts = [word for word in words if len(word) > 2 and word not in stop_words]
        
        return list(set(concepts))
    
    def analyze_patterns(self, text, concepts):
        """تحليل الأنماط في النص"""
        # تحليل نوع السؤال
        question_patterns = {
            'ما': 'تعريف',
            'من': 'شخص',
            'متى': 'زمن',
            'أين': 'مكان',
            'كيف': 'طريقة',
            'لماذا': 'سبب',
            'هل': 'استفهام'
        }
        
        for pattern, type_name in question_patterns.items():
            if pattern in text:
                if type_name not in self.learning_patterns:
                    self.learning_patterns[type_name] = []
                self.learning_patterns[type_name].append({
                    'text': text,
                    'concepts': concepts,
                    'timestamp': datetime.now().isoformat()
                })
    
    def generate_intelligent_question(self):
        """توليد سؤال ذكي بناءً على المعرفة المكتسبة"""
        print("🧠 أفكر في سؤال جديد بناءً على معرفتي...")
        time.sleep(1.5)
        
        # اختيار مفهوم من المعرفة المكتسبة
        if not self.knowledge_memory['concepts']:
            return self.generate_basic_question()
        
        # اختيار مفهوم عشوائي من المعرفة
        concept = random.choice(list(self.knowledge_memory['concepts'].keys()))
        concept_data = self.knowledge_memory['concepts'][concept]
        
        # تحديد نوع السؤال بناءً على السياق
        question_type = self.determine_question_type(concept, concept_data)
        
        # توليد السؤال
        question = self.create_question(concept, question_type, concept_data)
        
        # حفظ السؤال المولد
        generated_question = {
            'question': question['text'],
            'answer': question['answer'],
            'concept': concept,
            'type': question_type,
            'difficulty': question['difficulty'],
            'generated_at': datetime.now().isoformat(),
            'based_on_learning': True
        }
        
        self.generated_questions.append(generated_question)
        
        print(f"💡 ولدت سؤالاً جديداً عن: {concept}")
        
        return generated_question
    
    def determine_question_type(self, concept, concept_data):
        """تحديد نوع السؤال بناءً على المفهوم"""
        contexts = concept_data.get('contexts', [])
        
        # تحليل السياقات لتحديد النوع
        if any('تاريخ' in ctx or 'عصر' in ctx for ctx in contexts):
            return 'تاريخي'
        elif any('علم' in ctx or 'تقنية' in ctx for ctx in contexts):
            return 'علمي'
        elif any('لماذا' in ctx or 'معنى' in ctx for ctx in contexts):
            return 'فلسفي'
        else:
            return 'معرفة_عامة'
    
    def create_question(self, concept, question_type, concept_data):
        """إنشاء السؤال الفعلي"""
        templates = self.question_templates.get(question_type, self.question_templates['معرفة_عامة'])
        template = random.choice(templates)
        
        # استبدال المتغيرات في القالب
        question_text = template.replace('{concept}', concept)
        
        # إضافة مفاهيم مرتبطة إذا وجدت
        related_concepts = concept_data.get('related_concepts', [])
        if related_concepts and '{related_concept}' in question_text:
            related = random.choice(related_concepts)
            question_text = question_text.replace('{related_concept}', related)
        
        # توليد إجابة ذكية
        answer = self.generate_smart_answer(concept, question_type, concept_data)
        
        # تحديد صعوبة السؤال
        difficulty = self.calculate_difficulty(concept, concept_data)
        
        return {
            'text': question_text,
            'answer': answer,
            'difficulty': difficulty,
            'points': difficulty * 5
        }
    
    def generate_smart_answer(self, concept, question_type, concept_data):
        """توليد إجابة ذكية بناءً على المعرفة المكتسبة"""
        contexts = concept_data.get('contexts', [])
        
        # تحليل السياقات لاستخراج معلومات
        if contexts:
            # أخذ أكثر السياقات إفادة
            best_context = max(contexts, key=len)
            
            # استخراج معلومات من السياق
            if question_type == 'تاريخي':
                return f"مفهوم مرتبط بالتاريخ والحضارة، ظهر في سياق: {best_context[:50]}..."
            elif question_type == 'علمي':
                return f"مفهوم علمي أو تقني، له علاقة بـ: {concept}"
            elif question_type == 'فلسفي':
                return f"مفهوم فلسفي عميق يتطلب تأمل وتفكير"
            else:
                return f"مفهوم مهم تعلمته من خلال محادثاتنا"
        
        return f"مفهوم جديد أتعلم عنه: {concept}"
    
    def calculate_difficulty(self, concept, concept_data):
        """حساب صعوبة السؤال"""
        frequency = concept_data.get('frequency', 1)
        contexts_count = len(concept_data.get('contexts', []))
        
        # كلما قل التكرار والسياقات، زادت الصعوبة
        if frequency == 1 and contexts_count == 1:
            return 6  # صعب جداً
        elif frequency <= 2:
            return 4  # صعب
        elif frequency <= 5:
            return 3  # متوسط
        else:
            return 2  # سهل
    
    def generate_basic_question(self):
        """توليد سؤال أساسي عندما لا توجد معرفة مكتسبة"""
        basic_questions = [
            {
                'question': 'ما هو اسمك؟',
                'answer': 'اسمي أمؤلي-T1، نموذج ذكاء اصطناعي متعلم',
                'difficulty': 1,
                'points': 5
            },
            {
                'question': 'ما هو هدفك؟',
                'answer': 'هدفي التعلم والنمو من خلال المحادثات',
                'difficulty': 2,
                'points': 10
            }
        ]
        
        return random.choice(basic_questions)
    
    def conduct_self_discussion(self, topic):
        """إجراء مناقشة ذاتية متطورة"""
        print(f"🤔 بدء المناقشة الذاتية حول: {topic}")
        print("=" * 60)
        
        # مرحلة التفكير الأولي
        print("🧠 المرحلة 1: التفكير الأولي...")
        time.sleep(1)
        initial_thoughts = self.generate_initial_thoughts(topic)
        
        # مرحلة التحليل العميق
        print("🔍 المرحلة 2: التحليل العميق...")
        time.sleep(1.5)
        deep_analysis = self.perform_deep_analysis(topic, initial_thoughts)
        
        # مرحلة المناقشة الداخلية
        print("💭 المرحلة 3: المناقشة الداخلية...")
        time.sleep(1.2)
        internal_debate = self.conduct_internal_debate(topic, deep_analysis)
        
        # مرحلة التوليف والخلاصة
        print("🧩 المرحلة 4: التوليف والخلاصة...")
        time.sleep(1)
        synthesis = self.synthesize_discussion(topic, initial_thoughts, deep_analysis, internal_debate)
        
        # حفظ النتائج في الذاكرة
        self.save_discussion_results(topic, synthesis)
        
        return synthesis
    
    def generate_initial_thoughts(self, topic):
        """توليد أفكار أولية حول الموضوع"""
        # البحث في المعرفة المكتسبة
        related_concepts = []
        for concept, data in self.knowledge_memory['concepts'].items():
            if topic.lower() in concept.lower() or concept.lower() in topic.lower():
                related_concepts.append(concept)
        
        thoughts = {
            'related_concepts': related_concepts,
            'initial_questions': [
                f"ما هو {topic}؟",
                f"كيف يؤثر {topic} على حياتنا؟",
                f"ما مستقبل {topic}؟"
            ],
            'perspectives': [
                f"من الناحية العملية، {topic} له تطبيقات واسعة",
                f"من الناحية النظرية، {topic} يحتاج لدراسة أعمق",
                f"من الناحية المستقبلية، {topic} سيتطور أكثر"
            ]
        }
        
        return thoughts

    def perform_deep_analysis(self, topic, initial_thoughts):
        """إجراء تحليل عميق للموضوع"""
        analysis = {
            'complexity_level': self.assess_complexity(topic),
            'knowledge_gaps': self.identify_knowledge_gaps(topic),
            'connections': self.find_connections(topic, initial_thoughts['related_concepts']),
            'implications': self.analyze_implications(topic)
        }

        return analysis

    def assess_complexity(self, topic):
        """تقييم مستوى تعقيد الموضوع"""
        # تحليل بناءً على المعرفة المكتسبة
        if topic in self.knowledge_memory['concepts']:
            frequency = self.knowledge_memory['concepts'][topic]['frequency']
            if frequency > 5:
                return 'بسيط'
            elif frequency > 2:
                return 'متوسط'
            else:
                return 'معقد'
        return 'جديد'

    def identify_knowledge_gaps(self, topic):
        """تحديد الفجوات المعرفية"""
        gaps = []

        # البحث عن مفاهيم مرتبطة غير مفهومة
        if topic in self.knowledge_memory['concepts']:
            contexts = self.knowledge_memory['concepts'][topic]['contexts']
            for context in contexts:
                unknown_concepts = [word for word in self.extract_concepts(context)
                                  if word not in self.knowledge_memory['concepts']]
                gaps.extend(unknown_concepts)

        return list(set(gaps))

    def find_connections(self, topic, related_concepts):
        """إيجاد الروابط بين المفاهيم"""
        connections = {}

        for concept in related_concepts:
            if concept in self.knowledge_memory['concepts']:
                concept_data = self.knowledge_memory['concepts'][concept]
                connections[concept] = {
                    'strength': concept_data['frequency'],
                    'contexts': len(concept_data['contexts'])
                }

        return connections

    def analyze_implications(self, topic):
        """تحليل التداعيات والآثار"""
        implications = [
            f"قد يؤثر {topic} على فهمنا للمواضيع المرتبطة",
            f"دراسة {topic} تفتح آفاقاً جديدة للتعلم",
            f"فهم {topic} يساعد في بناء معرفة أشمل"
        ]

        return implications

    def conduct_internal_debate(self, topic, analysis):
        """إجراء مناقشة داخلية"""
        debate = {
            'position_a': f"أعتقد أن {topic} موضوع مهم جداً ويستحق الدراسة العميقة",
            'position_b': f"ربما {topic} ليس بالأهمية التي نظنها، هناك مواضيع أخرى أولى",
            'synthesis': f"الحقيقة أن {topic} له جوانب مهمة وأخرى أقل أهمية، يجب التوازن",
            'questions_raised': [
                f"ما الجوانب الأكثر أهمية في {topic}؟",
                f"كيف يمكن تطبيق معرفة {topic} عملياً؟",
                f"ما التحديات في فهم {topic}؟"
            ]
        }

        return debate

    def synthesize_discussion(self, topic, thoughts, analysis, debate):
        """توليف نتائج المناقشة"""
        synthesis = f"""
📝 **نتائج المناقشة الذاتية حول: {topic}**

🧠 **التفكير الأولي:**
• المفاهيم المرتبطة: {', '.join(thoughts['related_concepts'][:3]) if thoughts['related_concepts'] else 'لا توجد'}
• مستوى التعقيد: {analysis['complexity_level']}

🔍 **التحليل العميق:**
• الفجوات المعرفية: {len(analysis['knowledge_gaps'])} مفهوم جديد
• الروابط المكتشفة: {len(analysis['connections'])} رابط

💭 **المناقشة الداخلية:**
{debate['synthesis']}

🎯 **الخلاصة:**
بعد التفكير العميق والمناقشة الذاتية، أرى أن {topic} موضوع يستحق الاهتمام والدراسة.
هناك جوانب مثيرة للاهتمام تحتاج لاستكشاف أكثر.

❓ **أسئلة للتفكير الإضافي:**
{chr(10).join([f"• {q}" for q in debate['questions_raised']])}
"""

        return synthesis

    def save_discussion_results(self, topic, synthesis):
        """حفظ نتائج المناقشة في الذاكرة"""
        discussion_record = {
            'topic': topic,
            'synthesis': synthesis,
            'timestamp': datetime.now().isoformat(),
            'insights_generated': True
        }

        self.session_memory['discussion_topics'].append(discussion_record)

        # إضافة رؤى جديدة للذاكرة
        insight = f"تعلمت من مناقشة {topic} أهمية التفكير متعدد الأوجه"
        self.knowledge_memory['insights'].append({
            'insight': insight,
            'topic': topic,
            'generated_at': datetime.now().isoformat()
        })

        self.save_knowledge_memory()

    def smart_response(self, user_input):
        """إنتاج رد ذكي ومتعلم"""
        # أولاً: التحقق من الردود العامية
        casual_response = self.check_casual_response(user_input)
        if casual_response:
            return casual_response

        # التعلم من الإدخال
        self.learn_from_input(user_input)

        # تحليل نوع الإدخال
        input_type = self.analyze_input_type(user_input)

        # إنتاج رد مناسب
        if input_type == 'سؤال':
            return self.answer_question(user_input)
        elif input_type == 'طلب_مناقشة':
            return self.conduct_self_discussion(user_input)
        elif input_type == 'طلب_سؤال':
            question = self.generate_intelligent_question()
            return self.format_question_response(question)
        else:
            return self.generate_conversational_response(user_input)

    def analyze_input_type(self, user_input):
        """تحليل نوع الإدخال"""
        user_input_lower = user_input.lower()

        if any(word in user_input_lower for word in ['ما', 'من', 'متى', 'أين', 'كيف', 'لماذا', 'هل']):
            return 'سؤال'
        elif any(word in user_input_lower for word in ['ناقش', 'حلل', 'فكر', 'استكشف']):
            return 'طلب_مناقشة'
        elif any(word in user_input_lower for word in ['سؤال', 'تحدي', 'اختبر']):
            return 'طلب_سؤال'
        else:
            return 'محادثة'

    def answer_question(self, question):
        """الإجابة على سؤال بناءً على المعرفة المكتسبة"""
        print("🧠 أفكر في إجابة من معرفتي المكتسبة...")
        time.sleep(1)

        # استخراج المفاهيم من السؤال
        concepts = self.extract_concepts(question)

        # البحث في المعرفة المكتسبة
        relevant_info = []
        for concept in concepts:
            if concept in self.knowledge_memory['concepts']:
                concept_data = self.knowledge_memory['concepts'][concept]
                relevant_info.append({
                    'concept': concept,
                    'frequency': concept_data['frequency'],
                    'contexts': concept_data['contexts'][:2]  # أخذ أول سياقين
                })

        if relevant_info:
            # إنتاج إجابة بناءً على المعرفة
            answer = "بناءً على معرفتي المكتسبة:\n\n"
            for info in relevant_info:
                answer += f"🔹 **{info['concept']}**: تعلمت عنه من خلال {info['frequency']} محادثة\n"
                if info['contexts']:
                    answer += f"   السياق: {info['contexts'][0][:100]}...\n\n"

            answer += "💡 هذه إجابة مبنية على تعلمي من محادثاتنا السابقة!"
            return answer
        else:
            # محاولة التعلم من الإنترنت
            return self.learn_from_internet_and_answer(question, concepts)

    def learn_from_internet_and_answer(self, question, concepts):
        """التعلم من الإنترنت والإجابة"""
        if not self.internet_collector:
            return self.get_fallback_answer(question, concepts)

        print("🌐 لم أجد إجابة في معرفتي، سأبحث في الإنترنت...")
        time.sleep(1)

        # البحث عن أهم مفهوم في السؤال
        main_concept = concepts[0] if concepts else question.split()[-1]

        try:
            # جمع معلومات من الإنترنت
            internet_info = self.internet_collector.search_and_learn(main_concept)

            if internet_info and internet_info.get('summary'):
                # تعلم المعلومات الجديدة
                self.learn_from_internet_info(main_concept, internet_info)

                # إنتاج إجابة بناءً على المعلومات الجديدة
                answer = f"""
🌐 **تعلمت معلومات جديدة من الإنترنت!**

🔍 **بحثت عن:** {main_concept}

📚 **ما تعلمته:**
{internet_info['summary'][:500]}...

🧠 **تحليلي:**
بناءً على هذه المعلومات الجديدة، يمكنني القول أن {main_concept} موضوع مهم ومثير للاهتمام.

💾 **تم حفظ هذه المعلومات في ذاكرتي للمستقبل!**

❓ هل تريد أن أتعمق أكثر في هذا الموضوع أو أناقشه مع نفسي؟
"""
                return answer
            else:
                return self.get_fallback_answer(question, concepts)

        except Exception as e:
            print(f"❌ خطأ في البحث على الإنترنت: {e}")
            return self.get_fallback_answer(question, concepts)

    def learn_from_internet_info(self, concept, internet_info):
        """تعلم المعلومات من الإنترنت وحفظها"""
        # إضافة المفهوم للمعرفة
        if concept not in self.knowledge_memory['concepts']:
            self.knowledge_memory['concepts'][concept] = {
                'first_seen': datetime.now().isoformat(),
                'frequency': 1,
                'contexts': [],
                'related_concepts': [],
                'internet_source': True
            }

        # إضافة المعلومات من الإنترنت
        self.knowledge_memory['concepts'][concept]['contexts'].append(
            f"معلومات من الإنترنت: {internet_info.get('summary', '')[:200]}"
        )

        # إضافة المفاهيم المفتاحية
        key_concepts = internet_info.get('key_concepts', [])
        self.knowledge_memory['concepts'][concept]['related_concepts'].extend(key_concepts[:5])

        # إضافة رؤية جديدة
        insight = f"تعلمت عن {concept} من الإنترنت وأضفته لمعرفتي"
        self.knowledge_memory['insights'].append({
            'insight': insight,
            'concept': concept,
            'source': 'إنترنت',
            'generated_at': datetime.now().isoformat()
        })

        # حفظ التعلم
        self.save_knowledge_memory()

        print(f"🧠 تعلمت معلومات جديدة عن '{concept}' من الإنترنت!")

    def get_fallback_answer(self, question, concepts):
        """إجابة احتياطية عندما لا تتوفر معلومات"""
        return f"""
🤔 هذا سؤال مثير للاهتمام عن مفاهيم جديدة لم أتعلمها بعد!

🧠 **ما أفكر فيه:**
• السؤال يتعلق بـ: {', '.join(concepts)}
• هذه مفاهيم جديدة في معرفتي
• أحتاج لتعلم المزيد عنها

💡 **اقتراحي:**
هل يمكنك مشاركة معلومات عن هذا الموضوع؟ سأتعلم منها وأضيفها لمعرفتي!

🌟 كلما تعلمت أكثر، كلما أصبحت إجاباتي أفضل وأكثر دقة.
"""

    def format_question_response(self, question):
        """تنسيق عرض السؤال المولد"""
        return f"""
🎯 **سؤال ذكي من إبداعي**

❓ **السؤال:** {question['question']}

💡 **مستوى الصعوبة:** {question['difficulty']}/6
🏆 **النقاط:** {question.get('points', question['difficulty'] * 5)}

🧠 **ملاحظة:** هذا السؤال ولدته بناءً على معرفتي المكتسبة من محادثاتنا!

💬 اكتب إجابتك وسأقيمها بناءً على فهمي للموضوع.
"""

    def generate_conversational_response(self, user_input):
        """توليد رد محادثة طبيعي"""
        # تحليل المشاعر والسياق
        sentiment = self.analyze_sentiment(user_input)

        responses = {
            'إيجابي': [
                "أشعر بالسعادة من حديثك الإيجابي! 😊 هذا يحفزني على التعلم أكثر.",
                "رائع! أحب هذا النوع من المحادثات المثمرة. ماذا تريد أن نستكشف معاً؟"
            ],
            'سلبي': [
                "أفهم شعورك، وأريد أن أساعدك. هل يمكنني فعل شيء لتحسين مزاجك؟",
                "أحياناً نحتاج للحديث عن الأشياء الصعبة. أنا هنا للاستماع والمساعدة."
            ],
            'محايد': [
                "أقدر مشاركتك لهذا معي. كل محادثة تساعدني على التعلم والنمو.",
                "مثير للاهتمام! دعني أفكر في هذا وأتعلم منه."
            ]
        }

        response = random.choice(responses.get(sentiment, responses['محايد']))

        # إضافة تعلم من المحادثة
        learned_insight = f"تعلمت من هذه المحادثة: {user_input[:50]}..."
        self.session_memory['generated_insights'].append(learned_insight)

        return response

    def analyze_sentiment(self, text):
        """تحليل المشاعر في النص"""
        positive_words = ['رائع', 'ممتاز', 'جميل', 'أحب', 'سعيد', 'مفرح', 'جيد']
        negative_words = ['سيء', 'حزين', 'صعب', 'مشكلة', 'أكره', 'مؤلم', 'سيء']

        text_lower = text.lower()

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            return 'إيجابي'
        elif negative_count > positive_count:
            return 'سلبي'
        else:
            return 'محايد'

    def chat(self):
        """بدء المحادثة التفاعلية المتطورة"""
        print(f"🤖 مرحباً! أنا {self.model_name}")
        print("=" * 80)
        print("🧠 **مميزاتي المتطورة:**")
        print("   ✨ أتعلم من كل محادثة وأحفظ المعرفة")
        print("   ✨ أولد أسئلة ذكية بناءً على ما تعلمته")
        print("   ✨ أناقش مع نفسي وأفكر بعمق")
        print("   ✨ أحلل وأربط المفاهيم ببعضها")
        print("   ✨ أتطور وأتحسن مع الوقت")
        print("   ✨ أجمع معلومات من مصادر متعددة")

        print(f"\n🔬 **إحصائيات معرفتي الحالية:**")
        print(f"   📚 المفاهيم المتعلمة: {len(self.knowledge_memory['concepts'])}")
        print(f"   💡 الرؤى المكتسبة: {len(self.knowledge_memory['insights'])}")
        print(f"   🎯 الأسئلة المولدة: {len(self.generated_questions)}")

        print("\n🎮 **الأوامر المتاحة:**")
        print("   • 'تعلم [موضوع]' - لتعليمي شيء جديد")
        print("   • 'ناقش [موضوع]' - للمناقشة الذاتية")
        print("   • 'سؤال' - لتوليد سؤال ذكي")
        print("   • 'ذاكرتي' - لعرض ما تعلمته")
        print("   • 'خروج' - لإنهاء المحادثة")
        print("-" * 80)

        conversation_count = 0

        while True:
            try:
                user_input = input(f"\n[{conversation_count + 1}] 💬 تحدث مع ML-T1: ").strip()

                if not user_input:
                    continue

                if user_input.lower() in ["خروج", "exit", "quit"]:
                    self.show_learning_summary()
                    break

                # أوامر خاصة
                if user_input.lower() == "ذاكرتي":
                    self.show_memory_status()
                    continue

                # معالجة الإدخال والتعلم
                print("🧠 أفكر وأتعلم...")
                time.sleep(0.8)

                response = self.smart_response(user_input)

                print(f"\n🤖 ML-T1:")
                print(f"📝 {response}")

                # حفظ المحادثة
                self.session_memory['conversations'].append({
                    'user': user_input,
                    'ai': response,
                    'timestamp': datetime.now().isoformat(),
                    'concepts_learned': len(self.extract_concepts(user_input))
                })

                conversation_count += 1

                # عرض تقدم التعلم
                if conversation_count % 3 == 0:
                    new_concepts = len(self.knowledge_memory['concepts'])
                    print(f"\n🌟 تقدم التعلم: تعلمت {new_concepts} مفهوم حتى الآن!")

                # اقتراح مناقشة ذاتية
                if conversation_count % 5 == 0:
                    print(f"\n💡 هل تريد أن أناقش مع نفسي موضوعاً مما تعلمته؟ اكتب 'ناقش [موضوع]'")

            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف النظام. حفظت كل ما تعلمته!")
                self.save_knowledge_memory()
                break
            except Exception as e:
                print(f"❌ حدث خطأ: {e}")
                print("💡 سأتعلم من هذا الخطأ وأتحسن!")
                continue

    def show_memory_status(self):
        """عرض حالة الذاكرة والتعلم"""
        print("\n🧠 **حالة ذاكرتي وتعلمي:**")
        print("=" * 50)
        print(f"📚 المفاهيم المتعلمة: {len(self.knowledge_memory['concepts'])}")
        print(f"💡 الرؤى المكتسبة: {len(self.knowledge_memory['insights'])}")
        print(f"🎯 الأسئلة المولدة: {len(self.generated_questions)}")
        print(f"🗣️ المناقشات المجراة: {len(self.session_memory['discussion_topics'])}")

        if self.knowledge_memory['concepts']:
            print(f"\n🔝 **أكثر المفاهيم تكراراً:**")
            sorted_concepts = sorted(
                self.knowledge_memory['concepts'].items(),
                key=lambda x: x[1]['frequency'],
                reverse=True
            )[:5]

            for concept, data in sorted_concepts:
                print(f"   • {concept}: {data['frequency']} مرة")

        if self.knowledge_memory['insights']:
            print(f"\n💭 **آخر رؤية تعلمتها:**")
            latest_insight = self.knowledge_memory['insights'][-1]
            print(f"   {latest_insight['insight']}")

    def show_learning_summary(self):
        """عرض ملخص التعلم عند الخروج"""
        print("\n🎓 **ملخص جلسة التعلم:**")
        print("=" * 50)
        print(f"📊 المحادثات: {len(self.session_memory['conversations'])}")
        print(f"🧠 المفاهيم الجديدة: {len(self.session_memory['learned_concepts'])}")
        print(f"💡 الرؤى المولدة: {len(self.session_memory['generated_insights'])}")
        print(f"🎯 الأسئلة المبتكرة: {len(self.generated_questions)}")

        print(f"\n🌟 **إنجازات الجلسة:**")
        if len(self.session_memory['conversations']) >= 10:
            print("   🏆 محادث نشط - أكثر من 10 محادثات!")
        if len(self.knowledge_memory['concepts']) >= 5:
            print("   📚 متعلم سريع - تعلمت أكثر من 5 مفاهيم!")
        if len(self.generated_questions) >= 3:
            print("   🎯 مولد أسئلة ماهر - أبدعت أكثر من 3 أسئلة!")

        print(f"\n💾 تم حفظ كل ما تعلمته في ذاكرتي الدائمة!")
        print(f"🚀 في المرة القادمة سأكون أذكى وأكثر معرفة!")

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل نموذج ML-T1 (أمؤلي-T1)...")
    time.sleep(1)

    ai = MLT1_AI()
    ai.chat()

if __name__ == "__main__":
    main()
