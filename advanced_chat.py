# -*- coding: utf-8 -*-
"""
محادثة متقدمة مع النموذج العربي التفاعلي
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model
import re
import random

# إعدادات النموذج
MODEL_NAME = "interactive-arabic-ai"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
MAX_SEQUENCE_LEN = 20

class ConversationMemory:
    """ذاكرة المحادثة لتتبع السياق"""
    def __init__(self):
        self.conversation_history = []
        self.user_interests = set()
        self.topics_discussed = set()
    
    def add_exchange(self, user_input, ai_response):
        """إضافة تبادل محادثة"""
        self.conversation_history.append({
            'user': user_input,
            'ai': ai_response
        })
        
        # استخراج المواضيع المهمة
        keywords = ['الذكاء', 'الاصطناعي', 'العلم', 'التقنية', 'الصحة', 'التعليم', 'الرياضة']
        for keyword in keywords:
            if keyword in user_input:
                self.user_interests.add(keyword)
                self.topics_discussed.add(keyword)
    
    def get_context(self):
        """الحصول على سياق المحادثة"""
        if len(self.conversation_history) > 0:
            return self.conversation_history[-1]['user']
        return ""
    
    def get_interests(self):
        """الحصول على اهتمامات المستخدم"""
        return list(self.user_interests)

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s\?\!\.]', '', text)
    return text.strip()

def generate_detailed_response(seed_text, model, tokenizer, max_sequence_len, min_words=20, max_words=40):
    """توليد إجابة مفصلة وطويلة"""
    output_text = seed_text
    used_words = []
    
    for i in range(max_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]
        
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        
        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]
            
            # Temperature sampling للتنوع
            temperature = 0.7
            predicted_probs = np.log(predicted_probs + 1e-8) / temperature
            predicted_probs = np.exp(predicted_probs)
            predicted_probs = predicted_probs / np.sum(predicted_probs)
            
            # Top-k sampling
            top_k = min(12, len(predicted_probs))
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]
            
            # اختيار كلمة مناسبة
            selected_word = None
            for idx in sorted_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) >= 2:
                    # تجنب التكرار المفرط
                    recent_words = used_words[-4:] if len(used_words) >= 4 else used_words
                    if candidate_word not in recent_words or i > 15:
                        # تأكد من أن الكلمة عربية
                        if re.match(r'^[\u0600-\u06FF]+$', candidate_word):
                            selected_word = candidate_word
                            break
            
            if not selected_word:
                # استخدم أفضل كلمة متاحة
                for idx in sorted_indices:
                    candidate_word = tokenizer.index_word.get(idx, "")
                    if candidate_word and re.match(r'^[\u0600-\u06FF]+$', candidate_word):
                        selected_word = candidate_word
                        break
            
            if not selected_word:
                break
            
            output_text += " " + selected_word
            used_words.append(selected_word)
            
            # الحفاظ على آخر 6 كلمات فقط
            if len(used_words) > 6:
                used_words = used_words[-6:]
        
        except Exception:
            break
    
    return output_text.strip()

def add_follow_up_question(response, topic):
    """إضافة سؤال متابعة للإجابة"""
    follow_up_questions = {
        'الذكاء الاصطناعي': [
            "هل تريد معرفة المزيد عن تطبيقات الذكاء الاصطناعي في حياتنا؟",
            "ما رأيك في مستقبل الذكاء الاصطناعي؟",
            "هل لديك تجربة مع أدوات الذكاء الاصطناعي؟",
            "أي مجال تعتقد أن الذكاء الاصطناعي سيغيره أكثر؟"
        ],
        'العلم': [
            "أي فرع من العلوم يثير اهتمامك أكثر؟",
            "هل تحب التجارب العلمية؟",
            "ما أكثر اكتشاف علمي أعجبك؟",
            "كيف تطبق العلم في حياتك اليومية؟"
        ],
        'الصحة': [
            "ما هي عاداتك الصحية اليومية؟",
            "هل تمارس الرياضة بانتظام؟",
            "ما نوع الطعام الصحي الذي تفضله؟",
            "كيف تتعامل مع الضغوط النفسية؟"
        ],
        'التعليم': [
            "ما هو مجال دراستك أو اهتمامك؟",
            "كيف تفضل التعلم - بالقراءة أم بالممارسة؟",
            "ما أفضل طريقة تعلم جربتها؟",
            "هل تستخدم التقنية في التعلم؟"
        ],
        'عام': [
            "ما رأيك في هذا الموضوع؟",
            "هل لديك تجربة شخصية مع هذا؟",
            "ما الذي تود معرفته أكثر؟",
            "هل هناك جانب آخر يهمك في هذا الموضوع؟"
        ]
    }
    
    questions = follow_up_questions.get(topic, follow_up_questions['عام'])
    selected_question = random.choice(questions)
    
    return f"{response} {selected_question}"

def analyze_topic(text):
    """تحليل موضوع النص"""
    text_lower = text.lower()
    
    if any(word in text_lower for word in ['الذكاء', 'اصطناعي', 'تقنية', 'حاسوب', 'برمجة']):
        return 'الذكاء الاصطناعي'
    elif any(word in text_lower for word in ['علم', 'رياضيات', 'فيزياء', 'كيمياء', 'أحياء']):
        return 'العلم'
    elif any(word in text_lower for word in ['صحة', 'طب', 'رياضة', 'غذاء', 'نوم']):
        return 'الصحة'
    elif any(word in text_lower for word in ['تعليم', 'دراسة', 'مدرسة', 'جامعة', 'تعلم']):
        return 'التعليم'
    else:
        return 'عام'

def get_contextual_response(user_input, intent, model, tokenizer, max_sequence_len, memory):
    """توليد إجابة سياقية مفصلة"""
    
    # ردود تفاعلية للتحيات
    if intent == "تحية":
        greetings = [
            "وعليكم السلام ورحمة الله وبركاته! أهلاً وسهلاً بك، سعيد جداً بلقائك اليوم. كيف حالك وكيف كان يومك؟",
            "مرحباً بك أهلاً وسهلاً! نورت المكان بحضورك الكريم. أتمنى أن تكون بأفضل حال. ما الذي يمكنني مساعدتك فيه اليوم؟",
            "صباح الخير والنور! أتمنى أن تكون قد بدأت يومك بنشاط وحيوية. هل هناك شيء مثير تخطط له اليوم؟"
        ]
        return random.choice(greetings)
    
    elif intent == "سؤال عن الحال":
        responses = [
            "الحمد لله أنا بخير تماماً وأشعر بالحماس للتعلم والمساعدة! وأنت كيف حالك؟ أتمنى أن تكون بأفضل صحة وأحسن حال. هل هناك شيء جميل حدث معك اليوم؟",
            "تمام الحمد لله، أشعر بالسعادة عندما أتحدث مع أشخاص مثلك! وأنت شلونك؟ أتمنى أن تكون مرتاح ومبسوط. ما الذي يجعلك سعيداً هذه الأيام؟"
        ]
        return random.choice(responses)
    
    elif intent == "سؤال هوية":
        responses = [
            "أنا أمؤلي، نموذج ذكاء اصطناعي عربي مصمم لمساعدتك والتحدث معك باللغة العربية. أحب التعلم والنقاش في مواضيع مختلفة مثل العلوم والتقنية والثقافة. ما الموضوع الذي يثير اهتمامك أكثر؟",
            "اسمي أمؤلي وأنا مساعد ذكي باللغة العربية، أستمتع بالمحادثات المفيدة وأحب مساعدة الناس في التعلم واكتشاف أشياء جديدة. هل تريد أن نتحدث عن موضوع معين يهمك؟"
        ]
        return random.choice(responses)
    
    # للمواضيع الأخرى، استخدم النموذج مع إضافة سؤال متابعة
    else:
        # إضافة سياق من المحادثة السابقة
        context = memory.get_context()
        if context:
            enhanced_input = f"{context} {user_input}"
        else:
            enhanced_input = user_input
        
        # توليد إجابة مفصلة
        response = generate_detailed_response(
            enhanced_input,
            model,
            tokenizer,
            max_sequence_len,
            min_words=20,
            max_words=35
        )
        
        # تحليل الموضوع وإضافة سؤال متابعة
        topic = analyze_topic(user_input)
        response_with_question = add_follow_up_question(response, topic)
        
        return response_with_question

def analyze_intent(text):
    """تحليل نية المستخدم"""
    text_lower = text.lower()
    
    if any(word in text_lower for word in ["السلام عليكم", "مرحبا", "أهلا", "هلا", "صباح", "مساء"]):
        return "تحية"
    elif any(word in text_lower for word in ["كيف حالك", "شلونك", "كيفك", "وش أخبارك"]):
        return "سؤال عن الحال"
    elif any(word in text_lower for word in ["من أنت", "ما اسمك", "مين انت"]):
        return "سؤال هوية"
    elif any(word in text_lower for word in ["شكرا", "شكراً", "تسلم"]):
        return "شكر"
    else:
        return "موضوعي"

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً بك في أمؤلي التفاعلي المتطور!")
    print("=" * 60)
    
    # التحقق من وجود الملفات
    if not os.path.exists(MODEL_FILE):
        print(f"❌ ملف النموذج غير موجود: {MODEL_FILE}")
        print("يرجى تشغيل interactive_arabic_model.py أولاً")
        return
    
    if not os.path.exists(TOKENIZER_FILE):
        print(f"❌ ملف Tokenizer غير موجود: {TOKENIZER_FILE}")
        print("يرجى تشغيل interactive_arabic_model.py أولاً")
        return
    
    # تحميل النموذج
    print("📥 تحميل النموذج التفاعلي...")
    try:
        model = load_model(MODEL_FILE)
        print(f"✅ تم تحميل النموذج: {MODEL_FILE}")

        with open(TOKENIZER_FILE, "rb") as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل Tokenizer: {TOKENIZER_FILE}")

        print(f"🧠 النموذج التفاعلي جاهز مع {model.count_params():,} معامل")
        print(f"📚 حجم القاموس: {len(tokenizer.word_index):,} كلمة")

    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        return
    
    # تهيئة ذاكرة المحادثة
    memory = ConversationMemory()
    
    # حلقة التفاعل
    print("\n🎯 النموذج جاهز للمحادثة التفاعلية!")
    print("💡 مميزات جديدة:")
    print("   ✨ إجابات مفصلة وطويلة")
    print("   ✨ أسئلة متابعة تفاعلية")
    print("   ✨ ذاكرة للمحادثة والسياق")
    print("   ✨ تحليل اهتماماتك الشخصية")
    print("   - كتابة 'خروج' لإنهاء البرنامج")
    print("-" * 60)
    
    conversation_count = 0
    
    while True:
        try:
            user_input = input(f"\n[{conversation_count + 1}] 💬 تحدث معي: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ["خروج", "exit", "quit", "انتهاء"]:
                print("\n👋 كان من دواعي سروري التحدث معك!")
                if memory.get_interests():
                    print(f"🎯 لاحظت اهتمامك بـ: {', '.join(memory.get_interests())}")
                print("أتمنى أن نتحدث مرة أخرى قريباً!")
                break
            
            # تنظيف الإدخال
            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي صالح")
                continue
            
            # تحليل النية
            intent = analyze_intent(clean_user_input)
            print(f"🔍 نوع المحادثة: {intent}")

            # توليد الإجابة التفاعلية
            print("🤔 أفكر في إجابة مفصلة...")

            response = get_contextual_response(
                clean_user_input,
                intent,
                model,
                tokenizer,
                MAX_SEQUENCE_LEN,
                memory
            )

            print(f"\n🤖 أمؤلي:")
            print(f"📝 {response}")
            
            # حفظ المحادثة في الذاكرة
            memory.add_exchange(clean_user_input, response)
            
            # إحصائيات المحادثة
            response_words = len(response.split())
            input_words = len(clean_user_input.split())
            print(f"\n📊 إحصائيات: الإدخال {input_words} كلمة | الإجابة {response_words} كلمة")
            
            # عرض الاهتمامات كل 3 محادثات
            if conversation_count > 0 and conversation_count % 3 == 0:
                interests = memory.get_interests()
                if interests:
                    print(f"🎯 ألاحظ اهتمامك بـ: {', '.join(interests)}")

            conversation_count += 1
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. كان لقاءً ممتعاً!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            print("💡 جرب إعادة صياغة السؤال")
            continue

if __name__ == "__main__":
    main()
