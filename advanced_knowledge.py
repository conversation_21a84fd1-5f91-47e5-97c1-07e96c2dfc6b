# -*- coding: utf-8 -*-
"""
قاعدة المعرفة المتقدمة والمتخصصة
"""

# معلومات متقدمة ومتخصصة
ADVANCED_KNOWLEDGE = {
    
    # ========== العلوم المتقدمة ==========
    "الفيزياء الكمية": {
        "answer": "الفيزياء الكمية أو ميكانيكا الكم هي نظرية فيزيائية ثورية تصف سلوك المادة والطاقة على المستوى الذري ودون الذري! تختلف تماماً عن الفيزياء الكلاسيكية وتحتوي على مفاهيم غريبة مثل التراكب الكمي والتشابك الكمي ومبدأ عدم اليقين لهايزنبرغ. الجسيمات في العالم الكمي يمكن أن تكون في عدة حالات في نفس الوقت! هذه النظرية أدت لاختراعات مذهلة مثل الليزر والترانزستور والحاسوب الكمي. علماء مثل بلانك وأينشتاين وبور وشرودنغر وضعوا أسسها.",
        "related_topics": ["الحاسوب الكمي", "التشابك الكمي", "مبدأ عدم اليقين", "شرودنغر"],
        "questions": [
            "هل تجد الفيزياء الكمية مثيرة أم محيرة؟ ما أغرب مفهوم فيها؟",
            "ما رأيك في إمكانية الحاسوب الكمي في تغيير المستقبل؟",
            "هل سمعت عن تجربة قطة شرودنغر الشهيرة؟"
        ]
    },
    
    "الهندسة الوراثية": {
        "answer": "الهندسة الوراثية هي تقنية متطورة تسمح بتعديل الحمض النووي (DNA) للكائنات الحية! يمكن من خلالها إضافة أو حذف أو تعديل جينات معينة لتحسين خصائص الكائن الحي. تُستخدم في الطب لعلاج الأمراض الوراثية وإنتاج الأدوية، وفي الزراعة لإنتاج محاصيل مقاومة للآفات وأكثر إنتاجية. تقنية CRISPR الحديثة جعلت التعديل الوراثي أسهل وأدق. لكن هناك جدل أخلاقي حول حدود استخدامها، خاصة في تعديل الجينات البشرية. المستقبل قد يشهد علاج أمراض مستعصية وتحسين جودة الحياة.",
        "related_topics": ["CRISPR", "العلاج الجيني", "المحاصيل المعدلة", "الأخلاقيات الطبية"],
        "questions": [
            "ما رأيك في استخدام الهندسة الوراثية لعلاج الأمراض؟",
            "هل تؤيد تعديل الجينات البشرية لتحسين القدرات؟",
            "ما موقفك من المحاصيل المعدلة وراثياً؟"
        ]
    },
    
    # ========== التقنيات المستقبلية ==========
    "الواقع الافتراضي": {
        "answer": "الواقع الافتراضي (VR) تقنية مذهلة تخلق بيئة ثلاثية الأبعاد تفاعلية تماماً! باستخدام نظارات خاصة وأجهزة تحكم، يمكنك الدخول إلى عوالم رقمية والتفاعل معها وكأنها حقيقية. يُستخدم في الألعاب والتعليم والطب والهندسة والسياحة الافتراضية. في الطب، يساعد الجراحين في التدريب والتخطيط للعمليات. في التعليم، يمكن للطلاب زيارة الحضارات القديمة أو استكشاف الفضاء. الواقع المعزز (AR) يدمج العناصر الرقمية مع العالم الحقيقي. المستقبل قد يشهد عوالم افتراضية متكاملة للعمل والتعلم والترفيه.",
        "related_topics": ["الواقع المعزز", "الألعاب الافتراضية", "التعليم الافتراضي", "الميتافيرس"],
        "questions": [
            "هل جربت الواقع الافتراضي؟ ما انطباعك عن التجربة؟",
            "كيف تتخيل استخدام الواقع الافتراضي في التعليم؟",
            "ما رأيك في فكرة الميتافيرس والعوالم الافتراضية؟"
        ]
    },
    
    "إنترنت الأشياء": {
        "answer": "إنترنت الأشياء (IoT) هو شبكة من الأجهزة المترابطة التي تتواصل مع بعضها البعض عبر الإنترنت! من الثلاجة الذكية التي تخبرك بانتهاء الحليب، إلى السيارة التي تحجز موقف سيارات تلقائياً، إلى أجهزة مراقبة الصحة التي ترسل بياناتك للطبيب. يشمل المنازل الذكية والمدن الذكية والمصانع الذكية. يمكن للأجهزة جمع البيانات وتحليلها واتخاذ قرارات ذكية. لكن هناك تحديات في الأمان والخصوصية. المستقبل سيشهد مليارات الأجهزة المترابطة التي ستجعل حياتنا أسهل وأكثر كفاءة.",
        "related_topics": ["المنازل الذكية", "المدن الذكية", "أمن البيانات", "الأجهزة الذكية"],
        "questions": [
            "كم جهاز ذكي تملك في منزلك؟ هل تجدها مفيدة؟",
            "ما رأيك في فكرة المدن الذكية؟ هل تريد العيش في واحدة؟",
            "هل تقلق بشأن خصوصية بياناتك مع انتشار الأجهزة الذكية؟"
        ]
    },
    
    # ========== علوم الفضاء المتقدمة ==========
    "الثقوب السوداء": {
        "answer": "الثقوب السوداء من أغرب وأعجب الظواهر في الكون! هي مناطق في الفضاء بجاذبية قوية جداً لدرجة أن لا شيء يمكنه الهروب منها، حتى الضوء! تتكون عندما تنهار نجوم عملاقة في نهاية حياتها. لها 'أفق الحدث' وهو النقطة التي لا عودة بعدها. الوقت يتباطأ كلما اقتربت من الثقب الأسود! في مركز كل مجرة ثقب أسود عملاق. العالم ستيفن هوكينغ اكتشف أن الثقوب السوداء تشع طاقة وتتبخر ببطء شديد. عام 2019 تم تصوير أول ثقب أسود حقيقي! ما زالت تحتوي على أسرار كثيرة عن طبيعة الزمان والمكان.",
        "related_topics": ["ستيفن هوكينغ", "النسبية العامة", "أفق الحدث", "إشعاع هوكينغ"],
        "questions": [
            "هل تجد الثقوب السوداء مخيفة أم مثيرة للاهتمام؟",
            "ما رأيك في نظريات السفر عبر الزمن باستخدام الثقوب السوداء؟",
            "هل تعتقد أن الثقوب السوداء قد تكون بوابات لأكوان أخرى؟"
        ]
    },
    
    # ========== الطب المتقدم ==========
    "الطب الشخصي": {
        "answer": "الطب الشخصي أو الطب الدقيق هو ثورة في عالم الطب! يعتمد على تحليل الحمض النووي والجينات الشخصية لكل مريض لتقديم علاج مخصص له تماماً. بدلاً من العلاج الموحد للجميع، يتم تصميم الدواء والجرعة والعلاج حسب التركيب الجيني للشخص. يساعد في تحديد الأمراض قبل ظهورها والوقاية منها. يُستخدم بنجاح في علاج السرطان حيث يتم تحليل جينات الورم لاختيار أفضل علاج. الذكاء الاصطناعي يساعد في تحليل البيانات الجينية الضخمة. المستقبل سيشهد أدوية مصممة خصيصاً لكل شخص حسب جيناته.",
        "related_topics": ["الجينوم البشري", "علاج السرطان", "الوقاية الجينية", "الأدوية المخصصة"],
        "questions": [
            "هل تود معرفة تركيبك الجيني والأمراض التي قد تصيبك؟",
            "ما رأيك في فكرة الأدوية المصممة خصيصاً لك؟",
            "هل تعتقد أن الطب الشخصي سيكون متاحاً للجميع أم للأغنياء فقط؟"
        ]
    },
    
    # ========== البيئة والاستدامة ==========
    "الطاقة المتجددة": {
        "answer": "الطاقة المتجددة هي مستقبل كوكب الأرض! تشمل الطاقة الشمسية وطاقة الرياح والطاقة المائية والطاقة الحيوية والطاقة الحرارية الأرضية. هذه المصادر لا تنضب ولا تلوث البيئة مثل الوقود الأحفوري. تكلفة الطاقة الشمسية انخفضت بنسبة 90% في العقد الماضي! دول مثل الدنمارك تحصل على أكثر من 50% من كهربائها من الرياح. السعودية تبني مدينة نيوم بالكامل على الطاقة المتجددة. التحدي الأكبر هو تخزين الطاقة للاستخدام عند عدم وجود شمس أو رياح. بطاريات الليثيوم والهيدروجين الأخضر حلول واعدة.",
        "related_topics": ["الطاقة الشمسية", "طاقة الرياح", "بطاريات الليثيوم", "الهيدروجين الأخضر"],
        "questions": [
            "هل تفكر في تركيب ألواح شمسية في منزلك؟",
            "ما رأيك في مستقبل السيارات الكهربائية؟",
            "أي نوع من الطاقة المتجددة تجده الأكثر إثارة؟"
        ]
    },
    
    # ========== الاقتصاد الرقمي ==========
    "العملات الرقمية": {
        "answer": "العملات الرقمية أو المشفرة ثورة في عالم المال والاقتصاد! البيتكوين كانت أول عملة رقمية عام 2009، واليوم هناك آلاف العملات الرقمية. تعتمد على تقنية البلوك تشين التي تضمن الأمان والشفافية. لا تحتاج لبنوك أو حكومات للتحكم فيها. يمكن إرسال الأموال لأي مكان في العالم خلال دقائق بتكلفة قليلة. لكن أسعارها متقلبة جداً وتستهلك طاقة كبيرة. بعض الدول تبنتها رسمياً مثل السلفادور، وأخرى تحظرها. البنوك المركزية تطور عملاتها الرقمية الخاصة. المستقبل قد يشهد اقتصاداً رقمياً بالكامل.",
        "related_topics": ["البيتكوين", "البلوك تشين", "التمويل اللامركزي", "العملات المركزية الرقمية"],
        "questions": [
            "هل تملك أي عملات رقمية؟ ما رأيك في استثمارها؟",
            "هل تعتقد أن العملات الرقمية ستحل محل النقود التقليدية؟",
            "ما موقفك من تقلبات أسعار العملات الرقمية؟"
        ]
    },
    
    # ========== علم الأعصاب ==========
    "علم الأعصاب": {
        "answer": "علم الأعصاب أو النيوروساينس هو دراسة الجهاز العصبي والدماغ، أعقد عضو في جسم الإنسان! الدماغ البشري يحتوي على 86 مليار خلية عصبية مترابطة بتريليونات الوصلات. يدرس كيف نفكر ونتذكر ونشعر ونتخذ القرارات. التقنيات الحديثة مثل الرنين المغناطيسي تسمح برؤية الدماغ وهو يعمل! اكتشفنا أن الدماغ قابل للتغيير والتطور طوال الحياة (المرونة العصبية). يُستخدم في علاج الاكتئاب والصرع وباركنسون وألزهايمر. واجهات الدماغ-الحاسوب تسمح للمشلولين بالتحكم في الأجهزة بالفكر فقط! ما زال الدماغ يحتوي على أسرار كثيرة.",
        "related_topics": ["المرونة العصبية", "واجهات الدماغ-الحاسوب", "الذاكرة", "الوعي"],
        "questions": [
            "ما أكثر شيء يثير فضولك حول الدماغ البشري؟",
            "هل تمارس تمارين لتقوية الذاكرة والتركيز؟",
            "ما رأيك في إمكانية ربط الدماغ بالحاسوب مستقبلاً؟"
        ]
    }
}

# دمج قواعد المعرفة
def get_all_knowledge():
    """الحصول على جميع قواعد المعرفة"""
    from knowledge_database import COMPREHENSIVE_KNOWLEDGE
    all_knowledge = {}
    all_knowledge.update(COMPREHENSIVE_KNOWLEDGE)
    all_knowledge.update(ADVANCED_KNOWLEDGE)
    return all_knowledge
