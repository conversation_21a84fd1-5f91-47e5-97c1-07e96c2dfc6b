# -*- coding: utf-8 -*-
"""
نظام متقدم للإجابة على الأسئلة المتشابكة والمعقدة
يمكنه التعامل مع آلاف الأسئلة بسهولة
"""

import json
import re
import time
from datetime import datetime
from collections import defaultdict
import threading
import queue

class AdvancedQuestionHandler:
    """معالج الأسئلة المتقدم"""
    
    def __init__(self):
        self.knowledge_base = self.load_knowledge()
        self.question_patterns = self.load_question_patterns()
        self.answer_cache = {}  # تخزين مؤقت للإجابات
        self.processing_queue = queue.Queue()
        
    def load_knowledge(self):
        """تحميل قاعدة المعرفة"""
        try:
            with open('ml_t1_memory.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {'concepts': {}, 'facts': {}, 'insights': []}
    
    def load_question_patterns(self):
        """تحميل أنماط الأسئلة المختلفة"""
        return {
            'تعريف': ['ما هو', 'ما هي', 'عرف', 'اشرح', 'وضح'],
            'سبب': ['لماذا', 'ما سبب', 'ما السبب', 'كيف يحدث'],
            'طريقة': ['كيف', 'بأي طريقة', 'كيفية', 'الطريقة'],
            'مكان': ['أين', 'في أي مكان', 'مكان', 'موقع'],
            'زمان': ['متى', 'في أي وقت', 'زمن', 'تاريخ'],
            'شخص': ['من هو', 'من هي', 'من', 'أي شخص'],
            'مقارنة': ['ما الفرق', 'قارن', 'الفرق بين', 'أيهما أفضل'],
            'رأي': ['ما رأيك', 'هل تعتقد', 'برأيك', 'ما وجهة نظرك'],
            'عدد': ['كم', 'عدد', 'كمية', 'مقدار'],
            'نوع': ['أي نوع', 'ما نوع', 'أنواع', 'تصنيف']
        }
    
    def analyze_question_complexity(self, question):
        """تحليل مستوى تعقيد السؤال"""
        complexity_indicators = {
            'بسيط': 1,
            'متوسط': 2,
            'معقد': 3,
            'متشابك': 4
        }
        
        # عوامل التعقيد
        factors = 0
        
        # طول السؤال
        if len(question.split()) > 20:
            factors += 1
        
        # وجود أسئلة متعددة
        question_marks = question.count('؟')
        if question_marks > 1:
            factors += question_marks
        
        # وجود كلمات ربط معقدة
        complex_connectors = ['بالإضافة إلى', 'من ناحية أخرى', 'علاوة على ذلك', 'في المقابل']
        for connector in complex_connectors:
            if connector in question:
                factors += 1
        
        # وجود مفاهيم متعددة
        concepts = self.extract_concepts_from_question(question)
        if len(concepts) > 3:
            factors += 1
        
        # تحديد مستوى التعقيد
        if factors == 0:
            return 'بسيط'
        elif factors <= 2:
            return 'متوسط'
        elif factors <= 4:
            return 'معقد'
        else:
            return 'متشابك'
    
    def extract_concepts_from_question(self, question):
        """استخراج المفاهيم من السؤال"""
        # إزالة كلمات الاستفهام والوصل
        stop_words = {
            'ما', 'من', 'متى', 'أين', 'كيف', 'لماذا', 'هل', 'أي', 'كم',
            'في', 'على', 'عن', 'مع', 'إلى', 'من', 'هو', 'هي', 'أن', 'كان'
        }

        words = re.findall(r'\b[\u0600-\u06FFa-zA-Z]+\b', question.lower())
        concepts = [word for word in words if len(word) > 2 and word not in stop_words]

        # إضافة مفاهيم مرتبطة بناءً على السياق
        enhanced_concepts = list(set(concepts))

        # إذا كان السؤال عن إنجازات، أضف الاسم المذكور
        if 'إنجازات' in question or 'إنجازاته' in question:
            for concept in concepts:
                if concept in ['خوارزمي', 'الخوارزمي']:
                    enhanced_concepts.extend(['إنجازات', 'الخوارزمي', 'خوارزمي'])

        # إذا كان السؤال عن تأثير، أضف المفاهيم المرتبطة
        if 'أثر' in question or 'تأثير' in question:
            for concept in concepts:
                if concept in ['خوارزمي', 'الخوارزمي']:
                    enhanced_concepts.extend(['تأثير', 'أوروبا', 'الخوارزمي'])

        return list(set(enhanced_concepts))
    
    def break_down_complex_question(self, question):
        """تفكيك السؤال المعقد إلى أسئلة فرعية"""
        # تقسيم بناءً على علامات الاستفهام
        sub_questions = []
        
        # تقسيم بعلامات الاستفهام
        parts = question.split('؟')
        for part in parts:
            if part.strip():
                sub_questions.append(part.strip() + '؟')
        
        # تقسيم بناءً على كلمات الربط
        connectors = ['و', 'أيضاً', 'كذلك', 'بالإضافة', 'علاوة', 'كما']
        
        final_questions = []
        for q in sub_questions:
            # تقسيم إضافي بناءً على كلمات الربط
            for connector in connectors:
                if connector in q:
                    parts = q.split(connector)
                    for part in parts:
                        if part.strip() and len(part.strip()) > 5:
                            final_questions.append(part.strip())
                    break
            else:
                final_questions.append(q)
        
        return [q for q in final_questions if q.strip()]
    
    def find_relevant_knowledge(self, concepts):
        """البحث عن المعرفة ذات الصلة"""
        relevant_info = {
            'concepts': {},
            'facts': {},
            'insights': [],
            'detailed_answers': {}
        }

        # البحث في الإجابات المفصلة أولاً
        detailed_answers = self.knowledge_base.get('detailed_answers', {})
        for question_key, detailed_answer in detailed_answers.items():
            # بحث أكثر ذكاءً
            question_words = question_key.split()
            for concept in concepts:
                concept_words = concept.lower().split()
                # تحقق من التطابق المباشر أو الجزئي
                if (concept.lower() in question_key or
                    any(word in question_key for word in concept_words) or
                    any(concept.lower() in word for word in question_words)):
                    relevant_info['detailed_answers'][question_key] = detailed_answer

        # البحث في المفاهيم
        for concept in concepts:
            for stored_concept, data in self.knowledge_base.get('concepts', {}).items():
                if concept.lower() in stored_concept.lower() or stored_concept.lower() in concept.lower():
                    relevant_info['concepts'][stored_concept] = data

        # البحث في الحقائق المفصلة
        for fact_id, fact_data in self.knowledge_base.get('facts', {}).items():
            if fact_data.get('type') == 'detailed_answer':
                question = fact_data.get('question', '').lower()
                for concept in concepts:
                    if concept.lower() in question or any(word in question for word in concept.lower().split()):
                        relevant_info['facts'][fact_id] = fact_data
                        break
            else:
                # البحث العادي في الحقائق الأخرى
                fact_text = str(fact_data).lower()
                for concept in concepts:
                    if concept.lower() in fact_text:
                        relevant_info['facts'][fact_id] = fact_data
                        break

        # البحث في الرؤى
        for insight in self.knowledge_base.get('insights', []):
            insight_text = str(insight).lower()
            for concept in concepts:
                if concept.lower() in insight_text:
                    relevant_info['insights'].append(insight)
                    break

        return relevant_info
    
    def generate_comprehensive_answer(self, question, relevant_info):
        """توليد إجابة شاملة"""
        answer_parts = []

        # التحقق من وجود إجابات مفصلة
        if relevant_info['detailed_answers']:
            answer_parts.append(f"📖 **إجابة مفصلة على سؤالك:**\n")

            # استخدام أفضل إجابة مفصلة
            best_answer = None
            best_score = 0

            question_words = set(question.lower().split())

            for question_key, detailed_answer in relevant_info['detailed_answers'].items():
                key_words = set(question_key.split())
                score = len(question_words.intersection(key_words))
                if score > best_score:
                    best_score = score
                    best_answer = detailed_answer

            if best_answer:
                answer_parts.append(f"💡 {best_answer}")
                answer_parts.append("")

                # إضافة معلومات إضافية من المفاهيم إذا وجدت
                if relevant_info['concepts']:
                    answer_parts.append("📚 **معلومات إضافية من معرفتي:**")
                    for concept, data in list(relevant_info['concepts'].items())[:2]:
                        frequency = data.get('frequency', 1)
                        answer_parts.append(f"• **{concept}**: ذُكر {frequency} مرة في محادثاتنا")
                    answer_parts.append("")

                return "\n".join(answer_parts)

        # إذا لم توجد إجابات مفصلة، استخدم الطريقة التقليدية
        answer_parts.append(f"🧠 **إجابة شاملة على سؤالك:**\n")

        # البحث في الحقائق المفصلة
        detailed_facts = []
        for fact_id, fact_data in relevant_info['facts'].items():
            if fact_data.get('type') == 'detailed_answer':
                detailed_facts.append(fact_data)

        if detailed_facts:
            answer_parts.append("📝 **من المعلومات المفصلة:**")
            for fact_data in detailed_facts[:2]:
                answer = fact_data.get('answer', '')
                if answer:
                    # عرض أول 300 حرف من الإجابة المفصلة
                    short_answer = answer[:300] + "..." if len(answer) > 300 else answer
                    answer_parts.append(f"• {short_answer}")
            answer_parts.append("")

        # معلومات من المفاهيم
        if relevant_info['concepts']:
            answer_parts.append("📚 **من معرفتي بالمفاهيم:**")
            for concept, data in list(relevant_info['concepts'].items())[:3]:
                frequency = data.get('frequency', 1)
                contexts = data.get('contexts', [])
                answer_parts.append(f"• **{concept}**: تعلمت عنه {frequency} مرة")
                if contexts:
                    answer_parts.append(f"  السياق: {contexts[0][:100]}...")
            answer_parts.append("")

        # رؤى إضافية
        if relevant_info['insights']:
            answer_parts.append("💡 **رؤى إضافية:**")
            for insight in relevant_info['insights'][:2]:
                insight_text = insight.get('insight', str(insight))
                answer_parts.append(f"• {insight_text}")
            answer_parts.append("")

        # إذا لم توجد معلومات كافية
        if not any([relevant_info['detailed_answers'], detailed_facts, relevant_info['concepts']]):
            answer_parts.append("🤔 **لم أجد معلومات مفصلة عن هذا الموضوع في ذاكرتي.**")
            answer_parts.append("")
            answer_parts.append("💡 **اقتراحاتي:**")
            answer_parts.append("• علمني معلومات عن هذا الموضوع")
            answer_parts.append("• أو اطرح سؤالاً أكثر تحديداً")
            answer_parts.append("• أو اطلب مني البحث في الإنترنت")
        else:
            # خلاصة وتحليل
            answer_parts.append("🎯 **خلاصة:**")
            answer_parts.append("هذه المعلومات مبنية على ما تعلمته من مصادر موثوقة.")
            answer_parts.append("")

            # اقتراحات للتعمق
            answer_parts.append("🔍 **للتعمق أكثر:**")
            answer_parts.append("• اطرح أسئلة أكثر تحديداً")
            answer_parts.append("• أو اطلب مناقشة ذاتية حول الموضوع")

        return "\n".join(answer_parts)
    
    def process_complex_question(self, question):
        """معالجة السؤال المعقد"""
        print(f"🔄 معالجة سؤال معقد...")
        print(f"📝 السؤال: {question[:100]}...")
        
        # تحليل مستوى التعقيد
        complexity = self.analyze_question_complexity(question)
        print(f"📊 مستوى التعقيد: {complexity}")
        
        # استخراج المفاهيم
        concepts = self.extract_concepts_from_question(question)
        print(f"🔍 المفاهيم المستخرجة: {len(concepts)} مفهوم")
        
        # تفكيك السؤال إذا كان معقداً
        if complexity in ['معقد', 'متشابك']:
            sub_questions = self.break_down_complex_question(question)
            print(f"🧩 تم تفكيك السؤال إلى {len(sub_questions)} جزء")
        else:
            sub_questions = [question]
        
        # البحث عن المعرفة ذات الصلة
        relevant_info = self.find_relevant_knowledge(concepts)
        
        # توليد الإجابة الشاملة
        answer = self.generate_comprehensive_answer(question, relevant_info)
        
        # حفظ في التخزين المؤقت
        self.answer_cache[question] = {
            'answer': answer,
            'complexity': complexity,
            'concepts': concepts,
            'timestamp': datetime.now().isoformat()
        }
        
        return answer
    
    def handle_multiple_questions(self, questions_list):
        """التعامل مع قائمة من الأسئلة المتعددة"""
        print(f"📋 معالجة {len(questions_list)} سؤال...")
        
        answers = {}
        
        for i, question in enumerate(questions_list, 1):
            print(f"[{i}/{len(questions_list)}] معالجة: {question[:50]}...")
            
            # التحقق من التخزين المؤقت
            if question in self.answer_cache:
                answers[question] = self.answer_cache[question]['answer']
                print(f"✅ تم العثور على إجابة محفوظة")
            else:
                # معالجة السؤال
                answer = self.process_complex_question(question)
                answers[question] = answer
                print(f"✅ تم توليد إجابة جديدة")
            
            # تأخير قصير لتجنب الحمل الزائد
            if i % 10 == 0:
                time.sleep(0.1)
        
        return answers
    
    def generate_summary_report(self, questions_list, answers):
        """توليد تقرير ملخص للأسئلة والإجابات"""
        report = []
        
        report.append("📊 **تقرير شامل للأسئلة والإجابات**")
        report.append("=" * 60)
        report.append(f"📝 إجمالي الأسئلة: {len(questions_list)}")
        report.append(f"✅ الأسئلة المجابة: {len(answers)}")
        report.append(f"📈 معدل النجاح: {(len(answers)/len(questions_list)*100):.1f}%")
        report.append("")
        
        # تصنيف الأسئلة حسب التعقيد
        complexity_count = defaultdict(int)
        for question in questions_list:
            if question in self.answer_cache:
                complexity = self.answer_cache[question]['complexity']
                complexity_count[complexity] += 1
        
        report.append("📊 **توزيع الأسئلة حسب التعقيد:**")
        for complexity, count in complexity_count.items():
            report.append(f"• {complexity}: {count} سؤال")
        report.append("")
        
        # أكثر المفاهيم تكراراً
        all_concepts = []
        for question in questions_list:
            if question in self.answer_cache:
                all_concepts.extend(self.answer_cache[question]['concepts'])
        
        from collections import Counter
        concept_freq = Counter(all_concepts)
        
        report.append("🔝 **أكثر المفاهيم تكراراً:**")
        for concept, freq in concept_freq.most_common(5):
            report.append(f"• {concept}: {freq} مرة")
        
        return "\n".join(report)

def test_advanced_handler():
    """اختبار النظام المتقدم"""
    handler = AdvancedQuestionHandler()
    
    # أسئلة اختبارية معقدة
    test_questions = [
        "ما هو الذكاء الاصطناعي وكيف يعمل وما هي تطبيقاته؟",
        "من هو الخوارزمي وما إنجازاته وكيف أثر على الرياضيات؟",
        "ما الفرق بين البرمجة والذكاء الاصطناعي وأيهما أهم؟",
        "كيف تطورت الحضارة الإسلامية وما دور العلماء فيها؟",
        "ما هي الفيزياء الكمية وكيف تختلف عن الفيزياء التقليدية؟"
    ]
    
    print("🚀 بدء اختبار النظام المتقدم...")
    print("=" * 60)
    
    # معالجة الأسئلة
    answers = handler.handle_multiple_questions(test_questions)
    
    # عرض النتائج
    print("\n📋 **النتائج:**")
    for question, answer in answers.items():
        print(f"\n❓ **السؤال:** {question}")
        print(f"💡 **الإجابة:** {answer[:200]}...")
        print("-" * 40)
    
    # تقرير ملخص
    report = handler.generate_summary_report(test_questions, answers)
    print(f"\n{report}")

if __name__ == "__main__":
    test_advanced_handler()
