# -*- coding: utf-8 -*-
"""
نظام التفكير العميق والمتقدم للذكاء الاصطناعي العربي
"""

import time
import random
import re
from knowledge_database import COMPREHENSIVE_KNOWLEDGE, search_knowledge, get_related_info
from advanced_knowledge import ADVANCED_KNOWLEDGE, get_all_knowledge

class DeepThinkingSystem:
    """نظام التفكير العميق والتحليل المتقدم"""
    
    def __init__(self):
        self.thinking_steps = []
        self.analysis_depth = 3  # مستوى عمق التحليل
        self.knowledge_base = get_all_knowledge()  # استخدام قاعدة المعرفة الموسعة
        self.conversation_context = []
        
    def deep_analyze_question(self, question):
        """تحليل عميق للسؤال"""
        print("🧠 بدء التفكير العميق...")
        time.sleep(1)
        
        analysis = {
            'question_type': self._identify_question_type(question),
            'key_concepts': self._extract_key_concepts(question),
            'complexity_level': self._assess_complexity(question),
            'required_knowledge': self._identify_required_knowledge(question),
            'thinking_approach': self._determine_thinking_approach(question)
        }
        
        print(f"🔍 نوع السؤال: {analysis['question_type']}")
        time.sleep(0.5)
        print(f"🎯 المفاهيم الأساسية: {', '.join(analysis['key_concepts'])}")
        time.sleep(0.5)
        print(f"📊 مستوى التعقيد: {analysis['complexity_level']}")
        time.sleep(0.5)
        
        return analysis
    
    def _identify_question_type(self, question):
        """تحديد نوع السؤال"""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['ما هو', 'ما هي', 'تعريف', 'معنى']):
            return 'تعريفي'
        elif any(word in question_lower for word in ['كيف', 'طريقة', 'كيفية']):
            return 'إجرائي'
        elif any(word in question_lower for word in ['لماذا', 'ليش', 'سبب', 'علة']):
            return 'سببي'
        elif any(word in question_lower for word in ['متى', 'وقت', 'تاريخ']):
            return 'زمني'
        elif any(word in question_lower for word in ['أين', 'وين', 'مكان', 'موقع']):
            return 'مكاني'
        elif any(word in question_lower for word in ['هل', 'أم']) or '؟' in question:
            return 'استفهامي'
        elif any(word in question_lower for word in ['ما رأيك', 'اعتقد', 'تفكر']):
            return 'رأي وتحليل'
        else:
            return 'عام'
    
    def _extract_key_concepts(self, question):
        """استخراج المفاهيم الأساسية"""
        # كلمات مهمة في السؤال
        important_words = []
        words = question.split()
        
        # إزالة كلمات الوقف
        stop_words = {'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'ما', 'هو', 'هي'}
        
        for word in words:
            clean_word = re.sub(r'[^\u0600-\u06FF]', '', word)
            if clean_word and len(clean_word) > 2 and clean_word not in stop_words:
                important_words.append(clean_word)
        
        return important_words[:5]  # أهم 5 مفاهيم
    
    def _assess_complexity(self, question):
        """تقييم مستوى تعقيد السؤال"""
        words_count = len(question.split())
        concepts_count = len(self._extract_key_concepts(question))
        
        if words_count <= 5 and concepts_count <= 2:
            return 'بسيط'
        elif words_count <= 10 and concepts_count <= 4:
            return 'متوسط'
        else:
            return 'معقد'
    
    def _identify_required_knowledge(self, question):
        """تحديد المعرفة المطلوبة"""
        question_lower = question.lower()
        required_domains = []
        
        domain_keywords = {
            'جغرافيا': ['عاصمة', 'دولة', 'مدينة', 'بلد', 'قارة'],
            'تاريخ': ['تاريخ', 'قديم', 'حضارة', 'عصر', 'سنة'],
            'علوم': ['علم', 'فيزياء', 'كيمياء', 'أحياء', 'رياضيات'],
            'تقنية': ['تقنية', 'حاسوب', 'ذكاء اصطناعي', 'برمجة', 'إنترنت'],
            'طب': ['طب', 'صحة', 'مرض', 'علاج', 'دواء'],
            'ثقافة': ['ثقافة', 'أدب', 'فن', 'شعر', 'موسيقى']
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in question_lower for keyword in keywords):
                required_domains.append(domain)
        
        return required_domains if required_domains else ['عام']
    
    def _determine_thinking_approach(self, question):
        """تحديد منهج التفكير المناسب"""
        question_type = self._identify_question_type(question)
        complexity = self._assess_complexity(question)
        
        if question_type == 'تعريفي':
            return 'تحليل مفاهيمي'
        elif question_type == 'سببي':
            return 'تحليل سببي'
        elif question_type == 'إجرائي':
            return 'تحليل خطوات'
        elif complexity == 'معقد':
            return 'تحليل متعدد الأبعاد'
        else:
            return 'تحليل مباشر'
    
    def think_deeply_and_respond(self, question):
        """التفكير العميق وإنتاج الإجابة"""
        print("🤔 أحلل السؤال بعمق...")
        time.sleep(1)
        
        # 1. تحليل السؤال
        analysis = self.deep_analyze_question(question)
        
        # 2. البحث في قاعدة المعرفة
        print("📚 أبحث في قاعدة المعرفة...")
        time.sleep(1)
        search_results = search_knowledge(question, self.knowledge_base)
        
        # 3. التفكير متعدد المستويات
        print("🧩 أربط المعلومات وأحلل العلاقات...")
        time.sleep(1.5)
        
        # 4. بناء الإجابة
        print("✍️ أصيغ إجابة شاملة ومفيدة...")
        time.sleep(1)
        
        if search_results:
            # إجابة مبنية على المعرفة
            best_match = search_results[0]
            key, data, match_type = best_match
            
            base_answer = data['answer']
            related_topics = data.get('related_topics', [])
            questions = data.get('questions', [])
            
            # إثراء الإجابة بمعلومات إضافية
            enhanced_answer = self._enhance_answer(base_answer, related_topics, analysis)
            
            # اختيار سؤال متابعة ذكي
            follow_up = self._select_smart_follow_up(questions, analysis)
            
            return f"{enhanced_answer}\n\n🤔 {follow_up}"
        
        else:
            # إجابة تحليلية عامة
            return self._generate_analytical_response(question, analysis)
    
    def _enhance_answer(self, base_answer, related_topics, analysis):
        """تعزيز الإجابة بمعلومات إضافية"""
        enhanced = base_answer
        
        # إضافة سياق إضافي حسب نوع السؤال
        if analysis['question_type'] == 'تعريفي':
            enhanced += f" هذا الموضوع مرتبط بمجالات مثل {', '.join(related_topics[:3])}."
        
        elif analysis['question_type'] == 'سببي':
            enhanced += " هناك عوامل متعددة تؤثر على هذا الموضوع ويمكن تحليلها من زوايا مختلفة."
        
        elif analysis['complexity_level'] == 'معقد':
            enhanced += " هذا موضوع متعدد الأبعاد يتطلب فهماً عميقاً للعلاقات المترابطة."
        
        return enhanced
    
    def _select_smart_follow_up(self, questions, analysis):
        """اختيار سؤال متابعة ذكي"""
        if not questions:
            return "هل تريد معرفة المزيد عن هذا الموضوع الشيق؟"
        
        # اختيار سؤال حسب نوع التحليل
        if analysis['question_type'] == 'تعريفي':
            # أسئلة تطبيقية
            suitable_questions = [q for q in questions if any(word in q.lower() for word in ['تطبيق', 'استخدام', 'مثال'])]
        elif analysis['question_type'] == 'سببي':
            # أسئلة تحليلية
            suitable_questions = [q for q in questions if any(word in q.lower() for word in ['تأثير', 'نتيجة', 'علاقة'])]
        else:
            suitable_questions = questions
        
        return random.choice(suitable_questions if suitable_questions else questions)
    
    def _generate_analytical_response(self, question, analysis):
        """توليد إجابة تحليلية للأسئلة العامة"""
        thinking_responses = {
            'تعريفي': [
                "هذا مفهوم مثير للاهتمام! دعني أفكر فيه من زوايا متعددة. المفهوم له أبعاد نظرية وتطبيقية مختلفة.",
                "سؤال عميق يتطلب تحليلاً شاملاً! هذا الموضوع له جذور تاريخية وتطبيقات حديثة متنوعة."
            ],
            'سببي': [
                "سؤال ممتاز يتطلب تحليل الأسباب والنتائج! هناك عوامل متعددة تتفاعل معاً لتشكل الصورة الكاملة.",
                "هذا النوع من الأسئلة يحتاج لفهم العلاقات السببية المعقدة. دعني أحلل العوامل المؤثرة."
            ],
            'إجرائي': [
                "سؤال عملي رائع! الإجابة تتطلب تفكيراً منهجياً وتحليل الخطوات اللازمة.",
                "موضوع يحتاج لنهج عملي ومنطقي. هناك طرق متعددة يمكن استكشافها."
            ],
            'رأي وتحليل': [
                "سؤال فكري عميق! هذا يتطلب تحليلاً متوازناً ونظرة شاملة للموضوع من جوانب متعددة.",
                "موضوع يستحق النقاش والتأمل! هناك وجهات نظر مختلفة يمكن استكشافها."
            ]
        }
        
        question_type = analysis['question_type']
        base_responses = thinking_responses.get(question_type, thinking_responses['رأي وتحليل'])
        
        response = random.choice(base_responses)
        
        # إضافة تحليل للمفاهيم الأساسية
        key_concepts = analysis['key_concepts']
        if key_concepts:
            response += f" المفاهيم الأساسية في سؤالك ({', '.join(key_concepts)}) تفتح مجالات واسعة للاستكشاف."
        
        # إضافة سؤال متابعة
        follow_up_questions = [
            "ما الجانب الذي يثير اهتمامك أكثر في هذا الموضوع؟",
            "هل لديك تجربة شخصية مرتبطة بهذا الموضوع؟",
            "أي زاوية تريد أن نتعمق فيها أكثر؟",
            "هل تريد أن نناقش التطبيقات العملية لهذا الموضوع؟"
        ]
        
        response += f"\n\n🤔 {random.choice(follow_up_questions)}"
        
        return response
    
    def add_context(self, user_input, ai_response):
        """إضافة سياق للمحادثة"""
        self.conversation_context.append({
            'user': user_input,
            'ai': ai_response,
            'timestamp': time.time()
        })
        
        # الاحتفاظ بآخر 5 تبادلات فقط
        if len(self.conversation_context) > 5:
            self.conversation_context = self.conversation_context[-5:]
    
    def get_contextual_insight(self):
        """الحصول على رؤية سياقية من المحادثة"""
        if not self.conversation_context:
            return ""
        
        # تحليل المواضيع المتكررة
        topics = []
        for exchange in self.conversation_context:
            user_concepts = self._extract_key_concepts(exchange['user'])
            topics.extend(user_concepts)
        
        # العثور على المواضيع الأكثر تكراراً
        topic_counts = {}
        for topic in topics:
            topic_counts[topic] = topic_counts.get(topic, 0) + 1
        
        if topic_counts:
            most_common = max(topic_counts, key=topic_counts.get)
            return f"ألاحظ اهتمامك المتكرر بموضوع '{most_common}'"
        
        return ""
