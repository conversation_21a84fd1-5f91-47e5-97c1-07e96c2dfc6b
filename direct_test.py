# -*- coding: utf-8 -*-
"""
اختبار مباشر للنموذج
"""

import re
import random

# قاعدة معرفة مبسطة
knowledge_base = {
    "عاصمة العراق": "عاصمة جمهورية العراق هي بغداد العريقة، مدينة السلام ودار السلام كما كانت تُسمى قديماً. تقع على ضفاف نهر دجلة وهي أكبر مدن العراق وأهمها.",
    "عاصمة السعودية": "عاصمة المملكة العربية السعودية هي مدينة الرياض الجميلة، وهي أكبر مدن المملكة ومركزها السياسي والاقتصادي.",
    "عاصمة مصر": "عاصمة جمهورية مصر العربية هي القاهرة العريقة، والتي تُلقب بأم الدنيا لعراقتها وأهميتها التاريخية.",
    "الذكاء الاصطناعي": "الذكاء الاصطناعي هو تقنية متطورة تحاكي قدرات العقل البشري في التفكير والتعلم واتخاذ القرارات الذكية."
}

def get_response(user_input):
    """الحصول على إجابة"""
    user_input_lower = user_input.lower()
    
    # البحث في قاعدة المعرفة
    for key, answer in knowledge_base.items():
        key_words = key.lower().split()
        if all(word in user_input_lower for word in key_words):
            return f"{answer}\n\n🤔 هل تريد معرفة المزيد عن هذا الموضوع؟"
    
    # إجابة افتراضية
    return "هذا سؤال مثير! أحب هذا النوع من الأسئلة. هل يمكنك إعادة صياغة السؤال؟"

# اختبار
print("🤖 مرحباً! أنا أمؤلي - مساعدك الذكي")
print("=" * 50)

test_questions = [
    "ما عاصمة العراق؟",
    "ما عاصمة السعودية؟", 
    "ما هو الذكاء الاصطناعي؟",
    "مرحبا كيف حالك؟"
]

for question in test_questions:
    print(f"\n❓ السؤال: {question}")
    response = get_response(question)
    print(f"🤖 الإجابة: {response}")
    print("-" * 50)

print("\n✅ انتهى الاختبار!")
