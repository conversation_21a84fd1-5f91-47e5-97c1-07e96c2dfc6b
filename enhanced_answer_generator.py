# -*- coding: utf-8 -*-
"""
مولد إجابات محسن - يجعل الإجابات أكثر تفاعلاً وإثارة
"""

import random
import re
from datetime import datetime

class EnhancedAnswerGenerator:
    """مولد الإجابات المحسن"""
    
    def __init__(self):
        self.enthusiasm_phrases = [
            "🌟 هذا سؤال رائع!",
            "💡 أحب هذا النوع من الأسئلة!",
            "🔥 سؤال مثير للاهتمام!",
            "✨ دعني أشاركك شيئاً مذهلاً!",
            "🎯 هذا موضوع شيق جداً!",
            "🚀 استعد لمعلومات مذهلة!"
        ]
        
        self.connection_phrases = [
            "تخيل أن",
            "هل تعلم أن",
            "الأمر المدهش أن",
            "ما يثير الإعجاب أن",
            "الشيء الرائع أن",
            "من المذهل أن"
        ]
        
        self.modern_connections = {
            'الخوارزمي': [
                "خوارزميات جوجل وفيسبوك اليوم مبنية على أسسه!",
                "كل مرة تستخدم GPS في هاتفك، تستخدم رياضيات الخوارزمي!",
                "الذكاء الاصطناعي اليوم يعتمد على الخوارزميات التي أسسها!",
                "حتى Bitcoin يستخدم خوارزميات مشتقة من عمله!"
            ],
            'الجبر': [
                "الجبر اليوم يشغل كل شيء من الألعاب إلى الطيران!",
                "مهندسو Tesla يستخدمون الجبر لتصميم السيارات الكهربائية!",
                "حتى Instagram يستخدم الجبر لفلترة الصور!"
            ],
            'الأرقام العربية': [
                "بدون الأرقام العربية، لما كان عندنا حاسوب أو إنترنت!",
                "كل رقم تكتبه في واتساب هو بفضل الخوارزمي!",
                "حتى ساعة آبل تعرض الأرقام العربية!"
            ]
        }
        
        self.interactive_questions = [
            "هل تريد أن تعرف كيف غيّر هذا حياتنا اليوم؟",
            "تخيل لو لم يكن موجوداً، كيف كان سيكون العالم؟",
            "هل تعرف أي تطبيق حديث يستخدم هذا؟",
            "ما رأيك في هذا التأثير على عصرنا؟",
            "هل تريد أن نتعمق أكثر في هذا الموضوع؟",
            "أي جانب يثير فضولك أكثر؟"
        ]
        
        self.storytelling_starters = [
            "دعني أحكي لك قصة مذهلة...",
            "تخيل معي هذا المشهد...",
            "في يوم من الأيام في بغداد القديمة...",
            "كان هناك رجل عبقري في القرن التاسع...",
            "القصة تبدأ في بيت الحكمة ببغداد..."
        ]

    def enhance_answer(self, basic_answer, topic, question):
        """تحسين الإجابة الأساسية"""
        enhanced_parts = []
        
        # بداية متحمسة
        enthusiasm = random.choice(self.enthusiasm_phrases)
        enhanced_parts.append(f"{enthusiasm}\n")
        
        # إضافة عنصر القصة أحياناً
        if random.random() < 0.3:  # 30% احتمال
            story_starter = random.choice(self.storytelling_starters)
            enhanced_parts.append(f"📖 {story_starter}\n")
        
        # الإجابة الأساسية مع تحسينات
        enhanced_basic = self.add_personality_to_answer(basic_answer)
        enhanced_parts.append(enhanced_basic)
        
        # ربط بالعصر الحديث
        modern_connection = self.get_modern_connection(topic)
        if modern_connection:
            connection_phrase = random.choice(self.connection_phrases)
            enhanced_parts.append(f"\n🌐 **الربط بعصرنا:**")
            enhanced_parts.append(f"{connection_phrase} {modern_connection}")
        
        # أمثلة عملية
        practical_examples = self.get_practical_examples(topic)
        if practical_examples:
            enhanced_parts.append(f"\n🔧 **أمثلة من حياتنا اليومية:**")
            enhanced_parts.extend(practical_examples)
        
        # سؤال تفاعلي
        interactive_q = random.choice(self.interactive_questions)
        enhanced_parts.append(f"\n❓ {interactive_q}")
        
        # دعوة للاستكشاف
        enhanced_parts.append(f"\n🚀 **هل تريد أن نستكشف أكثر؟** اطرح سؤالاً أكثر تحديداً أو قل 'ناقش {topic}' للمناقشة العميقة!")
        
        return "\n".join(enhanced_parts)
    
    def add_personality_to_answer(self, answer):
        """إضافة شخصية للإجابة"""
        # إضافة تعبيرات حماسية
        answer = answer.replace("💡", "💡✨")
        
        # إضافة تعليقات شخصية
        personal_comments = [
            " (مذهل، أليس كذلك؟)",
            " (هذا يثير إعجابي دائماً!)",
            " (فكر في هذا للحظة!)",
            " (أليس هذا رائعاً؟)",
            " (هذا يجعلني متحمساً للتعلم أكثر!)"
        ]
        
        # إضافة تعليق شخصي عشوائي
        if random.random() < 0.4:  # 40% احتمال
            lines = answer.split('\n')
            if len(lines) > 3:
                random_line_index = random.randint(1, min(3, len(lines)-1))
                comment = random.choice(personal_comments)
                lines[random_line_index] += comment
                answer = '\n'.join(lines)
        
        return answer
    
    def get_modern_connection(self, topic):
        """الحصول على ربط بالعصر الحديث"""
        topic_lower = topic.lower()
        
        for key, connections in self.modern_connections.items():
            if key in topic_lower:
                return random.choice(connections)
        
        # ربط عام
        general_connections = [
            "هذا المفهوم يؤثر على تقنيات اليوم بطرق لا تتخيلها!",
            "شركات التقنية الكبرى تستفيد من هذا يومياً!",
            "هذا جزء من الأساس الذي بُني عليه عالمنا الرقمي!",
            "بدون هذا، لما كان عندنا الإنترنت كما نعرفه اليوم!"
        ]
        
        return random.choice(general_connections)
    
    def get_practical_examples(self, topic):
        """الحصول على أمثلة عملية"""
        topic_lower = topic.lower()
        
        examples = []
        
        if 'خوارزمي' in topic_lower:
            examples = [
                "• عندما تبحث في جوجل، تستخدم خوارزميات مطورة من أعماله",
                "• GPS في سيارتك يعتمد على حسابات جغرافية أسسها",
                "• حتى الآلة الحاسبة في هاتفك تستخدم نظام الأرقام الذي طوره",
                "• ألعاب الفيديو تستخدم خوارزميات الرسوم المبنية على أسسه"
            ]
        elif 'ذكاء اصطناعي' in topic_lower:
            examples = [
                "• Siri وGoogle Assistant يستخدمان تقنيات التعلم الآلي",
                "• نتفليكس يقترح عليك أفلام بناءً على خوارزميات ذكية",
                "• السيارات ذاتية القيادة تعتمد على الرؤية الحاسوبية",
                "• حتى فلاتر إنستغرام تستخدم الذكاء الاصطناعي!"
            ]
        elif 'حضارة إسلامية' in topic_lower:
            examples = [
                "• المستشفيات الحديثة مبنية على نموذج البيمارستان الإسلامي",
                "• الجامعات اليوم تتبع نظام بيت الحكمة في بغداد",
                "• الطب الحديث يستخدم أدوات جراحية طورها الرازي",
                "• حتى القهوة التي نشربها وصلت لأوروبا عبر العالم الإسلامي!"
            ]
        
        return examples[:3]  # أول 3 أمثلة
    
    def add_emotional_intelligence(self, answer, user_context=None):
        """إضافة ذكاء عاطفي للإجابة"""
        # إضافة تعاطف وفهم
        empathy_phrases = [
            "أفهم فضولك حول هذا الموضوع",
            "يسعدني أن أشارك معك هذه المعرفة",
            "أشعر بحماسك للتعلم",
            "أقدر اهتمامك بهذا الموضوع المهم"
        ]
        
        # إضافة تشجيع
        encouragement_phrases = [
            "استمر في طرح الأسئلة الرائعة!",
            "فضولك يلهمني للتعلم أكثر!",
            "أسئلتك تجعلني أفكر بعمق أكبر!",
            "معاً نستكشف عوالم المعرفة!"
        ]
        
        if random.random() < 0.3:  # 30% احتمال
            empathy = random.choice(empathy_phrases)
            answer = f"😊 {empathy}\n\n{answer}"
        
        if random.random() < 0.2:  # 20% احتمال
            encouragement = random.choice(encouragement_phrases)
            answer += f"\n\n💪 {encouragement}"
        
        return answer
    
    def create_engaging_response(self, basic_info, topic, question, user_context=None):
        """إنشاء رد جذاب ومتفاعل"""
        # تحسين الإجابة الأساسية
        enhanced = self.enhance_answer(basic_info, topic, question)
        
        # إضافة ذكاء عاطفي
        final_answer = self.add_emotional_intelligence(enhanced, user_context)
        
        # إضافة دعوة للعمل
        call_to_action = self.get_call_to_action(topic)
        if call_to_action:
            final_answer += f"\n\n{call_to_action}"
        
        return final_answer
    
    def get_call_to_action(self, topic):
        """الحصول على دعوة للعمل"""
        actions = [
            "🎯 **تحدي:** ابحث عن تطبيق واحد في هاتفك يستخدم هذا المفهوم!",
            "🔍 **مهمة:** اسأل صديقاً عن رأيه في هذا الموضوع!",
            "💡 **فكرة:** تخيل كيف يمكن تطوير هذا المفهوم في المستقبل!",
            "📚 **اقتراح:** ابحث عن فيديو يوتيوب يشرح هذا الموضوع بصرياً!",
            "🌟 **تجربة:** شارك هذه المعلومة مع شخص يهتم بالعلوم!"
        ]
        
        return random.choice(actions) if random.random() < 0.4 else None

def test_enhanced_generator():
    """اختبار المولد المحسن"""
    generator = EnhancedAnswerGenerator()
    
    basic_answer = """💡 الخوارزمي هو محمد بن موسى الخوارزمي (780-850م)، عالم رياضيات وفلك وجغرافيا مسلم عاش في بغداد خلال العصر العباسي. يُعتبر من أعظم علماء الرياضيات في التاريخ وأبو علم الجبر."""
    
    enhanced = generator.create_engaging_response(
        basic_answer, 
        "الخوارزمي", 
        "من هو الخوارزمي؟"
    )
    
    print("🧪 **اختبار المولد المحسن:**")
    print("=" * 60)
    print(enhanced)

if __name__ == "__main__":
    test_enhanced_generator()
