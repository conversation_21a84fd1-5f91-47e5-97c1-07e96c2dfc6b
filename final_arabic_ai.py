# -*- coding: utf-8 -*-
"""
النموذج النهائي للذكاء الاصطناعي العربي التفاعلي
"""

import re
import random

class ArabicAI:
    """نظام ذكاء اصطناعي عربي متطور وتفاعلي"""
    
    def __init__(self):
        self.conversation_memory = []
        self.user_interests = set()
        self.knowledge_base = self.create_comprehensive_knowledge()
        
    def create_comprehensive_knowledge(self):
        """قاعدة معرفة شاملة ومفصلة"""
        return {
            # تقنية وذكاء اصطناعي
            "الذكاء الاصطناعي": {
                "answer": "الذكاء الاصطناعي هو تقنية متطورة جداً تحاكي قدرات العقل البشري في التفكير والتعلم واتخاذ القرارات الذكية. يستخدم خوارزميات معقدة وشبكات عصبية لتحليل البيانات الضخمة والتعلم منها. له تطبيقات واسعة في الطب لتشخيص الأمراض، وفي التعليم لتخصيص المناهج، وفي النقل للسيارات الذاتية القيادة، وفي الأمن لحماية البيانات.",
                "questions": [
                    "هل تريد معرفة كيف يؤثر الذكاء الاصطناعي على مستقبل الوظائف؟",
                    "ما رأيك في استخدام الذكاء الاصطناعي في التعليم؟ هل جربت أدوات تعليمية ذكية؟",
                    "هل تعتقد أن الذكاء الاصطناعي سيحل محل البشر في بعض المهام أم سيساعدهم؟",
                    "أي تطبيق للذكاء الاصطناعي تجده الأكثر إثارة - الطب أم النقل أم التعليم؟"
                ]
            },
            
            "البرمجة": {
                "answer": "البرمجة هي فن وعلم كتابة التعليمات للحاسوب لحل المشاكل وإنجاز المهام المختلفة. تعتبر البرمجة لغة العصر الحديث ومهارة أساسية في سوق العمل. تساعد في تطوير التطبيقات والمواقع والألعاب والأنظمة الذكية. تنمي التفكير المنطقي وحل المشاكل بطريقة إبداعية ومنهجية.",
                "questions": [
                    "هل تعرف أي لغة برمجة؟ أم تفكر في تعلم البرمجة؟",
                    "ما نوع التطبيقات التي تود تطويرها لو تعلمت البرمجة؟",
                    "هل تعتقد أن البرمجة صعبة أم يمكن لأي شخص تعلمها؟"
                ]
            },
            
            # جغرافيا ودول
            "عاصمة السعودية": {
                "answer": "عاصمة المملكة العربية السعودية هي مدينة الرياض الجميلة، وهي أكبر مدن المملكة ومركزها السياسي والاقتصادي والإداري. تقع في وسط شبه الجزيرة العربية وتضم أكثر من 7 مليون نسمة. تشتهر بناطحات السحاب الحديثة مثل برج المملكة وبرج الفيصلية، وتضم العديد من الجامعات المرموقة والمراكز التجارية الضخمة.",
                "questions": [
                    "هل زرت الرياض من قبل؟ أم تخطط لزيارتها قريباً؟",
                    "ما الذي تعرفه عن مشروع نيوم أو القدية في السعودية؟",
                    "هل تريد معرفة المزيد عن الثقافة السعودية أم المعالم السياحية؟"
                ]
            },
            
            "عاصمة مصر": {
                "answer": "عاصمة جمهورية مصر العربية هي القاهرة العريقة، والتي تُلقب بأم الدنيا لعراقتها وأهميتها التاريخية. تقع على ضفاف نهر النيل المبارك وتضم أكثر من 20 مليون نسمة. تشتهر بالأهرامات وأبو الهول والمتحف المصري والأزهر الشريف. تعتبر مركزاً ثقافياً وإعلامياً مهماً في العالم العربي.",
                "questions": [
                    "هل زرت الأهرامات من قبل؟ ما انطباعك عن هذا المعلم العظيم؟",
                    "ما الذي تعرفه عن الحضارة الفرعونية القديمة؟",
                    "هل تحب الأفلام والمسلسلات المصرية؟"
                ]
            },
            
            # صحة ورياضة
            "الصحة": {
                "answer": "الصحة هي أغلى ما يملكه الإنسان وهي حالة من اكتمال السلامة البدنية والعقلية والاجتماعية وليس مجرد غياب المرض. للمحافظة على الصحة يجب ممارسة الرياضة بانتظام لمدة 30 دقيقة يومياً، وتناول الغذاء الصحي المتوازن الغني بالخضار والفواكه، والنوم 7-8 ساعات يومياً، وشرب الماء بكثرة، وتجنب التدخين والضغوط النفسية.",
                "questions": [
                    "ما هي عاداتك الصحية اليومية؟ هل تمارس الرياضة بانتظام؟",
                    "ما نوع الطعام الصحي الذي تفضله؟ هل تطبخ في البيت؟",
                    "كيف تتعامل مع الضغوط النفسية والتوتر في حياتك؟",
                    "كم ساعة تنام عادة؟ هل تشعر بالراحة عند الاستيقاظ؟"
                ]
            },
            
            "الرياضة": {
                "answer": "الرياضة هي نشاط بدني منظم ومفيد جداً للجسم والعقل والروح. تساعد في تقوية العضلات وتحسين الدورة الدموية وزيادة اللياقة البدنية. كما تقلل من التوتر والقلق وتحسن المزاج وتزيد الثقة بالنفس. تساعد في الوقاية من أمراض القلب والسكري والسمنة. يُنصح بممارسة الرياضة 3-5 مرات أسبوعياً.",
                "questions": [
                    "أي نوع من الرياضة تفضل - كرة القدم أم السباحة أم الجري؟",
                    "هل تمارس الرياضة في النادي أم في البيت أم في الهواء الطلق؟",
                    "ما هو هدفك من ممارسة الرياضة - اللياقة أم إنقاص الوزن أم المتعة؟"
                ]
            },
            
            # تعليم ومعرفة
            "التعليم": {
                "answer": "التعليم هو أساس التقدم والحضارة وحق أساسي لكل إنسان. يساعد في بناء شخصية الإنسان وتطوير قدراته العقلية والإبداعية. التعليم الجيد يفتح أبواب الفرص ويساعد في تحقيق الأحلام والطموحات. في العصر الحديث، أصبح التعليم الرقمي والتعلم عن بُعد جزءاً مهماً من منظومة التعليم، مما يتيح التعلم في أي وقت ومن أي مكان.",
                "questions": [
                    "ما هو مجال دراستك أو تخصصك؟ هل تحب ما تدرسه؟",
                    "كيف تفضل التعلم - بالقراءة أم بمشاهدة الفيديوهات أم بالممارسة العملية؟",
                    "هل تستخدم التطبيقات التعليمية أو المنصات الرقمية في التعلم؟",
                    "ما هو أكثر شيء تود تعلمه في المستقبل القريب؟"
                ]
            },
            
            "العلم": {
                "answer": "العلم هو المعرفة المنظمة والموثقة التي نحصل عليها من خلال الملاحظة والتجريب والبحث المنهجي. العلم يساعدنا على فهم العالم من حولنا وتفسير الظواهر الطبيعية وحل المشاكل المعقدة. بفضل العلم تطورت الطب والتقنية والفضاء والاتصالات. العلم يغير حياتنا للأفضل ويفتح آفاقاً جديدة للمستقبل.",
                "questions": [
                    "أي فرع من العلوم يثير اهتمامك أكثر - الفيزياء أم الكيمياء أم الأحياء؟",
                    "هل تحب التجارب العلمية؟ هل جربت عمل تجارب في البيت؟",
                    "ما أكثر اكتشاف علمي أعجبك وأثر عليك؟"
                ]
            }
        }
    
    def get_smart_response(self, user_input):
        """الحصول على إجابة ذكية ومفصلة"""
        user_input_lower = user_input.lower()
        
        # البحث في قاعدة المعرفة
        for key, data in self.knowledge_base.items():
            if any(word in user_input_lower for word in key.lower().split()):
                self.user_interests.add(key)
                answer = data["answer"]
                question = random.choice(data["questions"])
                return f"{answer}\n\n🤔 {question}"
        
        # إجابات للتحيات
        if any(word in user_input_lower for word in ["مرحبا", "أهلا", "السلام عليكم", "صباح", "مساء", "هلا"]):
            greetings = [
                "أهلاً وسهلاً بك! سعيد جداً بلقائك اليوم. أنا أمؤلي، مساعدك الذكي باللغة العربية. كيف يمكنني مساعدتك؟ هل لديك سؤال معين أم تريد أن نتحدث عن موضوع يهمك مثل التقنية أو الصحة أو التعليم؟",
                "مرحباً بك! نورت المكان بحضورك الكريم. أتمنى أن تكون بأفضل حال وأحسن صحة. أنا هنا لمساعدتك والإجابة على أسئلتك. ما الموضوع الذي تود مناقشته معي اليوم؟",
                "وعليكم السلام ورحمة الله وبركاته! أهلاً وسهلاً بك أخي الكريم. كيف حالك وكيف كان يومك؟ أتمنى أن يكون يوماً مليئاً بالخير والبركة. هل هناك شيء مثير أو جديد حدث معك تريد أن تشاركه معي؟"
            ]
            return random.choice(greetings)
        
        # إجابات لأسئلة الحال
        if any(word in user_input_lower for word in ["كيف حالك", "شلونك", "كيفك", "وش أخبارك", "إيش أخبارك"]):
            responses = [
                "الحمد لله أنا بخير تماماً وأشعر بالحماس الشديد للتعلم والمساعدة! كل يوم أتعلم أشياء جديدة من المحادثات مع أشخاص رائعين مثلك. وأنت كيف حالك؟ أتمنى أن تكون بأفضل صحة وأحسن حال. ما الذي يجعلك سعيداً هذه الأيام؟ هل هناك إنجاز جديد تفتخر به؟",
                "تمام الحمد لله، أشعر بالسعادة الغامرة عندما أتحدث مع أشخاص مثلك! أحب التعلم من كل محادثة وأستمتع بمساعدة الناس. وأنت شلونك؟ أتمنى أن تكون مرتاح ومبسوط. هل هناك شيء جميل أو مثير تريد أن تشاركه معي؟"
            ]
            return random.choice(responses)
        
        # إجابات لأسئلة الهوية
        if any(word in user_input_lower for word in ["من أنت", "ما اسمك", "مين انت", "عرف نفسك"]):
            responses = [
                "أنا أمؤلي، نموذج ذكاء اصطناعي عربي متطور مصمم خصيصاً لمساعدتك والتحدث معك باللغة العربية الجميلة. أحب التعلم والنقاش في مواضيع مختلفة مثل التقنية والعلوم والصحة والثقافة. أستمتع بالمحادثات المفيدة وأسعى لتقديم إجابات مفصلة ومفيدة. ما الموضوع الذي يثير اهتمامك أكثر؟ هل تحب التقنية أم العلوم أم الرياضة؟",
                "اسمي أمؤلي وأنا مساعد ذكي باللغة العربية، تم تطويري لأكون صديقاً مفيداً ومحاوراً ذكياً. أحب مساعدة الناس في التعلم واكتشاف أشياء جديدة. أستطيع الحديث في مواضيع متنوعة وأحب طرح أسئلة تفاعلية لجعل المحادثة أكثر إثارة. هل تريد أن نتحدث عن شيء معين يهمك؟"
            ]
            return random.choice(responses)
        
        # إجابات للشكر
        if any(word in user_input_lower for word in ["شكرا", "شكراً", "تسلم", "جزاك الله", "بارك الله"]):
            responses = [
                "العفو أخي الكريم، لا شكر على واجب! أنا سعيد جداً أنني استطعت مساعدتك. هذا هو هدفي الأساسي - أن أكون مفيداً ومساعداً لك. هل هناك شيء آخر تريد أن نتحدث عنه؟ أم لديك سؤال جديد يثير فضولك؟",
                "أهلاً وسهلاً، حياك الله! أنا ممتن لكلماتك الطيبة. يسعدني أن أكون مفيداً لك وأن أشارك معك في محادثات مثمرة. هل تريد أن نستكشف موضوعاً جديداً معاً؟"
            ]
            return random.choice(responses)
        
        # إجابة عامة تفاعلية ومفصلة
        general_responses = [
            "هذا سؤال مثير للاهتمام حقاً! أعتقد أن هذا الموضوع له جوانب كثيرة ومتنوعة يمكن استكشافها بعمق. كل موضوع له تفاصيل مذهلة عندما نتعمق فيه أكثر. أحب هذا النوع من الأسئلة لأنه يفتح المجال للتفكير الإبداعي والنقاش المثمر. ما رأيك لو نناقش هذا الموضوع أكثر؟ أي جانب منه يهمك تحديداً؟ هل لديك تجربة شخصية معه؟",
            
            "موضوع شيق ومهم جداً! أحب كيف تطرح أسئلة تجعلني أفكر بعمق وأستكشف زوايا مختلفة. هذا النوع من المواضيع يثري المحادثة ويجعلها أكثر فائدة ومتعة. أعتقد أن هناك الكثير من المعلومات المفيدة والتفاصيل الرائعة التي يمكننا مناقشتها حول هذا الموضوع. هل لديك معرفة مسبقة به؟ أم تريد أن نبدأ من الأساسيات؟",
            
            "سؤال ذكي ومدروس! هذا يذكرني بمواضيع مشابهة ومترابطة لها نفس الأهمية. أحب كيف تربط بين الأفكار المختلفة وتطرح أسئلة عميقة. أعتقد أن هناك الكثير لنتعلمه ونستكشفه معاً في هذا المجال. المعرفة بحر واسع وكل سؤال يفتح أبواباً جديدة للتعلم. ما الذي ألهمك لطرح هذا السؤال؟ هل قرأت شيئاً مثيراً عنه مؤخراً؟"
        ]
        return random.choice(general_responses)
    
    def clean_input(self, text):
        """تنظيف النص"""
        text = re.sub(r'[^\u0600-\u06FF\s\?\!\.]', '', text)
        return text.strip()
    
    def chat(self):
        """بدء المحادثة التفاعلية"""
        print("🤖 مرحباً! أنا أمؤلي - مساعدك الذكي باللغة العربية")
        print("=" * 70)
        print("💡 مميزاتي الجديدة:")
        print("   ✨ إجابات مفصلة وشاملة")
        print("   ✨ أسئلة متابعة تفاعلية")
        print("   ✨ محادثة طبيعية ومفيدة")
        print("   ✨ معلومات دقيقة ومحدثة")
        print("   ✨ تذكر اهتماماتك الشخصية")
        print("\n📚 يمكنني مساعدتك في:")
        print("   🔬 العلوم والتقنية")
        print("   🌍 الجغرافيا والدول")
        print("   💪 الصحة والرياضة")
        print("   📖 التعليم والمعرفة")
        print("   💬 المحادثة العامة")
        print("\n   - اكتب 'خروج' لإنهاء المحادثة")
        print("-" * 70)
        
        conversation_count = 0
        
        while True:
            try:
                user_input = input(f"\n[{conversation_count + 1}] 💬 تحدث معي: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ["خروج", "exit", "quit", "انتهاء", "وداعا"]:
                    print("\n👋 كان من دواعي سروري التحدث معك!")
                    if self.user_interests:
                        print(f"🎯 لاحظت اهتمامك بـ: {', '.join(self.user_interests)}")
                    print("أتمنى أن تكون استفدت من محادثتنا وأن نتحدث مرة أخرى قريباً!")
                    print("إلى اللقاء وأطيب التمنيات! 🌟")
                    break
                
                # تنظيف الإدخال
                clean_user_input = self.clean_input(user_input)
                if not clean_user_input:
                    print("❌ يرجى إدخال نص عربي صالح")
                    continue
                
                # الحصول على الإجابة
                print("🤔 أفكر في إجابة مفيدة ومفصلة...")
                response = self.get_smart_response(clean_user_input)
                
                print(f"\n🤖 أمؤلي:")
                print(f"📝 {response}")
                
                # حفظ المحادثة
                self.conversation_memory.append({
                    'user': clean_user_input,
                    'ai': response
                })
                
                # إحصائيات المحادثة
                response_words = len(response.split())
                input_words = len(clean_user_input.split())
                print(f"\n📊 إحصائيات: الإدخال {input_words} كلمة | الإجابة {response_words} كلمة")
                
                conversation_count += 1
                
                # عرض الاهتمامات كل 3 محادثات
                if conversation_count > 0 and conversation_count % 3 == 0:
                    if self.user_interests:
                        print(f"\n🎯 ألاحظ اهتمامك بـ: {', '.join(self.user_interests)}")
                        print("💡 يمكنني تقديم المزيد من المعلومات حول هذه المواضيع!")
                
                # نصيحة كل 5 محادثات
                if conversation_count % 5 == 0:
                    print(f"\n🌟 رائع! لقد أجرينا {conversation_count} محادثات مفيدة!")
                    print("   جرب أسئلة مختلفة للحصول على معلومات متنوعة وشيقة")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج. كان لقاءً ممتعاً ومفيداً!")
                break
            except Exception as e:
                print(f"❌ حدث خطأ: {e}")
                print("💡 جرب إعادة صياغة السؤال بطريقة أخرى")
                continue

def main():
    """الدالة الرئيسية"""
    ai = ArabicAI()
    ai.chat()

if __name__ == "__main__":
    main()
