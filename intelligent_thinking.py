# -*- coding: utf-8 -*-
"""
نظام التفكير الذكي والتعلم التفاعلي لأمؤلي-T1
"""

import os
import pickle
import json
import datetime
import re
from typing import Dict, List, Any

class IntelligentMemory:
    """نظام ذاكرة ذكية للتعلم والحفظ"""
    
    def __init__(self, memory_file: str = "intelligent_memory.json"):
        self.memory_file = memory_file
        self.memory = {
            "learned_facts": {},           # حقائق تم تعلمها
            "conversation_patterns": {},   # أنماط المحادثة
            "user_preferences": {},        # تفضيلات المستخدم
            "word_associations": {},       # ارتباطات الكلمات
            "context_memory": [],          # ذاكرة السياق
            "learning_sessions": []        # جلسات التعلم
        }
        self.load_memory()
    
    def load_memory(self):
        """تحميل الذاكرة من الملف"""
        if os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    self.memory = json.load(f)
                print(f"🧠 تم تحميل الذاكرة: {len(self.memory['learned_facts'])} حقيقة محفوظة")
            except:
                print("⚠️ خطأ في تحميل الذاكرة، سيتم إنشاء ذاكرة جديدة")
    
    def save_memory(self):
        """حفظ الذاكرة في الملف"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ خطأ في حفظ الذاكرة: {e}")
    
    def learn_fact(self, question: str, answer: str, confidence: float = 1.0):
        """تعلم حقيقة جديدة"""
        key = self.normalize_text(question)
        self.memory["learned_facts"][key] = {
            "answer": answer,
            "confidence": confidence,
            "learned_at": datetime.datetime.now().isoformat(),
            "usage_count": 0
        }
        self.save_memory()
    
    def get_learned_fact(self, question: str) -> str:
        """استرجاع حقيقة محفوظة مع البحث الذكي"""
        key = self.normalize_text(question)

        # البحث المباشر أولاً
        if key in self.memory["learned_facts"]:
            fact = self.memory["learned_facts"][key]
            fact["usage_count"] += 1
            self.save_memory()
            return fact["answer"]

        # البحث الذكي في الإجابات المحفوظة
        question_keywords = self.extract_keywords(question)

        for stored_question, fact_data in self.memory["learned_facts"].items():
            stored_answer = fact_data["answer"]

            # البحث عن كلمات مفتاحية في الإجابة
            for keyword in question_keywords:
                if keyword in stored_answer:
                    # استخراج الجزء المناسب من الإجابة
                    relevant_part = self.extract_relevant_answer(stored_answer, keyword, question)
                    if relevant_part:
                        fact_data["usage_count"] += 1
                        self.save_memory()
                        return relevant_part

        return None

    def extract_keywords(self, text: str) -> list:
        """استخراج الكلمات المفتاحية"""
        # إزالة كلمات الاستفهام والربط
        stop_words = ["ما", "هو", "هي", "كيف", "لماذا", "متى", "أين", "من", "في", "على", "إلى", "عن"]

        words = text.split()
        keywords = []

        for word in words:
            clean_word = re.sub(r'[^\u0600-\u06FF]', '', word)
            if clean_word and len(clean_word) > 2 and clean_word not in stop_words:
                keywords.append(clean_word)

        return keywords

    def extract_relevant_answer(self, full_answer: str, keyword: str, original_question: str) -> str:
        """استخراج الجزء المناسب من الإجابة الطويلة"""
        # تقسيم الإجابة إلى أجزاء
        parts = re.split(r'[،,]', full_answer)

        for part in parts:
            part = part.strip()
            if keyword in part:
                # إذا كان السؤال عن عاصمة
                if "عاصمة" in original_question:
                    # البحث عن نمط "مدينة عاصمة دولة"
                    if "عاصمة" in part:
                        return part
                    # أو نمط "مدينة هي عاصمة دولة"
                    elif any(word in part for word in ["هي", "هو"]):
                        return part
                # للأسئلة الأخرى، أرجع الجزء المحتوي على الكلمة المفتاحية
                else:
                    return part

        return None
    
    def add_word_association(self, word1: str, word2: str, strength: float = 1.0):
        """إضافة ارتباط بين كلمتين"""
        word1 = self.normalize_text(word1)
        word2 = self.normalize_text(word2)
        
        if word1 not in self.memory["word_associations"]:
            self.memory["word_associations"][word1] = {}
        
        self.memory["word_associations"][word1][word2] = strength
        self.save_memory()
    
    def get_word_associations(self, word: str) -> List[str]:
        """الحصول على الكلمات المرتبطة"""
        word = self.normalize_text(word)
        if word in self.memory["word_associations"]:
            # ترتيب حسب قوة الارتباط
            associations = self.memory["word_associations"][word]
            return sorted(associations.keys(), key=lambda x: associations[x], reverse=True)
        return []
    
    def add_context(self, user_input: str, ai_response: str):
        """إضافة سياق للذاكرة"""
        context = {
            "user_input": user_input,
            "ai_response": ai_response,
            "timestamp": datetime.datetime.now().isoformat()
        }
        self.memory["context_memory"].append(context)
        
        # الاحتفاظ بآخر 50 محادثة فقط
        if len(self.memory["context_memory"]) > 50:
            self.memory["context_memory"] = self.memory["context_memory"][-50:]
        
        self.save_memory()
    
    def get_recent_context(self, limit: int = 5) -> List[Dict]:
        """الحصول على السياق الأخير"""
        return self.memory["context_memory"][-limit:]
    
    def normalize_text(self, text: str) -> str:
        """تطبيع النص للمقارنة"""
        text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
        text = re.sub(r'\s+', ' ', text).strip().lower()
        return text

class IntelligentThinking:
    """نظام التفكير الذكي"""
    
    def __init__(self, memory: IntelligentMemory):
        self.memory = memory
        self.knowledge_base = self.load_knowledge_base()
    
    def load_knowledge_base(self) -> Dict[str, List[str]]:
        """تحميل قاعدة المعرفة الأساسية"""
        return {
            "الذكاء الاصطناعي": [
                "تقنية حديثة", "محاكاة الذكاء البشري", "خوارزميات ذكية",
                "تعلم آلي", "شبكات عصبية", "معالجة البيانات"
            ],
            "الحاسوب": [
                "جهاز إلكتروني", "معالجة البيانات", "تخزين المعلومات",
                "معالج", "ذاكرة", "برمجيات"
            ],
            "البرمجة": [
                "كتابة الكود", "لغات البرمجة", "حل المشاكل",
                "خوارزميات", "تطوير البرامج", "منطق"
            ],
            "العلوم": [
                "دراسة الطبيعة", "تجارب", "نظريات", "اكتشافات",
                "فيزياء", "كيمياء", "أحياء", "رياضيات"
            ],
            "التعليم": [
                "اكتساب المعرفة", "تطوير المهارات", "دراسة",
                "مدارس", "جامعات", "معلمين", "طلاب"
            ]
        }
    
    def think_about_question(self, question: str) -> str:
        """التفكير في السؤال وتوليد إجابة ذكية"""
        # البحث في الذاكرة أولاً
        learned_answer = self.memory.get_learned_fact(question)
        if learned_answer:
            return f"من ذاكرتي: {learned_answer}"
        
        # تحليل الكلمات المفتاحية
        keywords = self.extract_keywords(question)
        
        # البحث في قاعدة المعرفة
        relevant_info = []
        for keyword in keywords:
            for topic, info_list in self.knowledge_base.items():
                if keyword in topic or any(keyword in info for info in info_list):
                    relevant_info.extend(info_list[:2])  # أخذ أول معلومتين
        
        # إذا وجدت معلومات ذات صلة
        if relevant_info:
            # إنشاء إجابة من المعلومات المتاحة
            answer = self.construct_answer(question, keywords, relevant_info)
            
            # حفظ الإجابة في الذاكرة للمرات القادمة
            self.memory.learn_fact(question, answer, confidence=0.8)
            
            return answer
        
        # إذا لم توجد معلومات، حاول التفكير الإبداعي
        return self.creative_thinking(question, keywords)
    
    def extract_keywords(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية"""
        # إزالة كلمات الاستفهام والربط
        stop_words = ["ما", "هو", "هي", "كيف", "لماذا", "متى", "أين", "من", "في", "على", "إلى", "عن"]
        
        words = text.split()
        keywords = []
        
        for word in words:
            clean_word = re.sub(r'[^\u0600-\u06FF]', '', word)
            if clean_word and len(clean_word) > 2 and clean_word not in stop_words:
                keywords.append(clean_word)
        
        return keywords
    
    def construct_answer(self, question: str, keywords: List[str], info: List[str]) -> str:
        """بناء إجابة من المعلومات المتاحة"""
        # تحديد نوع السؤال
        if any(word in question for word in ["ما هو", "ما هي", "تعريف"]):
            # سؤال تعريفي
            if keywords:
                main_topic = keywords[0]
                relevant_info = [i for i in info if any(k in i for k in keywords)]
                if relevant_info:
                    return f"{main_topic} هو {relevant_info[0]} ويتميز بـ {relevant_info[1] if len(relevant_info) > 1 else 'خصائص متقدمة'}"
        
        elif any(word in question for word in ["كيف", "طريقة"]):
            # سؤال عن الطريقة
            return f"يمكن تحقيق ذلك من خلال {info[0]} باستخدام {info[1] if len(info) > 1 else 'الطرق المناسبة'}"
        
        elif any(word in question for word in ["لماذا", "سبب"]):
            # سؤال عن السبب
            return f"السبب في ذلك يعود إلى {info[0]} وأيضاً {info[1] if len(info) > 1 else 'عوامل أخرى مهمة'}"
        
        # إجابة عامة
        return f"بناءً على معرفتي، هذا يتعلق بـ {info[0]} و {info[1] if len(info) > 1 else 'مواضيع ذات صلة'}"
    
    def creative_thinking(self, question: str, keywords: List[str]) -> str:
        """التفكير الإبداعي للأسئلة غير المعروفة"""
        # البحث عن ارتباطات في الذاكرة
        associations = []
        for keyword in keywords:
            word_associations = self.memory.get_word_associations(keyword)
            associations.extend(word_associations[:2])
        
        if associations:
            return f"بناءً على تفكيري، هذا قد يكون مرتبطاً بـ {associations[0]} و {associations[1] if len(associations) > 1 else 'مفاهيم مشابهة'}"
        
        # إجابة إبداعية عامة
        if keywords:
            main_word = keywords[0]
            return f"من خلال تفكيري في {main_word}، أعتقد أنه موضوع مهم يستحق الدراسة والتعلم أكثر"
        
        return "هذا سؤال مثير للاهتمام، أحتاج للتفكير فيه أكثر وتعلم المزيد عنه"
    
    def learn_from_interaction(self, user_input: str, ai_response: str, user_feedback: str = None):
        """التعلم من التفاعل"""
        # حفظ السياق
        self.memory.add_context(user_input, ai_response)
        
        # إذا كان هناك تقييم من المستخدم
        if user_feedback and user_feedback.isdigit():
            rating = int(user_feedback)
            if rating >= 4:
                # إجابة جيدة، احفظها كحقيقة
                self.memory.learn_fact(user_input, ai_response, confidence=0.9)
            elif rating <= 2:
                # إجابة سيئة، تعلم من الخطأ
                self.memory.learn_fact(user_input, "يحتاج تحسين", confidence=0.1)
        
        # تعلم ارتباطات الكلمات
        user_words = self.extract_keywords(user_input)
        response_words = self.extract_keywords(ai_response)
        
        for user_word in user_words:
            for response_word in response_words:
                self.memory.add_word_association(user_word, response_word, 0.5)

def enhance_response_with_thinking(user_input: str, basic_response: str, 
                                 thinking_system: IntelligentThinking) -> str:
    """تحسين الإجابة بالتفكير الذكي"""
    
    # إذا كانت الإجابة الأساسية تحتوي على كلمات غريبة أو مكررة
    if is_poor_response(basic_response):
        print("🤔 الإجابة الأساسية غير مناسبة، سأفكر في إجابة أفضل...")
        intelligent_response = thinking_system.think_about_question(user_input)
        return intelligent_response
    
    return basic_response

def is_poor_response(response: str) -> bool:
    """تحديد ما إذا كانت الإجابة ضعيفة"""
    words = response.split()
    
    # إذا كانت الإجابة قصيرة جداً
    if len(words) < 3:
        return True
    
    # إذا كانت تحتوي على تكرار مفرط
    word_counts = {}
    for word in words:
        word_counts[word] = word_counts.get(word, 0) + 1
    
    max_repetition = max(word_counts.values()) if word_counts else 0
    if max_repetition > len(words) // 3:  # أكثر من ثلث الكلمات مكررة
        return True
    
    # إذا كانت تحتوي على كلمات غريبة كثيرة
    strange_words = ["النموذج", "التي", "طريق", "الآلة", "الآلي", "مما", "منها", "ولا"]
    strange_count = sum(1 for word in words if word in strange_words)
    if strange_count > len(words) // 2:  # أكثر من نصف الكلمات غريبة
        return True
    
    return False
