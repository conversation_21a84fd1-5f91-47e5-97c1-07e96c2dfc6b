# -*- coding: utf-8 -*-
"""
نموذج ذكاء اصطناعي عربي تفاعلي ومتطور
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.optimizers import Adam
import re
import random

# إعدادات النموذج المحسن
MODEL_NAME = "interactive-arabic-ai"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"

# معاملات محسنة للتفاعل الأفضل
MAX_SEQUENCE_LEN = 20  # أطول للسياق الأفضل
EMBEDDING_DIM = 256
LSTM_UNITS = 128
DROPOUT_RATE = 0.3
EPOCHS = 15
BATCH_SIZE = 16

def create_rich_conversational_data():
    """إنشاء بيانات محادثة غنية ومتنوعة"""
    
    # محادثات تفاعلية طويلة
    conversations = [
        # محادثات عن الذكاء الاصطناعي
        "ما هو الذكاء الاصطناعي؟ الذكاء الاصطناعي هو تقنية متطورة تحاكي قدرات العقل البشري في التفكير والتعلم. هل تريد أن تعرف المزيد عن تطبيقاته في حياتنا اليومية؟",
        "كيف يعمل الذكاء الاصطناعي؟ يعمل من خلال خوارزميات معقدة تحلل البيانات وتتعلم منها لاتخاذ قرارات ذكية. ما رأيك في استخدام الذكاء الاصطناعي في التعليم؟",
        "ما فوائد الذكاء الاصطناعي؟ يساعد في تطوير الطب والتعليم والنقل والأمن. يمكنه تشخيص الأمراض بدقة عالية ومساعدة الأطباء. هل لديك اهتمام بمجال معين تريد معرفة كيف يستفيد منه؟",
        
        # محادثات عن العلوم
        "لماذا ندرس العلوم؟ العلوم تساعدنا على فهم العالم من حولنا وحل المشاكل التي نواجهها. من خلال العلم نطور الأدوية ونبني التقنيات. أي فرع من العلوم يثير اهتمامك أكثر؟",
        "ما أهمية الرياضيات؟ الرياضيات لغة العلوم وأساس كل التقنيات الحديثة. بدونها لا يمكن بناء الحاسوب أو الطائرة أو حتى الهاتف الذكي. هل تجد صعوبة في تعلم الرياضيات؟",
        "كيف تؤثر الفيزياء على حياتنا؟ الفيزياء تفسر كل شيء حولنا من الضوء والصوت إلى الكهرباء والمغناطيسية. بفضلها نفهم كيف تعمل الأجهزة. ما هو أكثر شيء يثير فضولك في الفيزياء؟",
        
        # محادثات عن الجغرافيا والثقافة
        "ما عاصمة السعودية؟ عاصمة المملكة العربية السعودية هي الرياض، وهي أكبر مدنها وأهم مراكزها الاقتصادية والسياسية. هل زرت الرياض من قبل أم تخطط لزيارتها؟",
        "أخبرني عن مصر؟ مصر بلد عريق بتاريخ يمتد لآلاف السنين، موطن الأهرامات والحضارة الفرعونية. عاصمتها القاهرة تسمى أم الدنيا. ما الذي تعرفه عن تاريخ مصر القديم؟",
        "ما تعرف عن الإمارات؟ دولة الإمارات العربية المتحدة دولة حديثة ومتطورة، عاصمتها أبو ظبي ودبي مركز تجاري عالمي. هل تود معرفة المزيد عن إنجازاتها التقنية؟",
        
        # محادثات عن الصحة والرياضة
        "كيف نحافظ على الصحة؟ الصحة تحتاج لنظام غذائي متوازن وممارسة الرياضة بانتظام والنوم الكافي. كما يجب تجنب التدخين والضغوط النفسية. ما هي عاداتك الصحية اليومية؟",
        "ما فوائد الرياضة؟ الرياضة تقوي الجسم والعقل وتحسن المزاج وتقلل التوتر. تساعد في الوقاية من الأمراض وتزيد الثقة بالنفس. أي نوع من الرياضة تفضل ممارسته؟",
        "لماذا النوم مهم؟ النوم ضروري لراحة الجسم وتجديد الخلايا وتقوية الذاكرة. قلة النوم تؤثر على التركيز والصحة العامة. كم ساعة تنام عادة في الليلة؟",
        
        # محادثات عن التعليم والمستقبل
        "ما أهمية التعليم؟ التعليم يفتح أبواب المستقبل ويطور قدرات الإنسان ويساعده على تحقيق أحلامه. بالتعليم نبني مجتمعات متقدمة ومزدهرة. ما هو مجال دراستك أو اهتمامك؟",
        "كيف سيكون المستقبل؟ المستقبل سيشهد تطورات مذهلة في التقنية والطب والفضاء. ربما نرى سيارات طائرة ومدن ذكية وعلاج للأمراض المستعصية. ما هو حلمك للمستقبل؟",
        "ما دور التقنية في التعليم؟ التقنية تجعل التعليم أكثر تفاعلاً ومتعة، يمكن التعلم من أي مكان وفي أي وقت. الواقع الافتراضي يمكن أن ينقلنا لأي مكان. كيف تستخدم التقنية في تعلمك؟",
        
        # تحيات ومحادثات اجتماعية
        "مرحباً كيف حالك؟ أهلاً وسهلاً بك، أنا بخير والحمد لله. سعيد بلقائك اليوم. كيف كان يومك؟ هل هناك شيء مثير حدث معك؟",
        "صباح الخير كيف نومك؟ صباح النور والسرور، نمت بخير والحمد لله. أتمنى أن تكون قد استرحت جيداً. ما هي خططك لهذا اليوم الجميل؟",
        "ما أخبارك اليوم؟ الحمد لله كله تمام، يوم مليء بالعمل والإنجاز. أحب أن أتعلم أشياء جديدة كل يوم. وأنت ماذا تعلمت اليوم؟",
        
        # أسئلة فلسفية وعميقة
        "ما معنى السعادة؟ السعادة شعور داخلي بالرضا والطمأنينة، تأتي من تحقيق الأهداف ومساعدة الآخرين والامتنان لما نملك. ما الذي يجعلك سعيداً في الحياة؟",
        "كيف نحقق النجاح؟ النجاح يحتاج للعمل الجاد والصبر والتعلم المستمر. المهم أن نحدد أهدافنا ونسعى لتحقيقها خطوة بخطوة. ما هو هدفك الأكبر في الحياة؟",
        "ما قيمة الصداقة؟ الصداقة كنز ثمين، الصديق الحقيقي يقف معك في السراء والضراء ويشاركك أفراحك وأحزانك. الصداقة تجعل الحياة أجمل وأكثر معنى. كيف تختار أصدقاءك؟"
    ]
    
    # إضافة المزيد من البيانات التفاعلية
    interactive_responses = [
        "هذا سؤال ممتاز! دعني أفكر فيه معك. ما رأيك لو نناقش هذا الموضوع أكثر؟",
        "أحب هذا النوع من الأسئلة لأنه يجعلني أفكر بعمق. هل لديك تجربة شخصية مع هذا الموضوع؟",
        "موضوع شيق جداً! أعتقد أن هناك جوانب كثيرة يمكن استكشافها. أي جانب يهمك أكثر؟",
        "سؤال ذكي! هذا يذكرني بموضوع مشابه. هل تريد أن نتوسع في النقاش؟",
        "إجابة رائعة! أرى أنك تفكر بطريقة إبداعية. ما الذي ألهمك لهذا التفكير؟",
        "نقطة مهمة جداً! هذا يفتح المجال لأسئلة أخرى مثيرة. ما رأيك لو نستكشف المزيد؟",
        "أتفق معك تماماً! هذا يجعلني أتساءل عن أشياء أخرى مرتبطة. هل فكرت في هذا من قبل؟",
        "تفكير عميق! أحب كيف تربط بين الأفكار المختلفة. هل يمكنك أن تخبرني المزيد عن وجهة نظرك؟"
    ]
    
    return conversations + interactive_responses

def preprocess_texts(texts):
    """معالجة النصوص مع الحفاظ على الطول والتفاصيل"""
    processed = []
    for text in texts:
        # تنظيف النص مع الحفاظ على علامات الترقيم المهمة
        text = re.sub(r'[^\u0600-\u06FF\s\.\،\؟\!\:\؛]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        # قبول الجمل الطويلة والقصيرة
        if len(text.split()) >= 3:
            processed.append(text)
    
    return processed

def create_sequences(tokenizer, corpus, max_sequence_len):
    """إنشاء تسلسلات تدريب محسنة للمحادثات الطويلة"""
    input_sequences = []
    
    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]
        
        # إنشاء تسلسلات متداخلة أكثر تعقيداً
        for i in range(3, len(token_list) + 1):
            if i <= max_sequence_len:
                n_gram_sequence = token_list[:i]
                # إضافة padding
                while len(n_gram_sequence) < max_sequence_len:
                    n_gram_sequence = [0] + n_gram_sequence
                input_sequences.append(n_gram_sequence)
            else:
                # للنصوص الطويلة، إنشاء عدة تسلسلات
                for j in range(0, len(token_list) - max_sequence_len + 1, max_sequence_len // 2):
                    n_gram_sequence = token_list[j:j + max_sequence_len]
                    input_sequences.append(n_gram_sequence)
    
    if not input_sequences:
        return np.array([]), np.array([])
    
    input_sequences = np.array(input_sequences)
    X = input_sequences[:, :-1]
    y = input_sequences[:, -1]
    
    vocab_size = len(tokenizer.word_index) + 1
    y = to_categorical(y, num_classes=vocab_size)
    
    return X, y

def build_advanced_conversational_model(vocab_size, max_sequence_len):
    """بناء نموذج متقدم للمحادثة"""
    model = Sequential()
    
    # طبقة التضمين المحسنة
    model.add(Embedding(
        input_dim=vocab_size,
        output_dim=EMBEDDING_DIM,
        input_length=max_sequence_len - 1,
        mask_zero=True
    ))
    
    # طبقات LSTM متعددة للفهم العميق
    model.add(Bidirectional(LSTM(
        LSTM_UNITS,
        return_sequences=True,
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))
    
    model.add(Bidirectional(LSTM(
        LSTM_UNITS // 2,
        return_sequences=False,
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))
    
    # طبقات مكتملة الاتصال للذكاء المتقدم
    model.add(Dense(512, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))
    
    model.add(Dense(256, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))
    
    model.add(Dense(128, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))
    
    # طبقة الإخراج
    model.add(Dense(vocab_size, activation='softmax'))
    
    # تجميع النموذج مع محسن متقدم
    optimizer = Adam(learning_rate=0.0005, beta_1=0.9, beta_2=0.999)
    
    model.compile(
        loss='categorical_crossentropy',
        optimizer=optimizer,
        metrics=['accuracy', 'top_k_categorical_accuracy']
    )
    
    return model

def generate_long_response(seed_text, model, tokenizer, max_sequence_len, min_words=15, max_words=30):
    """توليد إجابة طويلة ومفصلة"""
    output_text = seed_text
    used_words = []

    for i in range(max_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]

        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]

        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')

        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]

            # Temperature sampling للتنوع
            temperature = 0.8
            predicted_probs = np.log(predicted_probs + 1e-8) / temperature
            predicted_probs = np.exp(predicted_probs)
            predicted_probs = predicted_probs / np.sum(predicted_probs)

            # Top-k sampling مع nucleus sampling
            top_k = min(15, len(predicted_probs))
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]

            # اختيار كلمة مناسبة
            selected_word = None
            for idx in sorted_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) >= 2:
                    # تجنب التكرار المفرط
                    recent_words = used_words[-5:] if len(used_words) >= 5 else used_words
                    if candidate_word not in recent_words or i > 10:
                        # تأكد من أن الكلمة عربية
                        if re.match(r'^[\u0600-\u06FF]+$', candidate_word):
                            selected_word = candidate_word
                            break

            if not selected_word:
                # استخدم أفضل كلمة متاحة
                for idx in sorted_indices:
                    candidate_word = tokenizer.index_word.get(idx, "")
                    if candidate_word and re.match(r'^[\u0600-\u06FF]+$', candidate_word):
                        selected_word = candidate_word
                        break

            if not selected_word:
                break

            output_text += " " + selected_word
            used_words.append(selected_word)

            # الحفاظ على آخر 8 كلمات فقط
            if len(used_words) > 8:
                used_words = used_words[-8:]

            # توقف إذا وصلنا للحد الأدنى ووجدنا نهاية طبيعية
            if i >= min_words and selected_word in ['؟', 'أليس', 'كذلك', 'صحيح']:
                break

        except Exception:
            break

    return output_text.strip()

def main():
    print("🚀 بدء تدريب النموذج التفاعلي المتطور...")

    # إنشاء البيانات الغنية
    texts = create_rich_conversational_data()
    processed_texts = preprocess_texts(texts)

    print(f"📝 تم معالجة {len(processed_texts)} نص محادثة")

    # إعداد Tokenizer
    tokenizer = Tokenizer()
    tokenizer.fit_on_texts(processed_texts)
    vocab_size = len(tokenizer.word_index) + 1

    print(f"🔤 حجم القاموس: {vocab_size} كلمة")

    # إنشاء بيانات التدريب
    X, y = create_sequences(tokenizer, processed_texts, MAX_SEQUENCE_LEN)

    if X.shape[0] == 0:
        print("❌ لا توجد بيانات تدريب")
        return

    print(f"📊 عدد عينات التدريب: {X.shape[0]}")

    # بناء النموذج المتقدم
    model = build_advanced_conversational_model(vocab_size, MAX_SEQUENCE_LEN)

    # بناء النموذج
    dummy_input = np.zeros((1, MAX_SEQUENCE_LEN - 1))
    model(dummy_input)
    print(f"🏗️ تم بناء النموذج التفاعلي مع {model.count_params():,} معامل")

    # التدريب المتقدم
    print("🚀 بدء التدريب المتقدم...")
    history = model.fit(
        X, y,
        epochs=EPOCHS,
        batch_size=BATCH_SIZE,
        verbose=1,
        validation_split=0.15,
        shuffle=True
    )

    # حفظ النموذج
    model.save(MODEL_FILE)
    with open(TOKENIZER_FILE, 'wb') as f:
        pickle.dump(tokenizer, f)

    print(f"✅ تم حفظ النموذج التفاعلي في: {MODEL_FILE}")
    print(f"✅ تم حفظ Tokenizer في: {TOKENIZER_FILE}")

    # عرض إحصائيات التدريب
    final_loss = history.history['loss'][-1]
    final_accuracy = history.history['accuracy'][-1]
    print(f"📊 الخسارة النهائية: {final_loss:.4f}")
    print(f"📊 الدقة النهائية: {final_accuracy:.4f}")

    # اختبار النموذج بإجابات طويلة
    print("\n🧪 اختبار النموذج بإجابات مفصلة:")
    test_inputs = [
        "ما هو الذكاء الاصطناعي",
        "كيف نحافظ على الصحة",
        "ما أهمية التعليم"
    ]

    for test_input in test_inputs:
        result = generate_long_response(test_input, model, tokenizer, MAX_SEQUENCE_LEN, 15, 25)
        print(f"🔤 '{test_input}'")
        print(f"📝 '{result}'")
        print("-" * 50)

    print("\n✅ انتهى تدريب النموذج التفاعلي بنجاح!")
    print("💡 النموذج الآن قادر على المحادثات الطويلة والتفاعلية")

if __name__ == "__main__":
    main()
