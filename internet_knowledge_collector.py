# -*- coding: utf-8 -*-
"""
جامع المعرفة من الإنترنت لنموذج ML-T1
نظام ذكي لجمع وتنظيم المعلومات من مصادر متعددة
"""

import requests
import json
import time
from datetime import datetime
import re
from urllib.parse import quote

class InternetKnowledgeCollector:
    """جامع المعرفة من الإنترنت"""
    
    def __init__(self):
        self.collected_data = {}
        self.sources = {
            'wikipedia_ar': 'https://ar.wikipedia.org/api/rest_v1/page/summary/',
            'news_apis': [],  # يمكن إضافة APIs للأخبار
            'educational_sites': []  # مواقع تعليمية
        }
        
        # إعدادات الجمع
        self.max_content_length = 1000
        self.timeout = 10
        
    def search_wikipedia_arabic(self, topic):
        """البحث في ويكيبيديا العربية"""
        try:
            print(f"🔍 أبحث عن '{topic}' في ويكيبيديا العربية...")
            
            # تنظيف وترميز الموضوع
            clean_topic = quote(topic.strip())
            url = f"{self.sources['wikipedia_ar']}{clean_topic}"
            
            response = requests.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                
                # استخراج المعلومات المهمة
                extracted_info = {
                    'title': data.get('title', topic),
                    'summary': data.get('extract', ''),
                    'url': data.get('content_urls', {}).get('desktop', {}).get('page', ''),
                    'source': 'ويكيبيديا العربية',
                    'collected_at': datetime.now().isoformat(),
                    'language': 'ar'
                }
                
                # تنظيف النص
                if extracted_info['summary']:
                    extracted_info['summary'] = self.clean_text(extracted_info['summary'])
                    extracted_info['word_count'] = len(extracted_info['summary'].split())
                
                print(f"✅ تم جمع معلومات عن '{topic}' بنجاح!")
                return extracted_info
            
            else:
                print(f"❌ لم أجد معلومات عن '{topic}' في ويكيبيديا")
                return None
                
        except requests.exceptions.Timeout:
            print("⏰ انتهت مهلة البحث")
            return None
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return None
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            return None
    
    def clean_text(self, text):
        """تنظيف النص من العلامات والرموز غير المرغوبة"""
        # إزالة الأقواس والمراجع
        text = re.sub(r'\[.*?\]', '', text)
        text = re.sub(r'\(.*?\)', '', text)
        
        # إزالة الأسطر الفارغة المتعددة
        text = re.sub(r'\n+', '\n', text)
        
        # تنظيف المسافات
        text = re.sub(r'\s+', ' ', text)
        
        # قطع النص إذا كان طويلاً جداً
        if len(text) > self.max_content_length:
            text = text[:self.max_content_length] + "..."
        
        return text.strip()
    
    def extract_key_concepts(self, text):
        """استخراج المفاهيم المفتاحية من النص"""
        # كلمات الوصل التي يجب تجاهلها
        stop_words = {
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هو', 'هي', 'أن', 'كان', 'كانت',
            'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'التي', 'اللذان', 'اللتان',
            'أو', 'لكن', 'لكن', 'بل', 'غير', 'سوى', 'إلا', 'ما', 'لا', 'لم', 'لن'
        }
        
        # استخراج الكلمات العربية
        words = re.findall(r'\b[\u0600-\u06FF]+\b', text)
        
        # تصفية الكلمات المهمة
        key_concepts = []
        for word in words:
            if len(word) > 2 and word not in stop_words:
                key_concepts.append(word)
        
        # إرجاع أكثر الكلمات تكراراً
        from collections import Counter
        word_freq = Counter(key_concepts)
        return [word for word, freq in word_freq.most_common(10)]
    
    def collect_comprehensive_info(self, topic):
        """جمع معلومات شاملة عن موضوع معين"""
        print(f"🌐 بدء جمع معلومات شاملة عن: {topic}")
        print("=" * 60)
        
        collected_info = {
            'topic': topic,
            'sources': [],
            'key_concepts': [],
            'summary': '',
            'collection_timestamp': datetime.now().isoformat()
        }
        
        # البحث في ويكيبيديا العربية
        wiki_info = self.search_wikipedia_arabic(topic)
        if wiki_info:
            collected_info['sources'].append(wiki_info)
            
            # استخراج المفاهيم المفتاحية
            if wiki_info['summary']:
                concepts = self.extract_key_concepts(wiki_info['summary'])
                collected_info['key_concepts'].extend(concepts)
        
        # إنشاء ملخص شامل
        if collected_info['sources']:
            collected_info['summary'] = self.create_comprehensive_summary(collected_info['sources'])
        
        # حفظ المعلومات
        self.save_collected_info(topic, collected_info)
        
        return collected_info
    
    def create_comprehensive_summary(self, sources):
        """إنشاء ملخص شامل من المصادر المجمعة"""
        summary_parts = []
        
        for source in sources:
            if source.get('summary'):
                summary_parts.append(f"**من {source['source']}:**\n{source['summary']}")
        
        if summary_parts:
            return "\n\n".join(summary_parts)
        else:
            return "لم يتم العثور على معلومات كافية."
    
    def save_collected_info(self, topic, info):
        """حفظ المعلومات المجمعة في ملف"""
        try:
            # تحميل البيانات الموجودة
            try:
                with open('collected_knowledge.json', 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except FileNotFoundError:
                existing_data = {}
            
            # إضافة المعلومات الجديدة
            existing_data[topic] = info
            
            # حفظ البيانات المحدثة
            with open('collected_knowledge.json', 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ معلومات '{topic}' بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ المعلومات: {e}")
    
    def load_collected_info(self, topic):
        """تحميل معلومات محفوظة عن موضوع"""
        try:
            with open('collected_knowledge.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get(topic, None)
        except FileNotFoundError:
            return None
        except Exception as e:
            print(f"❌ خطأ في تحميل المعلومات: {e}")
            return None
    
    def search_and_learn(self, topic):
        """البحث والتعلم عن موضوع جديد"""
        # التحقق من وجود معلومات محفوظة
        existing_info = self.load_collected_info(topic)
        
        if existing_info:
            print(f"📚 وجدت معلومات محفوظة عن '{topic}'")
            return existing_info
        else:
            print(f"🔍 لم أجد معلومات محفوظة، سأبحث في الإنترنت...")
            return self.collect_comprehensive_info(topic)
    
    def get_learning_suggestions(self, topic):
        """اقتراح مواضيع للتعلم بناءً على الموضوع الحالي"""
        info = self.load_collected_info(topic)
        
        if info and info.get('key_concepts'):
            suggestions = info['key_concepts'][:5]
            return suggestions
        else:
            # اقتراحات عامة
            general_suggestions = [
                f"تاريخ {topic}",
                f"أنواع {topic}",
                f"استخدامات {topic}",
                f"مستقبل {topic}",
                f"تطوير {topic}"
            ]
            return general_suggestions
    
    def generate_questions_from_collected_info(self, topic):
        """توليد أسئلة بناءً على المعلومات المجمعة"""
        info = self.load_collected_info(topic)
        
        if not info:
            return []
        
        questions = []
        
        # أسئلة أساسية
        questions.append({
            'question': f"ما هو {topic}؟",
            'answer': info.get('summary', '').split('\n')[0] if info.get('summary') else f"معلومات عن {topic}",
            'difficulty': 2,
            'source': 'معلومات مجمعة من الإنترنت'
        })
        
        # أسئلة من المفاهيم المفتاحية
        key_concepts = info.get('key_concepts', [])
        for concept in key_concepts[:3]:
            questions.append({
                'question': f"ما علاقة {concept} بـ {topic}؟",
                'answer': f"مفهوم مرتبط بـ {topic} تم استخراجه من المصادر",
                'difficulty': 3,
                'source': 'تحليل المفاهيم المفتاحية'
            })
        
        return questions

def main():
    """اختبار جامع المعرفة"""
    collector = InternetKnowledgeCollector()
    
    print("🌐 مرحباً بك في جامع المعرفة من الإنترنت!")
    print("=" * 60)
    
    while True:
        topic = input("\n🔍 أدخل موضوعاً للبحث عنه (أو 'خروج' للإنهاء): ").strip()
        
        if topic.lower() in ['خروج', 'exit', 'quit']:
            print("👋 وداعاً! تم حفظ كل المعلومات المجمعة.")
            break
        
        if not topic:
            continue
        
        # جمع المعلومات
        info = collector.search_and_learn(topic)
        
        if info and info.get('summary'):
            print(f"\n📝 **معلومات عن {topic}:**")
            print(info['summary'])
            
            if info.get('key_concepts'):
                print(f"\n🔑 **المفاهيم المفتاحية:**")
                print(", ".join(info['key_concepts'][:5]))
            
            # اقتراح مواضيع للتعلم
            suggestions = collector.get_learning_suggestions(topic)
            print(f"\n💡 **اقتراحات للتعلم أكثر:**")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"   {i}. {suggestion}")
        
        else:
            print(f"❌ لم أتمكن من جمع معلومات كافية عن '{topic}'")

if __name__ == "__main__":
    main()
