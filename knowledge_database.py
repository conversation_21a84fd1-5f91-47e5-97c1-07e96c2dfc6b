# -*- coding: utf-8 -*-
"""
قاعدة المعرفة الشاملة للذكاء الاصطناعي العربي
"""

# قاعدة معرفة ضخمة ومتنوعة
COMPREHENSIVE_KNOWLEDGE = {
    
    # ========== الجغرافيا والدول ==========
    "عاصمة العراق": {
        "answer": "عاصمة جمهورية العراق هي بغداد العريقة، مدينة السلام ودار السلام كما كانت تُسمى قديماً. تقع على ضفاف نهر دجلة وهي أكبر مدن العراق وأهمها. بغداد لها تاريخ عريق يمتد لأكثر من 1200 سنة، وكانت مركز الخلافة العباسية ومنارة العلم والثقافة في العالم الإسلامي. تشتهر بجامعاتها العريقة ومكتباتها التاريخية ومعالمها الأثرية.",
        "related_topics": ["تاريخ بغداد", "الخلافة العباسية", "بيت الحكمة", "نهر دجلة"],
        "questions": [
            "هل تعرف شيئاً عن تاريخ بغداد العريق وبيت الحكمة؟",
            "ما الذي تعرفه عن الحضارة العراقية القديمة في بلاد الرافدين؟",
            "هل سمعت عن الشعر العراقي والأدب العربي في العراق؟"
        ]
    },
    
    "عاصمة السعودية": {
        "answer": "عاصمة المملكة العربية السعودية هي مدينة الرياض الجميلة، وهي أكبر مدن المملكة ومركزها السياسي والاقتصادي والإداري. تقع في وسط شبه الجزيرة العربية وتضم أكثر من 7 مليون نسمة. تشتهر بناطحات السحاب الحديثة مثل برج المملكة وبرج الفيصلية، وتضم العديد من الجامعات المرموقة والمراكز التجارية الضخمة. الرياض تشهد تطوراً هائلاً في إطار رؤية 2030.",
        "related_topics": ["رؤية 2030", "نيوم", "القدية", "تاريخ السعودية"],
        "questions": [
            "هل زرت الرياض من قبل؟ أم تخطط لزيارتها قريباً؟",
            "ما الذي تعرفه عن مشروع نيوم أو القدية في السعودية؟",
            "هل تريد معرفة المزيد عن رؤية السعودية 2030؟"
        ]
    },
    
    "عاصمة مصر": {
        "answer": "عاصمة جمهورية مصر العربية هي القاهرة العريقة، والتي تُلقب بأم الدنيا لعراقتها وأهميتها التاريخية. تقع على ضفاف نهر النيل المبارك وتضم أكثر من 20 مليون نسمة. تشتهر بالأهرامات وأبو الهول والمتحف المصري والأزهر الشريف. تعتبر مركزاً ثقافياً وإعلامياً مهماً في العالم العربي، وتضم أقدم جامعة في العالم وهي الأزهر الشريف.",
        "related_topics": ["الأهرامات", "الأزهر الشريف", "نهر النيل", "الحضارة الفرعونية"],
        "questions": [
            "هل زرت الأهرامات من قبل؟ ما انطباعك عن هذا المعلم العظيم؟",
            "ما الذي تعرفه عن الحضارة الفرعونية القديمة؟",
            "هل تحب الأفلام والمسلسلات المصرية؟"
        ]
    },
    
    # ========== التقنية والذكاء الاصطناعي ==========
    "الذكاء الاصطناعي": {
        "answer": "الذكاء الاصطناعي هو تقنية متطورة جداً تحاكي قدرات العقل البشري في التفكير والتعلم واتخاذ القرارات الذكية. يستخدم خوارزميات معقدة وشبكات عصبية لتحليل البيانات الضخمة والتعلم منها. له تطبيقات واسعة في الطب لتشخيص الأمراض، وفي التعليم لتخصيص المناهج، وفي النقل للسيارات الذاتية القيادة، وفي الأمن لحماية البيانات. يشمل تعلم الآلة والتعلم العميق ومعالجة اللغات الطبيعية والرؤية الحاسوبية.",
        "related_topics": ["تعلم الآلة", "الشبكات العصبية", "البيانات الضخمة", "الروبوتات"],
        "questions": [
            "هل تريد معرفة كيف يؤثر الذكاء الاصطناعي على مستقبل الوظائف؟",
            "ما رأيك في استخدام الذكاء الاصطناعي في التعليم؟",
            "هل تعتقد أن الذكاء الاصطناعي سيحل محل البشر أم سيساعدهم؟"
        ]
    },
    
    "البرمجة": {
        "answer": "البرمجة هي فن وعلم كتابة التعليمات للحاسوب لحل المشاكل وإنجاز المهام المختلفة. تعتبر البرمجة لغة العصر الحديث ومهارة أساسية في سوق العمل. تساعد في تطوير التطبيقات والمواقع والألعاب والأنظمة الذكية. تنمي التفكير المنطقي وحل المشاكل بطريقة إبداعية ومنهجية. هناك لغات برمجة متعددة مثل Python و JavaScript و Java و C++ كل منها له استخداماته الخاصة.",
        "related_topics": ["لغات البرمجة", "تطوير التطبيقات", "هندسة البرمجيات", "الخوارزميات"],
        "questions": [
            "هل تعرف أي لغة برمجة؟ أم تفكر في تعلم البرمجة؟",
            "ما نوع التطبيقات التي تود تطويرها لو تعلمت البرمجة؟",
            "هل تعتقد أن البرمجة صعبة أم يمكن لأي شخص تعلمها؟"
        ]
    },
    
    # ========== العلوم والطب ==========
    "الطب": {
        "answer": "الطب هو علم وفن تشخيص وعلاج والوقاية من الأمراض والإصابات. يهدف إلى الحفاظ على صحة الإنسان وتحسين جودة حياته. يشمل تخصصات متعددة مثل الطب الباطني والجراحة وطب الأطفال وطب القلب والأعصاب. تطور الطب بشكل هائل مع التقنيات الحديثة مثل الجراحة بالمنظار والطب النووي والعلاج الجيني. الطب الحديث يعتمد على الأدلة العلمية والبحوث المتقدمة لتقديم أفضل رعاية للمرضى.",
        "related_topics": ["التشخيص الطبي", "الجراحة", "الأدوية", "الطب الوقائي"],
        "questions": [
            "هل تفكر في دراسة الطب أم لديك اهتمام بالمجال الطبي؟",
            "ما رأيك في استخدام الذكاء الاصطناعي في التشخيص الطبي؟",
            "هل تؤمن بأهمية الطب الوقائي والفحوصات الدورية؟"
        ]
    },
    
    "الفيزياء": {
        "answer": "الفيزياء هي علم دراسة المادة والطاقة والحركة والقوى في الكون. تفسر الظواهر الطبيعية من الذرة الصغيرة إلى المجرات الكبيرة. تشمل فروعاً مثل الميكانيكا والكهرومغناطيسية والديناميكا الحرارية وفيزياء الكم والنسبية. الفيزياء أساس جميع التقنيات الحديثة من الحاسوب والهاتف إلى الطائرة والقمر الصناعي. علماء مثل نيوتن وأينشتاين وماكسويل غيروا فهمنا للكون.",
        "related_topics": ["الكهرومغناطيسية", "فيزياء الكم", "النسبية", "الطاقة"],
        "questions": [
            "أي فرع من الفيزياء يثير اهتمامك أكثر؟",
            "هل تجد صعوبة في فهم مفاهيم الفيزياء؟",
            "ما رأيك في نظرية النسبية لأينشتاين؟"
        ]
    },
    
    # ========== التاريخ والحضارة ==========
    "بيت الحكمة": {
        "answer": "بيت الحكمة في بغداد كان أعظم مراكز العلم والترجمة في التاريخ الإسلامي! تأسس في عهد الخليفة هارون الرشيد وازدهر في عهد المأمون في القرن التاسع الميلادي. كان مكتبة ضخمة ومركز ترجمة ومعهد بحثي في آن واحد. ترجم فيه علماء مسلمون ومسيحيون ويهود أعمال أرسطو وأفلاطون والعلماء اليونانيين والفرس والهنود إلى العربية. ساهم في نقل المعرفة الإنسانية وحفظها للأجيال القادمة. عمل فيه علماء عظام مثل الخوارزمي وبنو موسى والكندي.",
        "related_topics": ["الخوارزمي", "الترجمة", "العصر العباسي", "العلوم الإسلامية"],
        "questions": [
            "هل تعرف أسماء علماء مشهورين عملوا في بيت الحكمة مثل الخوارزمي؟",
            "ما رأيك في دور الحضارة الإسلامية في نقل العلوم للعالم؟",
            "هل تريد معرفة المزيد عن العصر الذهبي للحضارة الإسلامية؟"
        ]
    },
    
    "الحضارة الإسلامية": {
        "answer": "الحضارة الإسلامية لعبت دوراً عظيماً ومحورياً في نقل العلوم والمعرفة للعالم! كانت جسراً حضارياً بين الشرق والغرب، حيث ترجم العلماء المسلمون أعمال اليونان والفرس والهنود وأضافوا عليها إبداعاتهم الخاصة. أسسوا علوماً جديدة مثل الجبر والكيمياء والطب التجريبي. علماء مثل الخوارزمي وابن سينا والرازي وابن رشد أثروا على الحضارة الإنسانية. نقلت أوروبا هذه العلوم في عصر النهضة وبنت عليها حضارتها الحديثة.",
        "related_topics": ["ابن سينا", "ابن رشد", "الخوارزمي", "الأندلس"],
        "questions": [
            "هل تعرف إنجازات علماء مثل الخوارزمي في الرياضيات أو ابن سينا في الطب؟",
            "ما رأيك في تأثير الترجمة على تطور العلوم عبر التاريخ؟",
            "هل تريد معرفة المزيد عن العصر الذهبي للحضارة الإسلامية في الأندلس؟"
        ]
    },
    
    # ========== الصحة والرياضة ==========
    "الصحة": {
        "answer": "الصحة هي أغلى ما يملكه الإنسان وهي حالة من اكتمال السلامة البدنية والعقلية والاجتماعية وليس مجرد غياب المرض. للمحافظة على الصحة يجب ممارسة الرياضة بانتظام لمدة 30 دقيقة يومياً، وتناول الغذاء الصحي المتوازن الغني بالخضار والفواكه، والنوم 7-8 ساعات يومياً، وشرب الماء بكثرة، وتجنب التدخين والضغوط النفسية. الصحة النفسية لا تقل أهمية عن الصحة الجسدية.",
        "related_topics": ["التغذية", "الرياضة", "النوم", "الصحة النفسية"],
        "questions": [
            "ما هي عاداتك الصحية اليومية؟ هل تمارس الرياضة بانتظام؟",
            "ما نوع الطعام الصحي الذي تفضله؟ هل تطبخ في البيت؟",
            "كيف تتعامل مع الضغوط النفسية والتوتر في حياتك؟"
        ]
    },
    
    # ========== التعليم والثقافة ==========
    "التعليم": {
        "answer": "التعليم هو أساس التقدم والحضارة وحق أساسي لكل إنسان. يساعد في بناء شخصية الإنسان وتطوير قدراته العقلية والإبداعية. التعليم الجيد يفتح أبواب الفرص ويساعد في تحقيق الأحلام والطموحات. في العصر الحديث، أصبح التعليم الرقمي والتعلم عن بُعد جزءاً مهماً من منظومة التعليم، مما يتيح التعلم في أي وقت ومن أي مكان. التعليم المستمر ضروري في عصر التطور السريع للتقنية.",
        "related_topics": ["التعلم الرقمي", "المناهج", "المعلمون", "الجامعات"],
        "questions": [
            "ما هو مجال دراستك أو تخصصك؟ هل تحب ما تدرسه؟",
            "كيف تفضل التعلم - بالقراءة أم بمشاهدة الفيديوهات أم بالممارسة العملية؟",
            "هل تستخدم التطبيقات التعليمية أو المنصات الرقمية في التعلم؟"
        ]
    },
    
    # ========== الاقتصاد والأعمال ==========
    "الاقتصاد": {
        "answer": "الاقتصاد هو علم دراسة كيفية إنتاج وتوزيع واستهلاك السلع والخدمات في المجتمع. يتعامل مع الموارد المحدودة والحاجات اللامحدودة. يشمل الاقتصاد الجزئي الذي يدرس سلوك الأفراد والشركات، والاقتصاد الكلي الذي يدرس الاقتصاد ككل. العوامل الاقتصادية تؤثر على حياتنا اليومية من الأسعار والوظائف إلى النمو والتضخم. الاقتصاد الرقمي والتجارة الإلكترونية غيرا وجه الاقتصاد الحديث.",
        "related_topics": ["التضخم", "البطالة", "التجارة", "الاستثمار"],
        "questions": [
            "هل تتابع الأخبار الاقتصادية؟ ما رأيك في الوضع الاقتصادي الحالي؟",
            "هل لديك اهتمام بالاستثمار أو ريادة الأعمال؟",
            "ما تأثير التقنية على الاقتصاد في رأيك؟"
        ]
    },
    
    # ========== البيئة والطبيعة ==========
    "البيئة": {
        "answer": "البيئة هي كل ما يحيط بالإنسان من هواء وماء وتربة ونباتات وحيوانات. حماية البيئة ضرورية لضمان استمرار الحياة على الأرض. نواجه تحديات بيئية كبيرة مثل التغير المناخي والتلوث وانقراض الأنواع. الحلول تشمل استخدام الطاقة المتجددة وتقليل النفايات وإعادة التدوير والحفاظ على الغابات. كل فرد يمكنه المساهمة في حماية البيئة من خلال تغيير عاداته اليومية.",
        "related_topics": ["التغير المناخي", "الطاقة المتجددة", "التلوث", "إعادة التدوير"],
        "questions": [
            "ما الذي تفعله شخصياً لحماية البيئة؟",
            "هل تؤمن بخطورة التغير المناخي؟",
            "ما رأيك في استخدام الطاقة المتجددة؟"
        ]
    },

    # ========== المزيد من الدول العربية ==========
    "عاصمة الأردن": {
        "answer": "عاصمة المملكة الأردنية الهاشمية هي عمان الجميلة، المدينة البيضاء التي تقع على سبعة تلال. تشتهر عمان بتاريخها العريق الذي يمتد لآلاف السنين، حيث كانت تُعرف قديماً باسم 'ربة عمون'. تضم المدينة معالم تاريخية مهمة مثل المدرج الروماني وقلعة عمان وسبيل الحوريات. عمان مدينة حديثة ومتطورة تجمع بين الأصالة والمعاصرة، وتعتبر مركزاً اقتصادياً وثقافياً مهماً في المنطقة.",
        "related_topics": ["التاريخ الأردني", "البتراء", "العقبة", "الأردن"],
        "questions": [
            "هل زرت الأردن من قبل؟ ما رأيك في البتراء الوردية؟",
            "هل تعرف شيئاً عن تاريخ الأردن القديم والأنباط؟",
            "ما الذي تعرفه عن الضيافة الأردنية والثقافة الأردنية؟"
        ]
    },

    "عاصمة لبنان": {
        "answer": "عاصمة الجمهورية اللبنانية هي بيروت الجميلة، باريس الشرق كما تُلقب لجمالها وثقافتها المتنوعة. تقع على ساحل البحر الأبيض المتوسط وتشتهر بتاريخها العريق وحضارتها المتنوعة. بيروت مدينة الثقافة والفنون والأدب، موطن العديد من الشعراء والكتاب والفنانين العرب. تضم جامعات عريقة ومعالم تاريخية مهمة. رغم التحديات التي واجهتها، تبقى بيروت رمزاً للصمود والإبداع في العالم العربي.",
        "related_topics": ["الأدب اللبناني", "فيروز", "الجامعة الأمريكية", "لبنان"],
        "questions": [
            "هل تحب الموسيقى اللبنانية؟ من مطربك المفضل؟",
            "ما رأيك في الأدب اللبناني وكتاب مثل جبران خليل جبران؟",
            "هل تعرف شيئاً عن المطبخ اللبناني الشهير؟"
        ]
    },

    "عاصمة سوريا": {
        "answer": "عاصمة الجمهورية العربية السورية هي دمشق العريقة، أقدم عاصمة مأهولة في العالم والتي يمتد تاريخها لأكثر من 4000 سنة. تُلقب بالفيحاء وجوهرة الشرق، وتقع في جنوب غرب سوريا. دمشق مدينة تاريخية عظيمة شهدت حضارات متعددة وكانت مركزاً مهماً للتجارة والثقافة. تشتهر بالمسجد الأموي الكبير والبيوت الدمشقية التراثية والحرف اليدوية التقليدية مثل الموزاييك والنحاس المطعم.",
        "related_topics": ["المسجد الأموي", "الحرف الدمشقية", "التاريخ السوري", "سوريا"],
        "questions": [
            "هل تعرف شيئاً عن تاريخ دمشق العريق والحضارات التي مرت بها؟",
            "ما الذي تعرفه عن الحرف اليدوية الدمشقية الشهيرة؟",
            "هل سمعت عن المسجد الأموي وأهميته التاريخية؟"
        ]
    },

    # ========== علماء ومفكرون ==========
    "ابن سينا": {
        "answer": "أبو علي الحسين بن عبد الله بن سينا (980-1037م) هو أحد أعظم العلماء في التاريخ الإسلامي والإنساني! يُلقب بالشيخ الرئيس وأمير الأطباء. ألف أكثر من 200 كتاب في الطب والفلسفة والرياضيات والفلك. كتابه 'القانون في الطب' ظل يُدرّس في الجامعات الأوروبية لأكثر من 600 سنة. اكتشف العديد من الأمراض ووصف الدورة الدموية الصغرى. كان فيلسوفاً عظيماً أيضاً وأثر على الفكر الإسلامي والأوروبي.",
        "related_topics": ["الطب الإسلامي", "الفلسفة الإسلامية", "القانون في الطب", "العلماء المسلمون"],
        "questions": [
            "هل تعرف إنجازات ابن سينا الأخرى في الفلسفة والرياضيات؟",
            "ما رأيك في تأثير العلماء المسلمين على الطب الحديث؟",
            "هل تريد معرفة المزيد عن علماء الطب في الحضارة الإسلامية؟"
        ]
    },

    "الخوارزمي": {
        "answer": "محمد بن موسى الخوارزمي (780-850م) هو عالم رياضيات وفلك وجغرافيا عظيم، يُلقب بأبي الجبر! عمل في بيت الحكمة في بغداد وأسس علم الجبر الحديث. كتابه 'الكتاب المختصر في حساب الجبر والمقابلة' هو أول كتاب منهجي في الجبر. نقل الأرقام الهندية إلى العالم الإسلامي وطورها، وهي التي نستخدمها اليوم. وضع أسس علم الخوارزميات في الرياضيات والحاسوب. رسم خرائط دقيقة للعالم وحسب محيط الأرض بدقة مذهلة.",
        "related_topics": ["الجبر", "الخوارزميات", "بيت الحكمة", "الرياضيات الإسلامية"],
        "questions": [
            "هل تعرف أن كلمة 'خوارزمية' في البرمجة مشتقة من اسم الخوارزمي؟",
            "ما رأيك في إسهامات العلماء المسلمين في الرياضيات؟",
            "هل تريد معرفة المزيد عن تطور الأرقام عبر التاريخ؟"
        ]
    },

    # ========== الفضاء والفلك ==========
    "الفضاء": {
        "answer": "الفضاء هو الكون الواسع الذي يحتوي على المجرات والنجوم والكواكب وكل ما هو خارج الغلاف الجوي للأرض. استكشاف الفضاء من أعظم إنجازات البشرية، بدءاً من إرسال أول قمر صناعي 'سبوتنيك' عام 1957 وصولاً إلى هبوط الإنسان على القمر عام 1969. اليوم نستكشف المريخ ونبحث عن حياة في الكواكب الأخرى. الفضاء يحتوي على أسرار مذهلة مثل الثقوب السوداء والمادة المظلمة والطاقة المظلمة. دراسة الفضاء تساعدنا في فهم أصل الكون ومستقبل البشرية.",
        "related_topics": ["ناسا", "المريخ", "القمر", "المجرات", "الثقوب السوداء"],
        "questions": [
            "هل تحلم بالسفر إلى الفضاء يوماً ما؟",
            "ما رأيك في إمكانية وجود حياة على كواكب أخرى؟",
            "أي كوكب تود استكشافه أكثر - المريخ أم القمر؟"
        ]
    },

    # ========== الرياضة والألعاب ==========
    "كرة القدم": {
        "answer": "كرة القدم هي أشهر رياضة في العالم ويتابعها مليارات الأشخاص! تُلعب بين فريقين كل منهما 11 لاعباً، والهدف هو تسجيل أكبر عدد من الأهداف في مرمى الخصم. نشأت كرة القدم الحديثة في إنجلترا في القرن التاسع عشر. كأس العالم هو أهم بطولة في كرة القدم ويُقام كل 4 سنوات. كرة القدم تجمع الشعوب وتكسر الحواجز الثقافية واللغوية. نجوم مثل بيليه ومارادونا وميسي ورونالدو أصبحوا أساطير عالمية.",
        "related_topics": ["كأس العالم", "ميسي", "رونالدو", "الدوريات الأوروبية"],
        "questions": [
            "من هو لاعبك المفضل في كرة القدم؟",
            "أي فريق تشجع؟ وما ذكرياتك المفضلة معه؟",
            "هل تلعب كرة القدم أم تكتفي بالمشاهدة؟"
        ]
    },
    # ========== الثقافة والفنون ==========
    "الثقافة": {
        "answer": "الثقافة هي سلوكيات وتقاليد ومعتقدات وتقاليد وممارسات تتميز بها مجتمع ما. تتضمن الثقافة التراث الأصيل والتقاليد والتقاليد والممارسات التي تشكل هوية المجتمع. تختلف الثقافات من مجتمع إلى آخر، وتؤثر على التفكير والتصرف والتعبير. الثقافة هي جزء من التنوع البشري وgetSourceOfLife.",
        "related_topics": ["التاريخ", "الفنون", "اللغة", "العادات"],
        "questions": [
            "ما هو تأثير الثقافة على حياتك اليومية؟",
            "هل تهتم بتعلم الثقافات الأخرى؟",
            "ما الذي يميز ثقافة بلدك عن ثقافات أخرى؟"
        ]
    },
    # ========== السياسة والقانون ==========
    "السياسة": {
        "answer": "السياسة هي عملية تحديد القرارات والسياسات التي تؤثر على المجتمع. يتم تنظيمها من خلال قوانين وقواعد وإجراءات. تتضمن السياسة العديد من المجالات مثل الاقتصاد والأمن والتنظيم والتعليم والصحة والبيئة. تعتبر السياسة مهمة جدًا لأنها تؤثر على كيفية توزيع الموارد وتحديد الأهداف وتحملية المجتمع. السياسة هي أساسية في تحقيق أهداف المجتمع.",
        "related_topics": ["الاقتصاد", "القانون", "التنظيم", "التنوع"],
        "questions": [
            "ما هي وظائفك في سياسيتك؟",
            "هل تؤمن بالسياسة في مجالك؟",
            "كيف يمكننا تحقيق أهداف المجتمع معًا؟"
        ]
    },
    # ========== العلوم الطبيعية ==========
    "العلوم الطبيعية": {
        "answer": "العلوم الطبيعية هي دراسة الطبيعة والظواهر الطبيعية. تتضمن العلوم الطبيعية الفيزياء والكيمياء والبيولوجيا والجغرافيا والأحياء الدقيقة والأحياء المجهرية. تساعد العلوم الطبيعية على فهم الكون والطبيعة وتطوير تقنيات جديدة. تشمل التطبيقات العلوم الطبيعية في الطب والصناعة والبيئة والتنمية والتنمية المستدامة.",
        "related_topics": ["الفيزياء", "الكيمياء", "البيولوجيا", "الجغرافيا"],
        "questions": [
            "ما الذي يثير اهتمامك في العلوم الطبيعية؟",
            "هل ترغب في دراسة أحد العلوم الطبيعية؟",
            "ما رأيك في تطبيقات العلوم الطبيعية في حياتنا اليومية؟"
        ]
    },

    # ========== الطعام والمطبخ العربي ==========
    "المطبخ العربي": {
        "answer": "المطبخ العربي من أغنى وأتنوع المطابخ في العالم! يتميز بتنوع هائل من الأطباق والنكهات والتوابل العطرة. كل دولة عربية لها أطباقها المميزة: المنسف الأردني، الكبسة السعودية، الملوخية المصرية، الحمص والتبولة اللبنانية، المقلوبة الفلسطينية، والكسكس المغربي. يعتمد المطبخ العربي على مكونات طبيعية مثل الأرز واللحوم والخضار والبقوليات والتوابل مثل الهيل والقرفة والكمون. الضيافة العربية مشهورة عالمياً بكرمها وتنوع أطباقها.",
        "related_topics": ["الكبسة", "المنسف", "الحمص", "التوابل العربية"],
        "questions": [
            "ما هو طبقك العربي المفضل؟ هل تجيد طبخه؟",
            "هل جربت أطباق من دول عربية مختلفة؟ أيها أعجبك أكثر؟",
            "ما رأيك في انتشار المطبخ العربي عالمياً؟"
        ]
    },

    # ========== الفنون والموسيقى ==========
    "الموسيقى العربية": {
        "answer": "الموسيقى العربية تراث عريق وغني يمتد لآلاف السنين! تتميز بالمقامات الموسيقية المتنوعة والآلات التقليدية مثل العود والقانون والناي والدربكة. أنجبت الموسيقى العربية أساطير خالدة مثل أم كلثوم وفيروز ومحمد عبد الوهاب وعبد الحليم حافظ. كل منطقة عربية لها طابعها الموسيقي المميز: الطرب المصري، الموشحات الشامية، الموسيقى الأندلسية، الراي المغربي، والسامبا الخليجية. الموسيقى العربية تعبر عن المشاعر العميقة وتحكي قصص الحب والوطن والحياة.",
        "related_topics": ["أم كلثوم", "فيروز", "العود", "المقامات الموسيقية"],
        "questions": [
            "من هو مطربك العربي المفضل؟ ما أجمل أغنية سمعتها؟",
            "هل تعزف على آلة موسيقية عربية؟ أم تفكر في تعلم العزف؟",
            "ما رأيك في الموسيقى العربية الحديثة مقارنة بالكلاسيكية؟"
        ]
    },

    # ========== التكنولوجيا الحديثة ==========
    "الهواتف الذكية": {
        "answer": "الهواتف الذكية ثورة تقنية حقيقية غيرت حياتنا بشكل جذري! من مجرد أجهزة للاتصال إلى حاسوب صغير في جيبك يحتوي على كاميرا عالية الجودة وإنترنت وتطبيقات لا حصر لها. تطورت الهواتف الذكية بسرعة مذهلة من أول آيفون عام 2007 إلى الهواتف الحديثة بتقنيات الذكاء الاصطناعي والكاميرات المتعددة والشحن السريع. غيرت طريقة تواصلنا وتعلمنا وعملنا وتسوقنا. لكن مع الفوائد الكبيرة، هناك تحديات مثل إدمان الهواتف وتأثيرها على الصحة النفسية.",
        "related_topics": ["آيفون", "أندرويد", "التطبيقات", "وسائل التواصل"],
        "questions": [
            "كم ساعة تقضي يومياً على هاتفك؟ هل تشعر أنها كثيرة؟",
            "ما أكثر التطبيقات التي تستخدمها؟ وأيها الأكثر فائدة؟",
            "هل تعتقد أن الهواتف الذكية جعلت حياتنا أفضل أم أعقد؟"
        ]
    },

    # ========== علم النفس والسلوك ==========
    "علم النفس": {
        "answer": "علم النفس هو دراسة السلوك البشري والعمليات العقلية والمشاعر. يساعدنا في فهم كيف نفكر ونشعر ونتصرف، ولماذا نتخذ قرارات معينة. يشمل فروعاً متعددة مثل علم النفس التربوي والإكلينيكي والاجتماعي والمعرفي. علم النفس مفيد في العلاج النفسي وتحسين التعليم وفهم السلوك الاجتماعي وتطوير الذات. في العصر الحديث، يُستخدم علم النفس في تصميم التطبيقات والمواقع وفي التسويق والإدارة. فهم علم النفس يساعدنا في بناء علاقات أفضل وتحقيق السعادة والنجاح.",
        "related_topics": ["الصحة النفسية", "العلاج النفسي", "تطوير الذات", "السلوك البشري"],
        "questions": [
            "هل تهتم بعلم النفس؟ ما الجانب الأكثر إثارة لك فيه؟",
            "كيف تتعامل مع الضغوط النفسية والتوتر في حياتك؟",
            "هل تؤمن بأهمية العلاج النفسي والاستشارة النفسية؟"
        ]
    },

    # ========== الأدب والشعر ==========
    "الشعر العربي": {
        "answer": "الشعر العربي تراث أدبي عظيم يمتد لأكثر من 1500 سنة! من الشعر الجاهلي مع امرئ القيس وعنترة بن شداد، إلى العصر الإسلامي مع حسان بن ثابت، والعصر العباسي مع أبي نواس والمتنبي، وصولاً للعصر الحديث مع أحمد شوقي ونزار قباني ومحمود درويش. الشعر العربي يتميز بالبحور الشعرية والقوافي والصور البلاغية الجميلة. يعبر عن كل المشاعر الإنسانية من الحب والحزن والفرح والوطنية والحكمة. الشعر العربي أثر على الأدب العالمي وما زال يُدرّس في الجامعات حول العالم.",
        "related_topics": ["المتنبي", "نزار قباني", "محمود درويش", "البحور الشعرية"],
        "questions": [
            "هل تحب الشعر؟ من شاعرك المفضل؟",
            "هل تكتب الشعر أم تكتفي بقراءته؟",
            "ما أجمل بيت شعر سمعته أو قرأته؟"
        ]
    },

    # ========== الاختراعات والابتكار ==========
    "الاختراعات": {
        "answer": "الاختراعات هي محرك التقدم البشري! من اختراع العجلة والكتابة في العصور القديمة، إلى الطباعة والبخار في العصور الوسطى، وصولاً للكهرباء والحاسوب والإنترنت في العصر الحديث. كل اختراع غيّر مجرى التاريخ وحسّن حياة البشر. العلماء والمخترعون مثل أرخميدس وابن الهيثم وليوناردو دافنشي وإديسون وتسلا تركوا بصمات خالدة. اليوم نشهد اختراعات مذهلة في الذكاء الاصطناعي والطب والفضاء. الابتكار يحتاج للفضول والمثابرة والتفكير خارج الصندوق.",
        "related_topics": ["إديسون", "تسلا", "ابن الهيثم", "الابتكار"],
        "questions": [
            "ما أعظم اختراع في رأيك؟ وكيف غيّر العالم؟",
            "هل لديك فكرة اختراع تود تطويرها؟",
            "ما رأيك في مستقبل الاختراعات والتقنية؟"
        ]
    }
}

# دالة للبحث في قاعدة المعرفة
def search_knowledge(query, knowledge_base=COMPREHENSIVE_KNOWLEDGE):
    """البحث في قاعدة المعرفة"""
    query_lower = query.lower()
    results = []
    
    for key, data in knowledge_base.items():
        # البحث في المفتاح
        if any(word in query_lower for word in key.lower().split()):
            results.append((key, data, 'exact'))
        
        # البحث في المواضيع المرتبطة
        elif any(topic.lower() in query_lower for topic in data.get('related_topics', [])):
            results.append((key, data, 'related'))
    
    return results

# دالة للحصول على معلومات مرتبطة
def get_related_info(topic, knowledge_base=COMPREHENSIVE_KNOWLEDGE):
    """الحصول على معلومات مرتبطة بموضوع معين"""
    related = []
    for key, data in knowledge_base.items():
        if topic.lower() in [t.lower() for t in data.get('related_topics', [])]:
            related.append((key, data))
    return related
