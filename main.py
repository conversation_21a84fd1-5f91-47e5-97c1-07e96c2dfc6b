# -*- coding: utf-8 -*-
"""
سكربت لاختبار موديل "أمؤلي-T1" التفاعلي: يمكنك طرح أي جملة بالعربية
ليقوم الموديل بإكمالها باستخدام التعلم العميق.
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.models import load_model

# اسم الموديل وملفات الحفظ
MODEL_NAME = "أمؤلي-T1"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
MAX_SEQUENCE_LEN = 6  # يجب أن يتطابق مع ما استخدمته في التدريب

#############################
# دالة توليد النص (التكملة)
#############################

def generate_text(seed_text: str, next_words: int, model: tf.keras.Model,
                  tokenizer: Tokenizer, max_sequence_len: int) -> str:
    """
    يولّد نصًا بطول 'next_words' ابتداءً من 'seed_text' باستخدام الموديل المدرب.
    """
    output_text = seed_text
    for _ in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        predicted_probs = model.predict(token_list, verbose=0)[0]
        predicted_index = np.argmax(predicted_probs)
        predicted_word = tokenizer.index_word.get(predicted_index, "")
        if not predicted_word:
            break
        output_text += " " + predicted_word
    return output_text

#############################
# تحميل الموديل والـ Tokenizer
#############################

# تأكد من وجود الملفات
if not os.path.exists(MODEL_FILE) or not os.path.exists(TOKENIZER_FILE):
    print(f"❌ ملف الموديل ({MODEL_FILE}) أو ملف الـ Tokenizer ({TOKENIZER_FILE}) غير موجود.")
    print("   تأكد أنك درّبت النموذج وشغلت السكربت الأصلي أولاً.")
    exit()

# تحميل الـ Tokenizer
with open(TOKENIZER_FILE, "rb") as f:
    tokenizer = pickle.load(f)
print(f"✅ تم تحميل الـ Tokenizer من: {TOKENIZER_FILE}")

# تحميل الموديل
model = load_model(MODEL_FILE)
print(f"✅ تم تحميل الموديل من: {MODEL_FILE}")

#############################
# حلقة التفاعل مع المستخدم
#############################

print("\n🤖 موديل أمؤلي-T1 جاهز للإجابة!")
print("✏️ اكتب أي جملة بالعربية للحصول على تكملة (أو 'خروج' للخروج):\n")

while True:
    seed = input("> ")
    if seed.strip().lower() in ["خروج", "exit", "quit"]:
        print("✨ تم إنهاء التفاعل. إلى اللقاء!")
        break

    # توليد تكملة من 10 كلمات
    completion = generate_text(seed, next_words=10, model=model,
                               tokenizer=tokenizer, max_sequence_len=MAX_SEQUENCE_LEN)
    print(f"\n🔍 أمؤلي-T1 يكمل الجملة:\n{completion}\n")
