{"concepts": {"مرحبا": {"first_seen": "2025-06-06T14:38:56.618653", "frequency": 1, "contexts": ["مرحبا"], "related_concepts": []}, "فكر": {"first_seen": "2025-06-06T14:39:07.936128", "frequency": 2, "contexts": ["ف<PERSON>ر", "فكر الخوارزمي"], "related_concepts": []}, "مين": {"first_seen": "2025-06-06T14:39:25.748371", "frequency": 1, "contexts": ["مين انت"], "related_concepts": []}, "انت": {"first_seen": "2025-06-06T14:39:25.748371", "frequency": 2, "contexts": ["مين انت", "من انت"], "related_concepts": []}, "الخوارزمي": {"first_seen": "2025-06-06T14:39:42.561018", "frequency": 8, "contexts": ["طيب تعرف الخوارزمي", "من هو الخوارزمي", "فكر الخوارزمي", "من هو الخوارزمي؟", "الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر", "من هو الخوارزمي", "طيب تعرف الخوارزمي", "من عو طيب تعرف الخوارزمي"], "related_concepts": []}, "تعرف": {"first_seen": "2025-06-06T14:39:42.561018", "frequency": 3, "contexts": ["طيب تعرف الخوارزمي", "طيب تعرف الخوارزمي", "من عو طيب تعرف الخوارزمي"], "related_concepts": []}, "طيب": {"first_seen": "2025-06-06T14:39:42.561018", "frequency": 3, "contexts": ["طيب تعرف الخوارزمي", "طيب تعرف الخوارزمي", "من عو طيب تعرف الخوارزمي"], "related_concepts": []}, "سؤال": {"first_seen": "2025-06-06T14:42:24.602388", "frequency": 5, "contexts": ["سؤال", "سؤال", "سؤال", "سؤال العراق", "سؤال"], "related_concepts": []}, "الاصطناعي": {"first_seen": "2025-06-06T14:42:56.431830", "frequency": 2, "contexts": ["ناقش الذكاء الاصطناعي", "تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري"], "related_concepts": []}, "ناقش": {"first_seen": "2025-06-06T14:42:56.432380", "frequency": 3, "contexts": ["ناقش الذكاء الاصطناعي", "ناقش العراق", "ناقش الفيزياء"], "related_concepts": []}, "الذكاء": {"first_seen": "2025-06-06T14:42:56.432526", "frequency": 2, "contexts": ["ناقش الذكاء الاصطناعي", "تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري"], "related_concepts": []}, "لحل": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 1, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل"], "related_concepts": []}, "للحاسوب": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 1, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل"], "related_concepts": []}, "التعليمات": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 1, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل"], "related_concepts": []}, "تعلم": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 9, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل", "تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري", "تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر", "تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات", "تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري", "تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس", "تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض", "تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية", "تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "البرمجة": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 2, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل", "تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات"], "related_concepts": []}, "كتابة": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 1, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل"], "related_concepts": []}, "المشاكل": {"first_seen": "2025-06-06T14:43:32.607935", "frequency": 1, "contexts": ["تعلم البرمجة هي كتابة التعليمات للحاسوب لحل المشاكل"], "related_concepts": []}, "العراق": {"first_seen": "2025-06-06T14:46:44.399252", "frequency": 3, "contexts": ["سؤال العراق", "ناقش العراق", "هل العراق عاصمة بغداد صحيح؟"], "related_concepts": []}, "دراسة": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 1, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري"], "related_concepts": []}, "المستوى": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 1, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري"], "related_concepts": []}, "الذري": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 1, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري"], "related_concepts": []}, "علم": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 2, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري", "الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر"], "related_concepts": []}, "المادة": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 2, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري", "تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "الفيزياء": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 4, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري", "الفيزياء", "الفيزياء الكمية", "ناقش الفيزياء"], "related_concepts": []}, "الكمية": {"first_seen": "2025-06-06T14:49:15.595582", "frequency": 2, "contexts": ["تعلم الفيزياء الكمية هي علم دراسة المادة على المستوى الذري", "الفيزياء الكمية"], "related_concepts": []}, "الثاني": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "القرن": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "عظيم": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "أندلسي": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "رشد": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "عشر": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "وطبيب": {"first_seen": "2025-06-06T14:49:17.448297", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "فيلسوف": {"first_seen": "2025-06-06T14:49:17.451580", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "ابن": {"first_seen": "2025-06-06T14:49:17.451580", "frequency": 1, "contexts": ["تعلم ابن رشد هو فيلسوف وطبيب أندلسي عظيم من القرن الثاني عشر"], "related_concepts": []}, "لتطوير": {"first_seen": "2025-06-06T14:49:19.287446", "frequency": 1, "contexts": ["تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات"], "related_concepts": []}, "التطبيقات": {"first_seen": "2025-06-06T14:49:19.287446", "frequency": 1, "contexts": ["تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات"], "related_concepts": []}, "ومفيدة": {"first_seen": "2025-06-06T14:49:19.287446", "frequency": 1, "contexts": ["تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات"], "related_concepts": []}, "بلغة": {"first_seen": "2025-06-06T14:49:19.287446", "frequency": 1, "contexts": ["تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات"], "related_concepts": []}, "سهلة": {"first_seen": "2025-06-06T14:49:19.287446", "frequency": 1, "contexts": ["تعلم البرمجة بلغة Python سهلة ومفيدة لتطوير التطبيقات"], "related_concepts": []}, "يستخدم": {"first_seen": "2025-06-06T14:49:23.074518", "frequency": 2, "contexts": ["تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري", "الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "التفكير": {"first_seen": "2025-06-06T14:49:23.074518", "frequency": 1, "contexts": ["تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري"], "related_concepts": []}, "البشري": {"first_seen": "2025-06-06T14:49:23.074518", "frequency": 2, "contexts": ["تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري", "الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "لمحاكاة": {"first_seen": "2025-06-06T14:49:23.074518", "frequency": 1, "contexts": ["تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري"], "related_concepts": []}, "الخوارزميات": {"first_seen": "2025-06-06T14:49:23.074518", "frequency": 1, "contexts": ["تعلم الذكاء الاصطناعي يستخدم الخوارزميات لمحاكاة التفكير البشري"], "related_concepts": []}, "الليزر": {"first_seen": "2025-06-06T14:50:15.904679", "frequency": 2, "contexts": ["ما هو الليزر؟", "الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "مكثف": {"first_seen": "2025-06-06T14:50:17.726610", "frequency": 1, "contexts": ["الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "ضوء": {"first_seen": "2025-06-06T14:50:17.726610", "frequency": 1, "contexts": ["الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "الطب": {"first_seen": "2025-06-06T14:50:17.726610", "frequency": 1, "contexts": ["الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "والصناعة": {"first_seen": "2025-06-06T14:50:17.726610", "frequency": 1, "contexts": ["الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "ومركز": {"first_seen": "2025-06-06T14:50:17.726610", "frequency": 1, "contexts": ["الليزر هو ضوء مكثف ومركز يستخدم في الطب والصناعة"], "related_concepts": []}, "أسس": {"first_seen": "2025-06-06T14:50:20.337357", "frequency": 1, "contexts": ["الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر"], "related_concepts": []}, "مسلم": {"first_seen": "2025-06-06T14:50:20.337357", "frequency": 1, "contexts": ["الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر"], "related_concepts": []}, "عالم": {"first_seen": "2025-06-06T14:50:20.337357", "frequency": 1, "contexts": ["الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر"], "related_concepts": []}, "رياضيات": {"first_seen": "2025-06-06T14:50:20.337357", "frequency": 1, "contexts": ["الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر"], "related_concepts": []}, "الجبر": {"first_seen": "2025-06-06T14:50:20.337357", "frequency": 1, "contexts": ["الخوارزمي هو عالم رياضيات مسلم أسس علم الجبر"], "related_concepts": []}, "تعمل": {"first_seen": "2025-06-06T14:50:21.164403", "frequency": 1, "contexts": ["كيف تعمل الشبكات العصبية؟"], "related_concepts": []}, "الشبكات": {"first_seen": "2025-06-06T14:50:21.164403", "frequency": 3, "contexts": ["كيف تعمل الشبكات العصبية؟", "الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات", "الشبكات"], "related_concepts": []}, "كيف": {"first_seen": "2025-06-06T14:50:21.164403", "frequency": 1, "contexts": ["كيف تعمل الشبكات العصبية؟"], "related_concepts": []}, "العصبية": {"first_seen": "2025-06-06T14:50:21.164403", "frequency": 2, "contexts": ["كيف تعمل الشبكات العصبية؟", "الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "عمل": {"first_seen": "2025-06-06T14:50:25.951356", "frequency": 1, "contexts": ["الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "الدماغ": {"first_seen": "2025-06-06T14:50:25.951356", "frequency": 1, "contexts": ["الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "المعلومات": {"first_seen": "2025-06-06T14:50:25.951356", "frequency": 1, "contexts": ["الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "تحاكي": {"first_seen": "2025-06-06T14:50:25.951356", "frequency": 1, "contexts": ["الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "معالجة": {"first_seen": "2025-06-06T14:50:25.951356", "frequency": 1, "contexts": ["الشبكات العصبية تحاكي عمل الدماغ البشري في معالجة المعلومات"], "related_concepts": []}, "ونظيفة": {"first_seen": "2025-06-06T14:51:03.085669", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "الطاقة": {"first_seen": "2025-06-06T14:51:03.085669", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "طاقة": {"first_seen": "2025-06-06T14:51:03.085669", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "الشمس": {"first_seen": "2025-06-06T14:51:03.085669", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "مصدر": {"first_seen": "2025-06-06T14:51:03.086668", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "متجددة": {"first_seen": "2025-06-06T14:51:03.086668", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "الشمسية": {"first_seen": "2025-06-06T14:51:03.086668", "frequency": 1, "contexts": ["تعلم الطاقة الشمسية مصدر طاقة متجددة ونظيفة من الشمس"], "related_concepts": []}, "قوة": {"first_seen": "2025-06-06T14:51:13.255618", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "بعضها": {"first_seen": "2025-06-06T14:51:13.255618", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "تجذب": {"first_seen": "2025-06-06T14:51:13.255618", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "الأجسام": {"first_seen": "2025-06-06T14:51:13.255618", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "نحو": {"first_seen": "2025-06-06T14:51:13.255618", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "الجاذبية": {"first_seen": "2025-06-06T14:51:13.256615", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "البعض": {"first_seen": "2025-06-06T14:51:13.256615", "frequency": 1, "contexts": ["تعلم الجاذبية قوة تجذب الأجسام نحو بعضها البعض"], "related_concepts": []}, "ألف": {"first_seen": "2025-06-06T14:51:14.089844", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "بسرعة": {"first_seen": "2025-06-06T14:51:14.190255", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "كهرومغناطيسية": {"first_seen": "2025-06-06T14:51:14.307865", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "الضوء": {"first_seen": "2025-06-06T14:51:14.377790", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "موجة": {"first_seen": "2025-06-06T14:51:14.391721", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "ثانية": {"first_seen": "2025-06-06T14:51:14.393706", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "تنتقل": {"first_seen": "2025-06-06T14:51:14.395736", "frequency": 1, "contexts": ["تعلم الضوء موجة كهرومغناطيسية تنتقل بسرعة 300 ألف كم/ثانية"], "related_concepts": []}, "نواة": {"first_seen": "2025-06-06T14:51:15.377404", "frequency": 1, "contexts": ["تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "وحدة": {"first_seen": "2025-06-06T14:51:15.377404", "frequency": 1, "contexts": ["تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "وإلكترونات": {"first_seen": "2025-06-06T14:51:15.377404", "frequency": 1, "contexts": ["تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "تتكون": {"first_seen": "2025-06-06T14:51:15.377404", "frequency": 1, "contexts": ["تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "أصغر": {"first_seen": "2025-06-06T14:51:15.377404", "frequency": 1, "contexts": ["تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "الذرة": {"first_seen": "2025-06-06T14:51:15.377404", "frequency": 1, "contexts": ["تعلم الذرة أصغر وحدة في المادة تتكون من نواة وإلكترونات"], "related_concepts": []}, "ذاكؤاي": {"first_seen": "2025-06-06T14:51:38.727380", "frequency": 1, "contexts": ["ذاكؤاي"], "related_concepts": []}, "صحيح": {"first_seen": "2025-06-06T14:54:33.763894", "frequency": 1, "contexts": ["هل العراق عاصمة بغداد صحيح؟"], "related_concepts": []}, "عاصمة": {"first_seen": "2025-06-06T14:54:33.763894", "frequency": 1, "contexts": ["هل العراق عاصمة بغداد صحيح؟"], "related_concepts": []}, "بغداد": {"first_seen": "2025-06-06T14:54:33.763894", "frequency": 1, "contexts": ["هل العراق عاصمة بغداد صحيح؟"], "related_concepts": []}, "الشغل": {"first_seen": "2025-06-06T15:30:50.703878", "frequency": 1, "contexts": ["وين الشغل"], "related_concepts": []}, "وين": {"first_seen": "2025-06-06T15:30:50.703878", "frequency": 1, "contexts": ["وين الشغل"], "related_concepts": []}, "شنو": {"first_seen": "2025-06-06T15:32:19.515789", "frequency": 1, "contexts": ["شنو"], "related_concepts": []}, "اخروج": {"first_seen": "2025-06-06T15:33:00.788878", "frequency": 1, "contexts": ["اخروج"], "related_concepts": []}, "انتد": {"first_seen": "2025-06-06T15:36:47.995541", "frequency": 1, "contexts": ["من انتد"], "related_concepts": []}}, "relationships": {}, "learned_facts": {}, "patterns": {}, "insights": [{"insight": "تعلمت من مناقشة فكر أهمية التفكير متعدد الأوجه", "topic": "ف<PERSON>ر", "generated_at": "2025-06-06T14:39:12.641535"}, {"insight": "تعلمت من مناقشة فكر الخوارزمي أهمية التفكير متعدد الأوجه", "topic": "فكر الخوارزمي", "generated_at": "2025-06-06T14:39:57.766616"}, {"insight": "تعلمت من مناقشة ناقش الذكاء الاصطناعي أهمية التفكير متعدد الأوجه", "topic": "ناقش الذكاء الاصطناعي", "generated_at": "2025-06-06T14:43:01.177494"}, {"insight": "تعلمت من مناقشة ناقش العراق أهمية التفكير متعدد الأوجه", "topic": "ناقش العراق", "generated_at": "2025-06-06T14:47:37.395177"}, {"insight": "تعلمت من مناقشة ناقش الفيزياء أهمية التفكير متعدد الأوجه", "topic": "ناقش الفيزياء", "generated_at": "2025-06-06T14:51:21.944479"}], "casual_responses": {"السلام عليكم": ["وعليكم السلام ورحمة الله وبركاته", "وعليكم السلام، أهلاً وسهلاً", "وعليكم السلام، مرحباً بك"], "سلام عليكم": ["وعليكم السلام ورحمة الله", "وعليكم السلام، أهلاً بك"], "مرحبا": ["أهلاً وسهلاً بك! 😊", "مرحباً! كيف حالك؟", "أهلاً بك، نورت!"], "مرحباً": ["أهلاً وسهلاً! 🌟", "مرحباً بك، كيف يمكنني مساعدتك؟"], "هلا": ["هلا والله! أهلاً بك", "هلا فيك! كيف الأحوال؟"], "اهلا": ["أهلاً وسهلاً! مرحباً بك", "اهلين فيك!"], "اهلين": ["اهلين فيك! نورت", "اهلا وسهلا بك"], "كيف حالك": ["الحمد لله، تمام! وأنت كيف حالك؟", "بخير والحمد لله، شكراً لسؤالك", "ممتاز! أتعلم وأتطور كل يوم"], "شلونك": ["زين الحمد لله! وأنت شلونك؟", "تمام، كله زين! شكراً"], "كيفك": ["منيح الحمد لله! وأنت كيفك؟", "تمام، شكراً لسؤالك"], "وش أخبارك": ["أخبار زينة الحمد لله! وأنت وش أخبارك؟", "كله تمام، أتعلم أشياء جديدة"], "ايش اخبارك": ["أخبار حلوة! أتعلم كل يوم شيء جديد", "كله منيح، شكراً لسؤالك"], "شكرا": ["العفو! لا شكر على واجب", "أهلاً وسهلاً، دايماً في الخدمة", "تسلم، هذا واجبي"], "شكراً": ["العفو! 😊", "لا شكر على واجب", "أهلاً وسهلاً بك"], "مشكور": ["الله يعافيك! تسلم", "أهلاً وسهلاً، هذا واجبي"], "يعطيك العافية": ["الله يعافيك! 💪", "ويعطيك العافية", "تسلم يا رب"], "تسلم": ["الله يسلمك! شكراً", "تسلم، وأنت كمان"], "صباح الخير": ["صباح النور! كيف حالك اليوم؟", "صباح الخير والسرور! 🌅", "صباح النور، يوم سعيد إن شاء الله"], "مساء الخير": ["مساء النور! كيف كان يومك؟", "مساء الخير والسرور! 🌙", "مساء النور، أتمنى يومك كان حلو"], "تصبح على خير": ["وأنت بخير! أحلام سعيدة", "تص<PERSON>ح على خير، نوم هنيء", "وأنت بألف خير! 🌙"], "مع السلامة": ["مع السلامة! في أمان الله", "الله معك، مع السلامة", "في أمان الله! 👋"], "باي": ["باي باي! أراك قريباً", "مع السلامة! 👋"], "وداعا": ["وداعاً! أتطلع لرؤيتك مرة أخرى", "في أمان الله!"], "آسف": ["لا بأس، ما صار شي", "عادي، مافي مشكلة", "الله يعافيك، عادي"], "معذرة": ["لا بأس عليك، عادي", "ما عليك زود، مافي مشكلة"], "اعتذر": ["لا داعي للاعتذار، كله تمام", "عادي، مافي شي"], "نعم": ["تمام! 👍", "زين، فهمت", "أوكي!"], "أيوة": ["تمام! فهمت", "زين، واضح"], "اوكي": ["أوكي! 👌", "تمام، فهمت"], "تمام": ["تمام تمام! 😊", "زين، كله واضح"], "لا": ["لا بأس، كما تشاء", "تمام، مافي مشكلة"], "لا شكرا": ["لا بأس، كما تريد", "تمام، أي وقت تحتاج شيء قولي"], "ممكن تساعدني": ["طبعاً! كيف أقدر أساعدك؟", "أكيد، أنا هنا للمساعدة", "بكل سرور، وش محتاج؟"], "عندي سؤال": ["تفضل، اسأل وأنا أجاوبك", "أنا جاهز للإجابة على أسئلتك", "تفضل، كلي آذان صاغية"], "ما فهمت": ["خليني أشرح لك بطريقة أوضح", "ما عليه، أقدر أشرح مرة ثانية", "دعني أحاول التوضيح بشكل أفضل"], "ممتاز": ["يسعدني أني قدرت أساعد! 😊", "شكراً لك! أحاول دائماً تقديم الأفضل", "هذا يسعدني كثيراً!"], "رائع": ["شكراً لك! 🌟", "سعيد أن هذا أعجبك", "يسعدني سماع هذا!"], "أحسنت": ["شكراً جزيلاً! 👏", "هذا من ذوقك", "أشكرك على هذا التقدير"], "بالتوفيق": ["الله يوفقك! 🙏", "شكراً لك، ولك بالمثل", "الله يوفق الجميع"], "الله يسعدك": ["ويسعدك يارب! 💝", "الله يسعد أيامك", "تسلم، الله يسعد الجميع"], "الله يخليك": ["ويخليك يارب! 🤲", "تسلم، الله يحفظك", "الله يخلي لك أحبابك"], "الله يعطيك العافية": ["ويعطيك العافية! 💪", "الله يعافيك ويسلمك", "تسلم، الله يقويك"], "الله يوفقك": ["آمين وياك! 🤲", "ولك بالمثل يارب", "الله يوفقنا جميعاً"], "يا هلا": ["هلا وغلا! 🌺", "يا هلا ومرحبا", "هلا بك وبمن معك"], "يا مرحبا": ["مراحب! نورتنا 🌟", "يا مرحبا بك وبمن معك", "مرحبتين وثلاث!"], "صباحو": ["صباح النور والسرور! 🌅", "صباحك عسل", "يا صباح الخير والبركة"], "مساءو": ["مساء النور والسرور! 🌙", "مساءك عسل", "يا مساء الخير والبركة"], "تسلم يدينك": ["الله يسلمك! 🙌", "تسلم من كل شر", "حياك الله"], "الله يسلمك": ["ويسلمك يارب! 🤲", "تسلم من كل شر", "الله يحفظك"], "ما قصرت": ["العفو! هذا واجبي 🙏", "تسلم، ما سوينا شي", "الله يسعدك"], "بارك الله فيك": ["وفيك بارك الله! 🤲", "آمين وإياك", "جزاك الله خير"], "جزاك الله خير": ["وإياك! جزاك الله خير 🤲", "وجزاك مثله وأكثر", "الله يجزيك كل خير"], "الله يجزاك خير": ["وياك يارب! 🤲", "ويجزيك خير", "آمين يارب العالمين"], "تسلم على هذا": ["الله يسلمك! العفو 🙏", "حياك الله، ما سوينا شي", "تسلم من كل شر"], "يعجبني هذا": ["حلو! يسعدني هذا 😊", "شكراً لك! هذا يشجعني", "يسعدني أن هذا أعجبك"], "حلو": ["شكراً لك! 😊", "يسعدني هذا", "حلو تواجدك"], "جميل": ["شكراً جزيلاً! 🌟", "يسعدني أن هذا أعجبك", "أنت الأجمل"], "عجيب": ["شكراً لك! 🌟", "يسعدني أن هذا أثار إعجابك", "أحاول دائماً التميز"], "واو": ["شكراً لك! 🌟", "يسعدني ردة فعلك", "هذا من ذوقك"], "برافو": ["شكراً جزيلاً! 👏", "تسلم على التشجيع", "هذا من ذوقك"], "أشكرك": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "شكراً جزيلاً": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "شكراً لك": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"]}, "facts": {"casual_response_1": {"type": "casual_response", "trigger": "السلام عليكم", "responses": ["وعليكم السلام ورحمة الله وبركاته", "وعليكم السلام، أهلاً وسهلاً", "وعليكم السلام، مرحباً بك"], "learned_at": "2025-06-06T15:29:28.539772", "category": "تحيات وردود عامية"}, "casual_response_2": {"type": "casual_response", "trigger": "سلام عليكم", "responses": ["وعليكم السلام ورحمة الله", "وعليكم السلام، أهلاً بك"], "learned_at": "2025-06-06T15:29:28.539772", "category": "تحيات وردود عامية"}, "casual_response_3": {"type": "casual_response", "trigger": "مرحبا", "responses": ["أهلاً وسهلاً بك! 😊", "مرحباً! كيف حالك؟", "أهلاً بك، نورت!"], "learned_at": "2025-06-06T15:29:28.539772", "category": "تحيات وردود عامية"}, "casual_response_4": {"type": "casual_response", "trigger": "مرحباً", "responses": ["أهلاً وسهلاً! 🌟", "مرحباً بك، كيف يمكنني مساعدتك؟"], "learned_at": "2025-06-06T15:29:28.539772", "category": "تحيات وردود عامية"}, "casual_response_5": {"type": "casual_response", "trigger": "هلا", "responses": ["هلا والله! أهلاً بك", "هلا فيك! كيف الأحوال؟"], "learned_at": "2025-06-06T15:29:28.539772", "category": "تحيات وردود عامية"}, "casual_response_6": {"type": "casual_response", "trigger": "اهلا", "responses": ["أهلاً وسهلاً! مرحباً بك", "اهلين فيك!"], "learned_at": "2025-06-06T15:29:28.544832", "category": "تحيات وردود عامية"}, "casual_response_7": {"type": "casual_response", "trigger": "اهلين", "responses": ["اهلين فيك! نورت", "اهلا وسهلا بك"], "learned_at": "2025-06-06T15:29:28.544832", "category": "تحيات وردود عامية"}, "casual_response_8": {"type": "casual_response", "trigger": "<PERSON><PERSON><PERSON> حال<PERSON>", "responses": ["الحمد لله، تمام! وأنت كيف حالك؟", "بخير والحمد لله، شكراً لسؤالك", "ممتاز! أتعلم وأتطور كل يوم"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_9": {"type": "casual_response", "trigger": "شلونك", "responses": ["زين الحمد لله! وأنت شلونك؟", "تمام، كله زين! شكراً"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_10": {"type": "casual_response", "trigger": "كيفك", "responses": ["منيح الحمد لله! وأنت كيفك؟", "تمام، شكراً لسؤالك"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_11": {"type": "casual_response", "trigger": "وش أخبارك", "responses": ["أخبار زينة الحمد لله! وأنت وش أخبارك؟", "كله تمام، أتعلم أشياء جديدة"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_12": {"type": "casual_response", "trigger": "ايش اخبارك", "responses": ["أخبار حلوة! أتعلم كل يوم شيء جديد", "كله منيح، شكراً لسؤالك"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_13": {"type": "casual_response", "trigger": "شكرا", "responses": ["العفو! لا شكر على واجب", "أهلاً وسهلاً، دايماً في الخدمة", "تسلم، هذا واجبي"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_14": {"type": "casual_response", "trigger": "شكراً", "responses": ["العفو! 😊", "لا شكر على واجب", "أهلاً وسهلاً بك"], "learned_at": "2025-06-06T15:29:28.547451", "category": "تحيات وردود عامية"}, "casual_response_15": {"type": "casual_response", "trigger": "مشكور", "responses": ["الله يعافيك! تسلم", "أهلاً وسهلاً، هذا واجبي"], "learned_at": "2025-06-06T15:29:28.555456", "category": "تحيات وردود عامية"}, "casual_response_16": {"type": "casual_response", "trigger": "يعطيك العافية", "responses": ["الله يعافيك! 💪", "ويعطيك العافية", "تسلم يا رب"], "learned_at": "2025-06-06T15:29:28.598331", "category": "تحيات وردود عامية"}, "casual_response_17": {"type": "casual_response", "trigger": "تسلم", "responses": ["الله يسلمك! شكراً", "تسلم، وأنت كمان"], "learned_at": "2025-06-06T15:29:28.603170", "category": "تحيات وردود عامية"}, "casual_response_18": {"type": "casual_response", "trigger": "ص<PERSON><PERSON><PERSON> الخير", "responses": ["صباح النور! كيف حالك اليوم؟", "صباح الخير والسرور! 🌅", "صباح النور، يوم سعيد إن شاء الله"], "learned_at": "2025-06-06T15:29:28.684850", "category": "تحيات وردود عامية"}, "casual_response_19": {"type": "casual_response", "trigger": "مساء الخير", "responses": ["مساء النور! كيف كان يومك؟", "مساء الخير والسرور! 🌙", "مساء النور، أتمنى يومك كان حلو"], "learned_at": "2025-06-06T15:29:28.695189", "category": "تحيات وردود عامية"}, "casual_response_20": {"type": "casual_response", "trigger": "تص<PERSON><PERSON> ع<PERSON>ى خير", "responses": ["وأنت بخير! أحلام سعيدة", "تص<PERSON>ح على خير، نوم هنيء", "وأنت بألف خير! 🌙"], "learned_at": "2025-06-06T15:29:28.833201", "category": "تحيات وردود عامية"}, "casual_response_21": {"type": "casual_response", "trigger": "مع السلامة", "responses": ["مع السلامة! في أمان الله", "الله معك، مع السلامة", "في أمان الله! 👋"], "learned_at": "2025-06-06T15:29:28.841204", "category": "تحيات وردود عامية"}, "casual_response_22": {"type": "casual_response", "trigger": "باي", "responses": ["باي باي! أراك قريباً", "مع السلامة! 👋"], "learned_at": "2025-06-06T15:29:28.891393", "category": "تحيات وردود عامية"}, "casual_response_23": {"type": "casual_response", "trigger": "وداعا", "responses": ["وداعاً! أتطلع لرؤيتك مرة أخرى", "في أمان الله!"], "learned_at": "2025-06-06T15:29:28.940921", "category": "تحيات وردود عامية"}, "casual_response_24": {"type": "casual_response", "trigger": "آسف", "responses": ["لا بأس، ما صار شي", "عادي، مافي مشكلة", "الله يعافيك، عادي"], "learned_at": "2025-06-06T15:29:28.940921", "category": "تحيات وردود عامية"}, "casual_response_25": {"type": "casual_response", "trigger": "معذرة", "responses": ["لا بأس عليك، عادي", "ما عليك زود، مافي مشكلة"], "learned_at": "2025-06-06T15:29:28.940921", "category": "تحيات وردود عامية"}, "casual_response_26": {"type": "casual_response", "trigger": "اعتذر", "responses": ["لا داعي للاعتذار، كله تمام", "عادي، مافي شي"], "learned_at": "2025-06-06T15:29:28.953031", "category": "تحيات وردود عامية"}, "casual_response_27": {"type": "casual_response", "trigger": "نعم", "responses": ["تمام! 👍", "زين، فهمت", "أوكي!"], "learned_at": "2025-06-06T15:29:28.953031", "category": "تحيات وردود عامية"}, "casual_response_28": {"type": "casual_response", "trigger": "أيوة", "responses": ["تمام! فهمت", "زين، واضح"], "learned_at": "2025-06-06T15:29:28.953031", "category": "تحيات وردود عامية"}, "casual_response_29": {"type": "casual_response", "trigger": "اوكي", "responses": ["أوكي! 👌", "تمام، فهمت"], "learned_at": "2025-06-06T15:29:28.953031", "category": "تحيات وردود عامية"}, "casual_response_30": {"type": "casual_response", "trigger": "تمام", "responses": ["تمام تمام! 😊", "زين، كله واضح"], "learned_at": "2025-06-06T15:29:28.953031", "category": "تحيات وردود عامية"}, "casual_response_31": {"type": "casual_response", "trigger": "لا", "responses": ["لا بأس، كما تشاء", "تمام، مافي مشكلة"], "learned_at": "2025-06-06T15:29:28.964580", "category": "تحيات وردود عامية"}, "casual_response_32": {"type": "casual_response", "trigger": "لا شكرا", "responses": ["لا بأس، كما تريد", "تمام، أي وقت تحتاج شيء قولي"], "learned_at": "2025-06-06T15:29:28.964580", "category": "تحيات وردود عامية"}, "casual_response_33": {"type": "casual_response", "trigger": "ممكن تساعدني", "responses": ["طبعاً! كيف أقدر أساعدك؟", "أكيد، أنا هنا للمساعدة", "بكل سرور، وش محتاج؟"], "learned_at": "2025-06-06T15:29:28.964580", "category": "تحيات وردود عامية"}, "casual_response_34": {"type": "casual_response", "trigger": "عندي سؤال", "responses": ["تفضل، اسأل وأنا أجاوبك", "أنا جاهز للإجابة على أسئلتك", "تفضل، كلي آذان صاغية"], "learned_at": "2025-06-06T15:29:28.964580", "category": "تحيات وردود عامية"}, "casual_response_35": {"type": "casual_response", "trigger": "ما فهمت", "responses": ["خليني أشرح لك بطريقة أوضح", "ما عليه، أقدر أشرح مرة ثانية", "دعني أحاول التوضيح بشكل أفضل"], "learned_at": "2025-06-06T15:29:28.964580", "category": "تحيات وردود عامية"}, "casual_response_36": {"type": "casual_response", "trigger": "مم<PERSON><PERSON><PERSON>", "responses": ["يسعدني أني قدرت أساعد! 😊", "شكراً لك! أحاول دائماً تقديم الأفضل", "هذا يسعدني كثيراً!"], "learned_at": "2025-06-06T15:29:28.972574", "category": "تحيات وردود عامية"}, "casual_response_37": {"type": "casual_response", "trigger": "رائع", "responses": ["شكراً لك! 🌟", "سعيد أن هذا أعجبك", "يسعدني سماع هذا!"], "learned_at": "2025-06-06T15:29:28.988377", "category": "تحيات وردود عامية"}, "casual_response_38": {"type": "casual_response", "trigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": ["شكراً جزيلاً! 👏", "هذا من ذوقك", "أشكرك على هذا التقدير"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_39": {"type": "casual_response", "trigger": "بالتوفيق", "responses": ["الله يوفقك! 🙏", "شكراً لك، ولك بالمثل", "الله يوفق الجميع"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_40": {"type": "casual_response", "trigger": "الله يسعدك", "responses": ["ويسعدك يارب! 💝", "الله يسعد أيامك", "تسلم، الله يسعد الجميع"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_41": {"type": "casual_response", "trigger": "الله يخليك", "responses": ["ويخليك يارب! 🤲", "تسلم، الله يحفظك", "الله يخلي لك أحبابك"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_42": {"type": "casual_response", "trigger": "الله يعطيك العافية", "responses": ["ويعطيك العافية! 💪", "الله يعافيك ويسلمك", "تسلم، الله يقويك"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_43": {"type": "casual_response", "trigger": "الله يوفقك", "responses": ["آمين وياك! 🤲", "ولك بالمثل يارب", "الله يوفقنا جميعاً"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_44": {"type": "casual_response", "trigger": "يا هلا", "responses": ["هلا وغلا! 🌺", "يا هلا ومرحبا", "هلا بك وبمن معك"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_45": {"type": "casual_response", "trigger": "يا مرحبا", "responses": ["مراحب! نورتنا 🌟", "يا مرحبا بك وبمن معك", "مرحبتين وثلاث!"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_46": {"type": "casual_response", "trigger": "صباحو", "responses": ["صباح النور والسرور! 🌅", "صباحك عسل", "يا صباح الخير والبركة"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_47": {"type": "casual_response", "trigger": "مساءو", "responses": ["مساء النور والسرور! 🌙", "مساءك عسل", "يا مساء الخير والبركة"], "learned_at": "2025-06-06T15:29:29.039191", "category": "تحيات وردود عامية"}, "casual_response_48": {"type": "casual_response", "trigger": "تسلم يدينك", "responses": ["الله يسلمك! 🙌", "تسلم من كل شر", "حياك الله"], "learned_at": "2025-06-06T15:29:29.048103", "category": "تحيات وردود عامية"}, "casual_response_49": {"type": "casual_response", "trigger": "الله يسلمك", "responses": ["ويسلمك يارب! 🤲", "تسلم من كل شر", "الله يحفظك"], "learned_at": "2025-06-06T15:29:29.048103", "category": "تحيات وردود عامية"}, "casual_response_50": {"type": "casual_response", "trigger": "ما قصرت", "responses": ["العفو! هذا واجبي 🙏", "تسلم، ما سوينا شي", "الله يسعدك"], "learned_at": "2025-06-06T15:29:29.056088", "category": "تحيات وردود عامية"}, "casual_response_51": {"type": "casual_response", "trigger": "بارك الله فيك", "responses": ["وفيك بارك الله! 🤲", "آمين وإياك", "جزاك الله خير"], "learned_at": "2025-06-06T15:29:29.056088", "category": "تحيات وردود عامية"}, "casual_response_52": {"type": "casual_response", "trigger": "جزاك الله خير", "responses": ["وإياك! جزاك الله خير 🤲", "وجزاك مثله وأكثر", "الله يجزيك كل خير"], "learned_at": "2025-06-06T15:29:29.056088", "category": "تحيات وردود عامية"}, "casual_response_53": {"type": "casual_response", "trigger": "الله يجزاك خير", "responses": ["وياك يارب! 🤲", "ويجزيك خير", "آمين يارب العالمين"], "learned_at": "2025-06-06T15:29:29.056088", "category": "تحيات وردود عامية"}, "casual_response_54": {"type": "casual_response", "trigger": "تسلم على هذا", "responses": ["الله يسلمك! العفو 🙏", "حياك الله، ما سوينا شي", "تسلم من كل شر"], "learned_at": "2025-06-06T15:29:29.056088", "category": "تحيات وردود عامية"}, "casual_response_55": {"type": "casual_response", "trigger": "يعجبني هذا", "responses": ["حلو! يسعدني هذا 😊", "شكراً لك! هذا يشجعني", "يسعدني أن هذا أعجبك"], "learned_at": "2025-06-06T15:29:29.056088", "category": "تحيات وردود عامية"}, "casual_response_56": {"type": "casual_response", "trigger": "ح<PERSON><PERSON>", "responses": ["شكراً لك! 😊", "يسعدني هذا", "حلو تواجدك"], "learned_at": "2025-06-06T15:29:29.065242", "category": "تحيات وردود عامية"}, "casual_response_57": {"type": "casual_response", "trigger": "جميل", "responses": ["شكراً جزيلاً! 🌟", "يسعدني أن هذا أعجبك", "أنت الأجمل"], "learned_at": "2025-06-06T15:29:29.067520", "category": "تحيات وردود عامية"}, "casual_response_58": {"type": "casual_response", "trigger": "<PERSON><PERSON><PERSON><PERSON>", "responses": ["شكراً لك! 🌟", "يسعدني أن هذا أثار إعجابك", "أحاول دائماً التميز"], "learned_at": "2025-06-06T15:29:29.069713", "category": "تحيات وردود عامية"}, "casual_response_59": {"type": "casual_response", "trigger": "واو", "responses": ["شكراً لك! 🌟", "يسعدني ردة فعلك", "هذا من ذوقك"], "learned_at": "2025-06-06T15:29:29.069713", "category": "تحيات وردود عامية"}, "casual_response_60": {"type": "casual_response", "trigger": "برافو", "responses": ["شكراً جزيلاً! 👏", "تسلم على التشجيع", "هذا من ذوقك"], "learned_at": "2025-06-06T15:29:29.069713", "category": "تحيات وردود عامية"}, "casual_response_61": {"type": "casual_response", "trigger": "أشكرك", "responses": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "learned_at": "2025-06-06T15:29:29.069713", "category": "تحيات وردود عامية"}, "casual_response_62": {"type": "casual_response", "trigger": "شكراً جزيلاً", "responses": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "learned_at": "2025-06-06T15:29:29.069713", "category": "تحيات وردود عامية"}, "casual_response_63": {"type": "casual_response", "trigger": "شكراً لك", "responses": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "learned_at": "2025-06-06T15:29:29.069713", "category": "تحيات وردود عامية"}, "casual_response_64": {"type": "casual_response", "trigger": "السلام عليكم", "responses": ["وعليكم السلام ورحمة الله وبركاته", "وعليكم السلام، أهلاً وسهلاً", "وعليكم السلام، مرحباً بك"], "learned_at": "2025-06-06T15:29:34.401126", "category": "تحيات وردود عامية"}, "casual_response_65": {"type": "casual_response", "trigger": "سلام عليكم", "responses": ["وعليكم السلام ورحمة الله", "وعليكم السلام، أهلاً بك"], "learned_at": "2025-06-06T15:29:34.401126", "category": "تحيات وردود عامية"}, "casual_response_66": {"type": "casual_response", "trigger": "مرحبا", "responses": ["أهلاً وسهلاً بك! 😊", "مرحباً! كيف حالك؟", "أهلاً بك، نورت!"], "learned_at": "2025-06-06T15:29:34.401126", "category": "تحيات وردود عامية"}, "casual_response_67": {"type": "casual_response", "trigger": "مرحباً", "responses": ["أهلاً وسهلاً! 🌟", "مرحباً بك، كيف يمكنني مساعدتك؟"], "learned_at": "2025-06-06T15:29:34.408437", "category": "تحيات وردود عامية"}, "casual_response_68": {"type": "casual_response", "trigger": "هلا", "responses": ["هلا والله! أهلاً بك", "هلا فيك! كيف الأحوال؟"], "learned_at": "2025-06-06T15:29:34.408437", "category": "تحيات وردود عامية"}, "casual_response_69": {"type": "casual_response", "trigger": "اهلا", "responses": ["أهلاً وسهلاً! مرحباً بك", "اهلين فيك!"], "learned_at": "2025-06-06T15:29:34.408437", "category": "تحيات وردود عامية"}, "casual_response_70": {"type": "casual_response", "trigger": "اهلين", "responses": ["اهلين فيك! نورت", "اهلا وسهلا بك"], "learned_at": "2025-06-06T15:29:34.408437", "category": "تحيات وردود عامية"}, "casual_response_71": {"type": "casual_response", "trigger": "<PERSON><PERSON><PERSON> حال<PERSON>", "responses": ["الحمد لله، تمام! وأنت كيف حالك؟", "بخير والحمد لله، شكراً لسؤالك", "ممتاز! أتعلم وأتطور كل يوم"], "learned_at": "2025-06-06T15:29:34.411136", "category": "تحيات وردود عامية"}, "casual_response_72": {"type": "casual_response", "trigger": "شلونك", "responses": ["زين الحمد لله! وأنت شلونك؟", "تمام، كله زين! شكراً"], "learned_at": "2025-06-06T15:29:34.411136", "category": "تحيات وردود عامية"}, "casual_response_73": {"type": "casual_response", "trigger": "كيفك", "responses": ["منيح الحمد لله! وأنت كيفك؟", "تمام، شكراً لسؤالك"], "learned_at": "2025-06-06T15:29:34.411136", "category": "تحيات وردود عامية"}, "casual_response_74": {"type": "casual_response", "trigger": "وش أخبارك", "responses": ["أخبار زينة الحمد لله! وأنت وش أخبارك؟", "كله تمام، أتعلم أشياء جديدة"], "learned_at": "2025-06-06T15:29:34.411136", "category": "تحيات وردود عامية"}, "casual_response_75": {"type": "casual_response", "trigger": "ايش اخبارك", "responses": ["أخبار حلوة! أتعلم كل يوم شيء جديد", "كله منيح، شكراً لسؤالك"], "learned_at": "2025-06-06T15:29:34.411136", "category": "تحيات وردود عامية"}, "casual_response_76": {"type": "casual_response", "trigger": "شكرا", "responses": ["العفو! لا شكر على واجب", "أهلاً وسهلاً، دايماً في الخدمة", "تسلم، هذا واجبي"], "learned_at": "2025-06-06T15:29:34.419135", "category": "تحيات وردود عامية"}, "casual_response_77": {"type": "casual_response", "trigger": "شكراً", "responses": ["العفو! 😊", "لا شكر على واجب", "أهلاً وسهلاً بك"], "learned_at": "2025-06-06T15:29:34.419135", "category": "تحيات وردود عامية"}, "casual_response_78": {"type": "casual_response", "trigger": "مشكور", "responses": ["الله يعافيك! تسلم", "أهلاً وسهلاً، هذا واجبي"], "learned_at": "2025-06-06T15:29:34.419135", "category": "تحيات وردود عامية"}, "casual_response_79": {"type": "casual_response", "trigger": "يعطيك العافية", "responses": ["الله يعافيك! 💪", "ويعطيك العافية", "تسلم يا رب"], "learned_at": "2025-06-06T15:29:34.419135", "category": "تحيات وردود عامية"}, "casual_response_80": {"type": "casual_response", "trigger": "تسلم", "responses": ["الله يسلمك! شكراً", "تسلم، وأنت كمان"], "learned_at": "2025-06-06T15:29:34.450046", "category": "تحيات وردود عامية"}, "casual_response_81": {"type": "casual_response", "trigger": "ص<PERSON><PERSON><PERSON> الخير", "responses": ["صباح النور! كيف حالك اليوم؟", "صباح الخير والسرور! 🌅", "صباح النور، يوم سعيد إن شاء الله"], "learned_at": "2025-06-06T15:29:34.485419", "category": "تحيات وردود عامية"}, "casual_response_82": {"type": "casual_response", "trigger": "مساء الخير", "responses": ["مساء النور! كيف كان يومك؟", "مساء الخير والسرور! 🌙", "مساء النور، أتمنى يومك كان حلو"], "learned_at": "2025-06-06T15:29:34.489353", "category": "تحيات وردود عامية"}, "casual_response_83": {"type": "casual_response", "trigger": "تص<PERSON><PERSON> ع<PERSON>ى خير", "responses": ["وأنت بخير! أحلام سعيدة", "تص<PERSON>ح على خير، نوم هنيء", "وأنت بألف خير! 🌙"], "learned_at": "2025-06-06T15:29:34.499750", "category": "تحيات وردود عامية"}, "casual_response_84": {"type": "casual_response", "trigger": "مع السلامة", "responses": ["مع السلامة! في أمان الله", "الله معك، مع السلامة", "في أمان الله! 👋"], "learned_at": "2025-06-06T15:29:34.499750", "category": "تحيات وردود عامية"}, "casual_response_85": {"type": "casual_response", "trigger": "باي", "responses": ["باي باي! أراك قريباً", "مع السلامة! 👋"], "learned_at": "2025-06-06T15:29:34.512523", "category": "تحيات وردود عامية"}, "casual_response_86": {"type": "casual_response", "trigger": "وداعا", "responses": ["وداعاً! أتطلع لرؤيتك مرة أخرى", "في أمان الله!"], "learned_at": "2025-06-06T15:29:34.512523", "category": "تحيات وردود عامية"}, "casual_response_87": {"type": "casual_response", "trigger": "آسف", "responses": ["لا بأس، ما صار شي", "عادي، مافي مشكلة", "الله يعافيك، عادي"], "learned_at": "2025-06-06T15:29:34.528802", "category": "تحيات وردود عامية"}, "casual_response_88": {"type": "casual_response", "trigger": "معذرة", "responses": ["لا بأس عليك، عادي", "ما عليك زود، مافي مشكلة"], "learned_at": "2025-06-06T15:29:34.528802", "category": "تحيات وردود عامية"}, "casual_response_89": {"type": "casual_response", "trigger": "اعتذر", "responses": ["لا داعي للاعتذار، كله تمام", "عادي، مافي شي"], "learned_at": "2025-06-06T15:29:34.544238", "category": "تحيات وردود عامية"}, "casual_response_90": {"type": "casual_response", "trigger": "نعم", "responses": ["تمام! 👍", "زين، فهمت", "أوكي!"], "learned_at": "2025-06-06T15:29:34.578089", "category": "تحيات وردود عامية"}, "casual_response_91": {"type": "casual_response", "trigger": "أيوة", "responses": ["تمام! فهمت", "زين، واضح"], "learned_at": "2025-06-06T15:29:34.578089", "category": "تحيات وردود عامية"}, "casual_response_92": {"type": "casual_response", "trigger": "اوكي", "responses": ["أوكي! 👌", "تمام، فهمت"], "learned_at": "2025-06-06T15:29:34.602221", "category": "تحيات وردود عامية"}, "casual_response_93": {"type": "casual_response", "trigger": "تمام", "responses": ["تمام تمام! 😊", "زين، كله واضح"], "learned_at": "2025-06-06T15:29:34.602221", "category": "تحيات وردود عامية"}, "casual_response_94": {"type": "casual_response", "trigger": "لا", "responses": ["لا بأس، كما تشاء", "تمام، مافي مشكلة"], "learned_at": "2025-06-06T15:29:34.605835", "category": "تحيات وردود عامية"}, "casual_response_95": {"type": "casual_response", "trigger": "لا شكرا", "responses": ["لا بأس، كما تريد", "تمام، أي وقت تحتاج شيء قولي"], "learned_at": "2025-06-06T15:29:34.644164", "category": "تحيات وردود عامية"}, "casual_response_96": {"type": "casual_response", "trigger": "ممكن تساعدني", "responses": ["طبعاً! كيف أقدر أساعدك؟", "أكيد، أنا هنا للمساعدة", "بكل سرور، وش محتاج؟"], "learned_at": "2025-06-06T15:29:34.644164", "category": "تحيات وردود عامية"}, "casual_response_97": {"type": "casual_response", "trigger": "عندي سؤال", "responses": ["تفضل، اسأل وأنا أجاوبك", "أنا جاهز للإجابة على أسئلتك", "تفضل، كلي آذان صاغية"], "learned_at": "2025-06-06T15:29:34.663649", "category": "تحيات وردود عامية"}, "casual_response_98": {"type": "casual_response", "trigger": "ما فهمت", "responses": ["خليني أشرح لك بطريقة أوضح", "ما عليه، أقدر أشرح مرة ثانية", "دعني أحاول التوضيح بشكل أفضل"], "learned_at": "2025-06-06T15:29:34.663649", "category": "تحيات وردود عامية"}, "casual_response_99": {"type": "casual_response", "trigger": "مم<PERSON><PERSON><PERSON>", "responses": ["يسعدني أني قدرت أساعد! 😊", "شكراً لك! أحاول دائماً تقديم الأفضل", "هذا يسعدني كثيراً!"], "learned_at": "2025-06-06T15:29:34.663649", "category": "تحيات وردود عامية"}, "casual_response_100": {"type": "casual_response", "trigger": "رائع", "responses": ["شكراً لك! 🌟", "سعيد أن هذا أعجبك", "يسعدني سماع هذا!"], "learned_at": "2025-06-06T15:29:34.666686", "category": "تحيات وردود عامية"}, "casual_response_101": {"type": "casual_response", "trigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": ["شكراً جزيلاً! 👏", "هذا من ذوقك", "أشكرك على هذا التقدير"], "learned_at": "2025-06-06T15:29:34.666686", "category": "تحيات وردود عامية"}, "casual_response_102": {"type": "casual_response", "trigger": "بالتوفيق", "responses": ["الله يوفقك! 🙏", "شكراً لك، ولك بالمثل", "الله يوفق الجميع"], "learned_at": "2025-06-06T15:29:34.671423", "category": "تحيات وردود عامية"}, "casual_response_103": {"type": "casual_response", "trigger": "الله يسعدك", "responses": ["ويسعدك يارب! 💝", "الله يسعد أيامك", "تسلم، الله يسعد الجميع"], "learned_at": "2025-06-06T15:29:34.673270", "category": "تحيات وردود عامية"}, "casual_response_104": {"type": "casual_response", "trigger": "الله يخليك", "responses": ["ويخليك يارب! 🤲", "تسلم، الله يحفظك", "الله يخلي لك أحبابك"], "learned_at": "2025-06-06T15:29:34.673270", "category": "تحيات وردود عامية"}, "casual_response_105": {"type": "casual_response", "trigger": "الله يعطيك العافية", "responses": ["ويعطيك العافية! 💪", "الله يعافيك ويسلمك", "تسلم، الله يقويك"], "learned_at": "2025-06-06T15:29:34.673270", "category": "تحيات وردود عامية"}, "casual_response_106": {"type": "casual_response", "trigger": "الله يوفقك", "responses": ["آمين وياك! 🤲", "ولك بالمثل يارب", "الله يوفقنا جميعاً"], "learned_at": "2025-06-06T15:29:34.681267", "category": "تحيات وردود عامية"}, "casual_response_107": {"type": "casual_response", "trigger": "يا هلا", "responses": ["هلا وغلا! 🌺", "يا هلا ومرحبا", "هلا بك وبمن معك"], "learned_at": "2025-06-06T15:29:34.683769", "category": "تحيات وردود عامية"}, "casual_response_108": {"type": "casual_response", "trigger": "يا مرحبا", "responses": ["مراحب! نورتنا 🌟", "يا مرحبا بك وبمن معك", "مرحبتين وثلاث!"], "learned_at": "2025-06-06T15:29:34.684764", "category": "تحيات وردود عامية"}, "casual_response_109": {"type": "casual_response", "trigger": "صباحو", "responses": ["صباح النور والسرور! 🌅", "صباحك عسل", "يا صباح الخير والبركة"], "learned_at": "2025-06-06T15:29:34.691674", "category": "تحيات وردود عامية"}, "casual_response_110": {"type": "casual_response", "trigger": "مساءو", "responses": ["مساء النور والسرور! 🌙", "مساءك عسل", "يا مساء الخير والبركة"], "learned_at": "2025-06-06T15:29:34.699668", "category": "تحيات وردود عامية"}, "casual_response_111": {"type": "casual_response", "trigger": "تسلم يدينك", "responses": ["الله يسلمك! 🙌", "تسلم من كل شر", "حياك الله"], "learned_at": "2025-06-06T15:29:34.710605", "category": "تحيات وردود عامية"}, "casual_response_112": {"type": "casual_response", "trigger": "الله يسلمك", "responses": ["ويسلمك يارب! 🤲", "تسلم من كل شر", "الله يحفظك"], "learned_at": "2025-06-06T15:29:34.721138", "category": "تحيات وردود عامية"}, "casual_response_113": {"type": "casual_response", "trigger": "ما قصرت", "responses": ["العفو! هذا واجبي 🙏", "تسلم، ما سوينا شي", "الله يسعدك"], "learned_at": "2025-06-06T15:29:34.722260", "category": "تحيات وردود عامية"}, "casual_response_114": {"type": "casual_response", "trigger": "بارك الله فيك", "responses": ["وفيك بارك الله! 🤲", "آمين وإياك", "جزاك الله خير"], "learned_at": "2025-06-06T15:29:34.722766", "category": "تحيات وردود عامية"}, "casual_response_115": {"type": "casual_response", "trigger": "جزاك الله خير", "responses": ["وإياك! جزاك الله خير 🤲", "وجزاك مثله وأكثر", "الله يجزيك كل خير"], "learned_at": "2025-06-06T15:29:34.727934", "category": "تحيات وردود عامية"}, "casual_response_116": {"type": "casual_response", "trigger": "الله يجزاك خير", "responses": ["وياك يارب! 🤲", "ويجزيك خير", "آمين يارب العالمين"], "learned_at": "2025-06-06T15:29:34.727934", "category": "تحيات وردود عامية"}, "casual_response_117": {"type": "casual_response", "trigger": "تسلم على هذا", "responses": ["الله يسلمك! العفو 🙏", "حياك الله، ما سوينا شي", "تسلم من كل شر"], "learned_at": "2025-06-06T15:29:34.741303", "category": "تحيات وردود عامية"}, "casual_response_118": {"type": "casual_response", "trigger": "يعجبني هذا", "responses": ["حلو! يسعدني هذا 😊", "شكراً لك! هذا يشجعني", "يسعدني أن هذا أعجبك"], "learned_at": "2025-06-06T15:29:34.745525", "category": "تحيات وردود عامية"}, "casual_response_119": {"type": "casual_response", "trigger": "ح<PERSON><PERSON>", "responses": ["شكراً لك! 😊", "يسعدني هذا", "حلو تواجدك"], "learned_at": "2025-06-06T15:29:34.745525", "category": "تحيات وردود عامية"}, "casual_response_120": {"type": "casual_response", "trigger": "جميل", "responses": ["شكراً جزيلاً! 🌟", "يسعدني أن هذا أعجبك", "أنت الأجمل"], "learned_at": "2025-06-06T15:29:34.752866", "category": "تحيات وردود عامية"}, "casual_response_121": {"type": "casual_response", "trigger": "<PERSON><PERSON><PERSON><PERSON>", "responses": ["شكراً لك! 🌟", "يسعدني أن هذا أثار إعجابك", "أحاول دائماً التميز"], "learned_at": "2025-06-06T15:29:34.752866", "category": "تحيات وردود عامية"}, "casual_response_122": {"type": "casual_response", "trigger": "واو", "responses": ["شكراً لك! 🌟", "يسعدني ردة فعلك", "هذا من ذوقك"], "learned_at": "2025-06-06T15:29:34.752866", "category": "تحيات وردود عامية"}, "casual_response_123": {"type": "casual_response", "trigger": "برافو", "responses": ["شكراً جزيلاً! 👏", "تسلم على التشجيع", "هذا من ذوقك"], "learned_at": "2025-06-06T15:29:34.752866", "category": "تحيات وردود عامية"}, "casual_response_124": {"type": "casual_response", "trigger": "أشكرك", "responses": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "learned_at": "2025-06-06T15:29:34.752866", "category": "تحيات وردود عامية"}, "casual_response_125": {"type": "casual_response", "trigger": "شكراً جزيلاً", "responses": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "learned_at": "2025-06-06T15:29:34.756132", "category": "تحيات وردود عامية"}, "casual_response_126": {"type": "casual_response", "trigger": "شكراً لك", "responses": ["العفو! سعيد بخدمتك 🙏", "لا شكر على واجب", "أنا هنا لمساعدتك"], "learned_at": "2025-06-06T15:29:34.756132", "category": "تحيات وردود عامية"}}}