# -*- coding: utf-8 -*-
"""
نموذج ذكاء اصطناعي عربي يعمل بدون إنترنت
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.optimizers import Adam
import re

# إعدادات النموذج
MODEL_NAME = "ml-T1-Simple-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"

# معاملات محسنة
MAX_SEQUENCE_LEN = 12
EMBEDDING_DIM = 128
LSTM_UNITS = 64
DROPOUT_RATE = 0.3
EPOCHS = 10
BATCH_SIZE = 32

def create_comprehensive_arabic_data():
    """إنشاء بيانات عربية شاملة للتدريب"""
    
    # معلومات عامة
    general_knowledge = [
        "الذكاء الاصطناعي هو تقنية حديثة تحاكي الذكاء البشري",
        "تعلم الآلة جزء مهم من الذكاء الاصطناعي",
        "البرمجة مهارة أساسية في عصر التقنية",
        "الحاسوب أداة قوية تساعد في العمل والتعلم",
        "الإنترنت شبكة عالمية تربط الناس ببعضهم",
        "العلم والمعرفة أساس التقدم والحضارة",
        "التعليم حق أساسي لكل إنسان",
        "الصحة تاج على رؤوس الأصحاء",
        "الرياضة تقوي الجسم والعقل",
        "القراءة غذاء العقل والروح"
    ]
    
    # معلومات جغرافية
    geography = [
        "عاصمة السعودية هي الرياض",
        "عاصمة مصر هي القاهرة",
        "عاصمة الإمارات هي أبو ظبي",
        "عاصمة الكويت هي مدينة الكويت",
        "عاصمة قطر هي الدوحة",
        "عاصمة البحرين هي المنامة",
        "عاصمة عمان هي مسقط",
        "عاصمة الأردن هي عمان",
        "عاصمة لبنان هي بيروت",
        "عاصمة سوريا هي دمشق"
    ]
    
    # تحيات وعبارات شائعة
    greetings = [
        "السلام عليكم ورحمة الله وبركاته",
        "وعليكم السلام ورحمة الله وبركاته",
        "مرحبا بك أهلا وسهلا",
        "أهلا وسهلا بك نورت المكان",
        "صباح الخير والنور",
        "صباح النور والسرور",
        "مساء الخير والسعادة",
        "مساء النور والبركة",
        "كيف حالك اليوم",
        "الحمد لله بخير وأنت كيف حالك",
        "شكرا لك جزيلا",
        "العفو لا شكر على واجب",
        "بارك الله فيك",
        "حفظك الله ورعاك"
    ]
    
    # أسئلة وإجابات
    qa_pairs = [
        "ما هو الذكاء الاصطناعي هو تقنية تحاكي الذكاء البشري",
        "كيف يعمل الحاسوب يعمل بالكهرباء والبرمجيات",
        "لماذا نتعلم البرمجة لأنها مهارة المستقبل",
        "متى اخترع الإنترنت اخترع في الستينات",
        "أين تقع مكة المكرمة تقع في السعودية",
        "من اخترع الكهرباء اكتشفها علماء كثيرون",
        "ما فائدة الرياضة تقوي الجسم والعقل",
        "كيف نحافظ على الصحة بالرياضة والغذاء الصحي",
        "ما أهمية التعليم يبني العقول والمجتمعات",
        "لماذا نقرأ الكتب لنكتسب المعرفة والثقافة"
    ]
    
    # علوم ومعرفة
    science = [
        "الرياضيات أم العلوم وأساس التقدم",
        "الفيزياء تفسر قوانين الطبيعة والكون",
        "الكيمياء تدرس المواد وتفاعلاتها",
        "الأحياء تدرس الكائنات الحية",
        "الطب يساعد في علاج الأمراض",
        "الهندسة تبني الحضارة والمستقبل",
        "الفلك يدرس النجوم والكواكب",
        "الجيولوجيا تدرس طبقات الأرض",
        "البيئة تحتاج للحماية والاهتمام",
        "الطاقة المتجددة مستقبل الكوكب"
    ]
    
    # ثقافة وأدب
    culture = [
        "اللغة العربية لغة القرآن الكريم",
        "الشعر العربي تراث عريق وجميل",
        "الأدب يعبر عن مشاعر الإنسان",
        "التاريخ يعلمنا دروس الماضي",
        "الثقافة تثري حياة الإنسان",
        "الفن يجمل الحياة ويهذب النفس",
        "الموسيقى لغة عالمية تفهمها القلوب",
        "المسرح مرآة المجتمع وقضاياه",
        "السينما فن يحكي قصص الحياة",
        "الرسم يعبر عن الجمال والإبداع"
    ]
    
    return general_knowledge + geography + greetings + qa_pairs + science + culture

def preprocess_texts(texts):
    """معالجة النصوص العربية"""
    processed = []
    for text in texts:
        # تنظيف النص
        text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        # فقط الجمل التي تحتوي على 3 كلمات أو أكثر
        if len(text.split()) >= 3:
            processed.append(text)
    
    return processed

def create_sequences(tokenizer, corpus, max_sequence_len):
    """إنشاء تسلسلات التدريب"""
    input_sequences = []
    
    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]
        
        # إنشاء تسلسلات متدرجة
        for i in range(2, len(token_list) + 1):
            if i <= max_sequence_len:
                n_gram_sequence = token_list[:i]
                # إضافة padding
                while len(n_gram_sequence) < max_sequence_len:
                    n_gram_sequence = [0] + n_gram_sequence
                input_sequences.append(n_gram_sequence)
    
    if not input_sequences:
        return np.array([]), np.array([])
    
    input_sequences = np.array(input_sequences)
    X = input_sequences[:, :-1]
    y = input_sequences[:, -1]
    
    vocab_size = len(tokenizer.word_index) + 1
    y = to_categorical(y, num_classes=vocab_size)
    
    return X, y

def build_model(vocab_size, max_sequence_len):
    """بناء نموذج ذكي"""
    model = Sequential()
    
    # طبقة التضمين
    model.add(Embedding(
        input_dim=vocab_size,
        output_dim=EMBEDDING_DIM,
        input_length=max_sequence_len - 1,
        mask_zero=True
    ))
    
    # طبقة LSTM ثنائية الاتجاه
    model.add(Bidirectional(LSTM(
        LSTM_UNITS,
        return_sequences=False,
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))
    
    # طبقة مكتملة الاتصال
    model.add(Dense(128, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))
    
    # طبقة الإخراج
    model.add(Dense(vocab_size, activation='softmax'))
    
    # تجميع النموذج
    optimizer = Adam(learning_rate=0.001)
    model.compile(
        loss='categorical_crossentropy',
        optimizer=optimizer,
        metrics=['accuracy']
    )
    
    return model

def generate_text(seed_text, next_words, model, tokenizer, max_sequence_len):
    """توليد نص ذكي"""
    output_text = seed_text
    used_words = []
    
    for i in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]
        
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        
        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]
            
            # Top-k sampling للتنوع
            top_k = min(5, len(predicted_probs))
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]
            
            # اختيار كلمة مناسبة
            selected_word = None
            for idx in sorted_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) >= 2:
                    # تجنب التكرار المفرط
                    recent_words = used_words[-3:] if len(used_words) >= 3 else used_words
                    if candidate_word not in recent_words:
                        selected_word = candidate_word
                        break
            
            if not selected_word:
                # استخدم أفضل كلمة متاحة
                predicted_index = sorted_indices[0]
                selected_word = tokenizer.index_word.get(predicted_index, "")
            
            if not selected_word:
                break
            
            output_text += " " + selected_word
            used_words.append(selected_word)
            
            # الحفاظ على آخر 5 كلمات فقط
            if len(used_words) > 5:
                used_words = used_words[-5:]
        
        except Exception:
            break
    
    return output_text

def main():
    print("🚀 بدء تدريب النموذج العربي الذكي...")
    
    # إنشاء البيانات
    texts = create_comprehensive_arabic_data()
    processed_texts = preprocess_texts(texts)
    
    print(f"📝 تم معالجة {len(processed_texts)} جملة")
    
    # إعداد Tokenizer
    tokenizer = Tokenizer()
    tokenizer.fit_on_texts(processed_texts)
    vocab_size = len(tokenizer.word_index) + 1
    
    print(f"🔤 حجم القاموس: {vocab_size} كلمة")
    
    # إنشاء بيانات التدريب
    X, y = create_sequences(tokenizer, processed_texts, MAX_SEQUENCE_LEN)
    
    if X.shape[0] == 0:
        print("❌ لا توجد بيانات تدريب")
        return
    
    print(f"📊 عدد عينات التدريب: {X.shape[0]}")
    
    # بناء النموذج
    model = build_model(vocab_size, MAX_SEQUENCE_LEN)
    
    # بناء النموذج
    dummy_input = np.zeros((1, MAX_SEQUENCE_LEN - 1))
    model(dummy_input)
    print(f"🏗️ تم بناء النموذج مع {model.count_params():,} معامل")
    
    # التدريب
    print("🚀 بدء التدريب...")
    history = model.fit(
        X, y,
        epochs=EPOCHS,
        batch_size=BATCH_SIZE,
        verbose=1,
        validation_split=0.1
    )
    
    # حفظ النموذج
    model.save(MODEL_FILE)
    with open(TOKENIZER_FILE, 'wb') as f:
        pickle.dump(tokenizer, f)
    
    print(f"✅ تم حفظ النموذج في: {MODEL_FILE}")
    print(f"✅ تم حفظ Tokenizer في: {TOKENIZER_FILE}")
    
    # عرض إحصائيات التدريب
    final_loss = history.history['loss'][-1]
    final_accuracy = history.history['accuracy'][-1]
    print(f"📊 الخسارة النهائية: {final_loss:.4f}")
    print(f"📊 الدقة النهائية: {final_accuracy:.4f}")
    
    # اختبار النموذج
    print("\n🧪 اختبار النموذج:")
    test_inputs = [
        "ما هو الذكاء الاصطناعي",
        "عاصمة السعودية",
        "مرحبا بك",
        "العلم والمعرفة",
        "الصحة مهمة"
    ]
    
    for test_input in test_inputs:
        result = generate_text(test_input, 8, model, tokenizer, MAX_SEQUENCE_LEN)
        print(f"🔤 '{test_input}' → '{result}'")
    
    print("\n✅ انتهى التدريب والاختبار بنجاح!")
    print("💡 يمكنك الآن تشغيل run_simple_advanced.py للتفاعل مع النموذج")

if __name__ == "__main__":
    main()
