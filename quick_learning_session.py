# -*- coding: utf-8 -*-
"""
جلسة تعليم سريعة لنموذج ML-T1
"""

import json
import os
from datetime import datetime

class QuickLearningSession:
    def __init__(self):
        self.knowledge = self.load_knowledge()
        
    def load_knowledge(self):
        """تحميل المعرفة الموجودة"""
        try:
            if os.path.exists('ml_t1_memory.json'):
                with open('ml_t1_memory.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        
        return {
            'concepts': {},
            'facts': {},
            'insights': [],
            'learning_sessions': []
        }
    
    def save_knowledge(self):
        """حفظ المعرفة"""
        try:
            with open('ml_t1_memory.json', 'w', encoding='utf-8') as f:
                json.dump(self.knowledge, f, ensure_ascii=False, indent=2)
            print("💾 تم حفظ المعرفة بنجاح!")
        except Exception as e:
            print(f"❌ خطأ في الحفظ: {e}")
    
    def teach_fact(self, question, answer):
        """تعليم حقيقة جديدة"""
        fact_id = f"fact_{len(self.knowledge['facts']) + 1}"
        
        self.knowledge['facts'][fact_id] = {
            'question': question,
            'answer': answer,
            'learned_at': datetime.now().isoformat(),
            'category': self.categorize_question(question)
        }
        
        # استخراج المفاهيم
        concepts = self.extract_concepts(f"{question} {answer}")
        for concept in concepts:
            if concept not in self.knowledge['concepts']:
                self.knowledge['concepts'][concept] = {
                    'frequency': 1,
                    'contexts': [f"{question} -> {answer}"],
                    'first_seen': datetime.now().isoformat()
                }
            else:
                self.knowledge['concepts'][concept]['frequency'] += 1
                self.knowledge['concepts'][concept]['contexts'].append(f"{question} -> {answer}")
        
        print(f"🧠 تعلمت: {question}")
        print(f"📝 الإجابة: {answer}")
        print(f"🔍 المفاهيم المستخرجة: {', '.join(concepts)}")
        print("-" * 50)
    
    def categorize_question(self, question):
        """تصنيف السؤال"""
        if any(word in question.lower() for word in ['كوكب', 'نجم', 'فضاء', 'مجموعة شمسية']):
            return 'فلك'
        elif any(word in question.lower() for word in ['مخترع', 'اختراع', 'اكتشاف']):
            return 'اختراعات'
        elif any(word in question.lower() for word in ['عاصمة', 'دولة', 'مدينة']):
            return 'جغرافيا'
        elif any(word in question.lower() for word in ['حيوان', 'طائر', 'سمك']):
            return 'أحياء'
        elif any(word in question.lower() for word in ['عدد', 'كم', 'عظام', 'قارات']):
            return 'أرقام ومعلومات'
        else:
            return 'عام'
    
    def extract_concepts(self, text):
        """استخراج المفاهيم من النص"""
        import re
        
        # كلمات الوصل
        stop_words = {'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هو', 'هي', 'أن', 'كان', 'كانت', 'ما', 'هذا', 'هذه'}
        
        # استخراج الكلمات العربية والإنجليزية
        words = re.findall(r'\b[\u0600-\u06FFa-zA-Z]+\b', text)
        concepts = [word for word in words if len(word) > 2 and word not in stop_words]
        
        return list(set(concepts))
    
    def show_learning_progress(self):
        """عرض تقدم التعلم"""
        print("\n🎓 **تقدم التعلم:**")
        print("=" * 40)
        print(f"📚 الحقائق المتعلمة: {len(self.knowledge['facts'])}")
        print(f"🧠 المفاهيم المكتسبة: {len(self.knowledge['concepts'])}")
        
        if self.knowledge['facts']:
            print(f"\n📋 **الحقائق الجديدة:**")
            for fact_id, fact in list(self.knowledge['facts'].items())[-5:]:
                print(f"   • {fact['question']} -> {fact['answer']}")
        
        # تصنيف الحقائق
        categories = {}
        for fact in self.knowledge['facts'].values():
            category = fact['category']
            categories[category] = categories.get(category, 0) + 1
        
        if categories:
            print(f"\n🏷️ **التصنيفات:**")
            for category, count in categories.items():
                print(f"   • {category}: {count} حقيقة")

def main():
    """الدالة الرئيسية للتعليم السريع"""
    print("🚀 بدء جلسة التعليم السريع لنموذج ML-T1")
    print("=" * 60)
    
    session = QuickLearningSession()
    
    # الأسئلة والإجابات للتعليم السريع
    quick_facts = [
        ("ما هو أكبر كوكب في المجموعة الشمسية؟", "المشتري هو أكبر كوكب في المجموعة الشمسية"),
        ("من هو مخترع الهاتف؟", "ألكسندر جراهام بيل مخترع الهاتف عام 1876"),
        ("ما هي عاصمة فرنسا؟", "باريس هي عاصمة فرنسا ومدينة الأنوار"),
        ("كم عدد قارات العالم؟", "سبع قارات: آسيا وأفريقيا وأوروبا وأمريكا الشمالية وأمريكا الجنوبية وأستراليا والقطب الجنوبي"),
        ("ما هو أسرع حيوان في العالم؟", "الفهد هو أسرع حيوان بري بسرعة تصل إلى 120 كم/ساعة"),
        ("في أي سنة اخترع الإنترنت؟", "الإنترنت اخترع عام 1969 كشبكة أربانت في أمريكا"),
        ("ما هو أطول نهر في العالم؟", "نهر النيل هو أطول نهر في العالم بطول 6650 كيلومتر"),
        ("من هو مؤسس شركة مايكروسوفت؟", "بيل غيتس وبول ألين مؤسسا شركة مايكروسوفت عام 1975"),
        ("ما هي أصغر دولة في العالم؟", "الفاتيكان هي أصغر دولة في العالم بمساحة 0.17 ميل مربع"),
        ("كم عدد عظام جسم الإنسان البالغ؟", "206 عظمة في جسم الإنسان البالغ")
    ]
    
    print("🧠 بدء تعليم الحقائق السريعة...")
    print()
    
    for i, (question, answer) in enumerate(quick_facts, 1):
        print(f"[{i}/10] تعليم حقيقة جديدة:")
        session.teach_fact(question, answer)
    
    # حفظ المعرفة
    session.save_knowledge()
    
    # عرض التقدم
    session.show_learning_progress()
    
    print(f"\n🎉 **انتهت جلسة التعليم السريع!**")
    print(f"✅ تم تعليم النموذج {len(quick_facts)} حقائق جديدة")
    print(f"💾 تم حفظ كل شيء في ذاكرة النموذج")
    print(f"🚀 النموذج الآن أذكى وأكثر معرفة!")
    
    # اقتراح الخطوة التالية
    print(f"\n💡 **الخطوة التالية:**")
    print(f"   شغل النموذج الرئيسي: python ML_T1_ultimate.py")
    print(f"   واكتب 'ذاكرتي' لرؤية ما تعلمه!")

if __name__ == "__main__":
    main()
