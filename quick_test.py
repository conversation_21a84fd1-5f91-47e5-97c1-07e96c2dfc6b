# -*- coding: utf-8 -*-
"""
اختبار سريع لنموذج أمؤلي-T1 المتقدم
Quick test for Advanced Amuli-T1 Model
"""

import os
import sys

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow: {tf.__version__}")
    except ImportError:
        print("❌ TensorFlow غير مثبت")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError:
        print("❌ NumPy غير مثبت")
        return False
    
    try:
        import requests
        print(f"✅ Requests متوفر")
    except ImportError:
        print("❌ Requests غير مثبت")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print(f"✅ BeautifulSoup متوفر")
    except ImportError:
        print("❌ BeautifulSoup غير مثبت")
        return False
    
    return True

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    
    files_to_check = [
        "t1.py",
        "run_advanced_model.py",
        "README_ADVANCED.md",
        "requirements.txt"
    ]
    
    all_exist = True
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} غير موجود")
            all_exist = False
    
    return all_exist

def check_model_files():
    """فحص ملفات النموذج المدرب"""
    print("\n🤖 فحص ملفات النموذج...")
    
    model_files = [
        "ml-T1-Advanced.h5",
        "ml-T1-Advanced_tokenizer.pkl"
    ]
    
    trained = True
    for file in model_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} غير موجود - يحتاج تدريب")
            trained = False
    
    return trained

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🧪 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار استيراد الوحدات
        from t1 import clean_input, preprocess_text
        
        # اختبار تنظيف النص
        test_text = "مرحبا بك في عالم الذكاء الاصطناعي!"
        cleaned = clean_input(test_text)
        print(f"✅ تنظيف النص: '{test_text}' -> '{cleaned}'")
        
        # اختبار معالجة النص
        processed = preprocess_text("هذا نص تجريبي للاختبار. يحتوي على جمل متعددة!")
        print(f"✅ معالجة النص: تم بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def run_quick_demo():
    """تشغيل عرض سريع"""
    print("\n🎬 عرض سريع للنموذج...")
    
    if not check_model_files():
        print("⚠️ النموذج غير مدرب. يرجى تشغيل t1.py أولاً")
        return
    
    try:
        print("🚀 تشغيل النموذج...")
        os.system("python run_advanced_model.py")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النموذج: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎯 اختبار سريع لنموذج أمؤلي-T1 المتقدم")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ يرجى تثبيت المتطلبات أولاً:")
        print("pip install -r requirements.txt")
        return
    
    # فحص الملفات
    if not check_files():
        print("\n❌ بعض الملفات مفقودة")
        return
    
    # اختبار الوظائف
    if not test_basic_functionality():
        print("\n❌ فشل في اختبار الوظائف الأساسية")
        return
    
    print("\n✅ جميع الاختبارات نجحت!")
    
    # عرض الخيارات
    print("\n🎯 الخيارات المتاحة:")
    print("1. تدريب النموذج (python t1.py)")
    print("2. تشغيل النموذج التفاعلي (python run_advanced_model.py)")
    print("3. عرض سريع")
    print("4. خروج")
    
    while True:
        try:
            choice = input("\nاختر رقم الخيار: ").strip()
            
            if choice == "1":
                print("🚀 بدء التدريب...")
                os.system("python t1.py")
                break
            elif choice == "2":
                run_quick_demo()
                break
            elif choice == "3":
                print("\n🎬 العرض السريع:")
                print("- النموذج يدعم الأسئلة والإجابات باللغة العربية")
                print("- يمكنه تكملة الجمل بطريقة إبداعية")
                print("- يتعلم من تقييمات المستخدم")
                print("- يحتفظ بذاكرة المحادثات")
                break
            elif choice == "4":
                print("👋 إلى اللقاء!")
                break
            else:
                print("❌ خيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف البرنامج")
            break

if __name__ == "__main__":
    main()
