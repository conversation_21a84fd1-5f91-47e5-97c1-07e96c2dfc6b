# -*- coding: utf-8 -*-
"""
نموذج اختبار سريع للذكاء الاصطناعي العربي
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout
from tensorflow.keras.utils import to_categorical
import re

# إعدادات مبسطة
MODEL_NAME = "quick-test-model"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"

# معاملات مبسطة للاختبار السريع
MAX_SEQUENCE_LEN = 8
EMBEDDING_DIM = 64
LSTM_UNITS = 32
EPOCHS = 3
BATCH_SIZE = 16

def create_sample_data():
    """إنشاء بيانات تدريب بسيطة للاختبار"""
    sample_texts = [
        "الذكاء الاصطناعي هو تقنية حديثة",
        "تعلم الآلة جزء من الذكاء الاصطناعي",
        "البرمجة مهارة مهمة في التقنية",
        "الحاسوب أداة قوية للعمل",
        "العلم والمعرفة أساس التقدم",
        "التعليم مهم لبناء المستقبل",
        "التقنية تساعد في حل المشاكل",
        "الإنترنت يربط العالم ببعضه",
        "الرياضيات أساس العلوم",
        "الفيزياء تفسر الطبيعة",
        "الكيمياء تدرس المواد",
        "الطب يساعد في علاج الأمراض",
        "الهندسة تبني المستقبل",
        "الاقتصاد ينظم الموارد",
        "التاريخ يعلمنا الدروس",
        "اللغة العربية لغة جميلة",
        "الثقافة تثري الحياة",
        "الفن يعبر عن المشاعر",
        "الموسيقى تهذب النفس",
        "الرياضة تقوي الجسم"
    ]
    
    # إضافة تحيات وعبارات شائعة
    greetings = [
        "السلام عليكم ورحمة الله",
        "مرحبا بك أهلا وسهلا",
        "صباح الخير والنور",
        "مساء الخير والسرور",
        "كيف حالك اليوم",
        "أهلا وسهلا بك",
        "حياك الله وبياك",
        "نورت المكان",
        "شكرا لك جزيلا",
        "بارك الله فيك"
    ]
    
    return sample_texts + greetings

def preprocess_texts(texts):
    """معالجة النصوص"""
    processed = []
    for text in texts:
        # تنظيف النص
        text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        if len(text.split()) >= 3:  # فقط الجمل التي تحتوي على 3 كلمات أو أكثر
            processed.append(text)
    return processed

def create_sequences(tokenizer, corpus, max_sequence_len):
    """إنشاء تسلسلات التدريب"""
    input_sequences = []
    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]
        for i in range(2, len(token_list) + 1):
            if i <= max_sequence_len:
                n_gram_sequence = token_list[:i]
                # إضافة padding
                while len(n_gram_sequence) < max_sequence_len:
                    n_gram_sequence = [0] + n_gram_sequence
                input_sequences.append(n_gram_sequence)
    
    if not input_sequences:
        return np.array([]), np.array([])
        
    input_sequences = np.array(input_sequences)
    X = input_sequences[:, :-1]
    y = input_sequences[:, -1]
    
    vocab_size = len(tokenizer.word_index) + 1
    y = to_categorical(y, num_classes=vocab_size)
    
    return X, y

def build_simple_model(vocab_size, max_sequence_len):
    """بناء نموذج بسيط"""
    model = Sequential()
    
    model.add(Embedding(
        input_dim=vocab_size, 
        output_dim=EMBEDDING_DIM, 
        input_length=max_sequence_len - 1
    ))
    
    model.add(LSTM(LSTM_UNITS, dropout=0.2))
    model.add(Dense(vocab_size, activation='softmax'))
    
    model.compile(
        loss='categorical_crossentropy',
        optimizer='adam',
        metrics=['accuracy']
    )
    
    return model

def generate_text(seed_text, next_words, model, tokenizer, max_sequence_len):
    """توليد نص"""
    output_text = seed_text
    
    for _ in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]
        
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        
        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]
            predicted_index = np.argmax(predicted_probs)
            predicted_word = tokenizer.index_word.get(predicted_index, "")
            
            if not predicted_word:
                break
                
            output_text += " " + predicted_word
            
        except Exception:
            break
    
    return output_text

def main():
    print("🚀 بدء تدريب النموذج السريع...")
    
    # إنشاء البيانات
    texts = create_sample_data()
    processed_texts = preprocess_texts(texts)
    
    print(f"📝 تم معالجة {len(processed_texts)} جملة")
    
    # إعداد Tokenizer
    tokenizer = Tokenizer()
    tokenizer.fit_on_texts(processed_texts)
    vocab_size = len(tokenizer.word_index) + 1
    
    print(f"🔤 حجم القاموس: {vocab_size} كلمة")
    
    # إنشاء بيانات التدريب
    X, y = create_sequences(tokenizer, processed_texts, MAX_SEQUENCE_LEN)
    
    if X.shape[0] == 0:
        print("❌ لا توجد بيانات تدريب")
        return
    
    print(f"📊 عدد عينات التدريب: {X.shape[0]}")
    
    # بناء النموذج
    model = build_simple_model(vocab_size, MAX_SEQUENCE_LEN)

    # بناء النموذج بإدخال وهمي
    dummy_input = np.zeros((1, MAX_SEQUENCE_LEN - 1))
    model(dummy_input)
    print(f"🏗️ تم بناء النموذج مع {model.count_params():,} معامل")
    
    # التدريب
    print("🚀 بدء التدريب...")
    model.fit(X, y, epochs=EPOCHS, batch_size=BATCH_SIZE, verbose=1)
    
    # حفظ النموذج
    model.save(MODEL_FILE)
    with open(TOKENIZER_FILE, 'wb') as f:
        pickle.dump(tokenizer, f)
    
    print(f"✅ تم حفظ النموذج في: {MODEL_FILE}")
    print(f"✅ تم حفظ Tokenizer في: {TOKENIZER_FILE}")
    
    # اختبار النموذج
    print("\n🧪 اختبار النموذج:")
    test_inputs = [
        "الذكاء الاصطناعي",
        "مرحبا",
        "العلم",
        "التقنية"
    ]
    
    for test_input in test_inputs:
        result = generate_text(test_input, 5, model, tokenizer, MAX_SEQUENCE_LEN)
        print(f"🔤 '{test_input}' → '{result}'")
    
    print("\n✅ انتهى التدريب والاختبار بنجاح!")

if __name__ == "__main__":
    main()
