# -*- coding: utf-8 -*-
"""
سكربت تشغيل النموذج المتقدم "أمؤلي-T1 Advanced" 
للتفاعل المباشر مع الذكاء الاصطناعي المحسن
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.models import load_model
import re
import datetime

# إعدادات النموذج
MODEL_NAME = "ml-T1-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
KNOWLEDGE_BASE = f"{MODEL_NAME}_knowledge.pkl"
MAX_SEQUENCE_LEN = 32

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s\.\،\؟\!\:\؛]', '', text)
    return text.strip()

def generate_text_advanced(seed_text: str, next_words: int, model: tf.keras.Model,
                          tokenizer: Tokenizer, max_sequence_len: int, 
                          temperature: float = 0.7, top_k: int = 5) -> str:
    """توليد نص متقدم مع Temperature و Top-K sampling"""
    output_text = seed_text
    
    for _ in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]
            
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        
        predicted_probs = model.predict(token_list, verbose=0)[0]
        
        # تطبيق Temperature
        if temperature > 0:
            predicted_probs = np.log(predicted_probs + 1e-8) / temperature
            predicted_probs = np.exp(predicted_probs)
            predicted_probs = predicted_probs / np.sum(predicted_probs)
        
        # Top-K sampling
        if top_k > 0:
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            top_k_probs = predicted_probs[top_k_indices]
            top_k_probs = top_k_probs / np.sum(top_k_probs)
            predicted_index = np.random.choice(top_k_indices, p=top_k_probs)
        else:
            predicted_index = np.random.choice(len(predicted_probs), p=predicted_probs)
        
        predicted_word = tokenizer.index_word.get(predicted_index, "")
        if not predicted_word:
            break
            
        output_text += " " + predicted_word
    
    return output_text

def analyze_user_intent(text: str) -> str:
    """تحليل نية المستخدم"""
    text_lower = text.lower()
    
    if any(word in text_lower for word in ["ما هو", "ما هي", "تعريف", "معنى"]):
        return "definition"
    elif any(word in text_lower for word in ["كيف", "طريقة", "خطوات"]):
        return "how_to"
    elif any(word in text_lower for word in ["لماذا", "سبب", "علة"]):
        return "explanation"
    elif any(word in text_lower for word in ["متى", "وقت", "تاريخ"]):
        return "time"
    elif any(word in text_lower for word in ["أين", "مكان", "موقع"]):
        return "location"
    elif any(word in text_lower for word in ["هل", "أم", "؟"]):
        return "yes_no"
    else:
        return "general"

def intelligent_response(question: str, model: tf.keras.Model, tokenizer: Tokenizer, 
                        max_sequence_len: int) -> str:
    """إجابة ذكية على الأسئلة"""
    clean_question = clean_input(question)
    
    # توليد عدة إجابات مختلفة
    responses = []
    for temp in [0.3, 0.7, 1.0]:
        response = generate_text_advanced(
            clean_question, 
            next_words=15, 
            model=model, 
            tokenizer=tokenizer, 
            max_sequence_len=max_sequence_len,
            temperature=temp,
            top_k=10
        )
        responses.append(response)
    
    # اختيار أفضل إجابة
    best_response = max(responses, key=lambda x: len(x.split()))
    return best_response

class SimpleKnowledgeBase:
    """قاعدة معرفة مبسطة"""
    def __init__(self):
        self.conversation_history = []
    
    def add_conversation(self, user_input: str, ai_response: str, rating: int = None):
        conversation = {
            'timestamp': datetime.datetime.now(),
            'user_input': user_input,
            'ai_response': ai_response,
            'rating': rating
        }
        self.conversation_history.append(conversation)
    
    def get_context(self, max_history: int = 2) -> str:
        if not self.conversation_history:
            return ""
        
        recent_conversations = self.conversation_history[-max_history:]
        context_parts = []
        
        for conv in recent_conversations:
            context_parts.append(f"{conv['user_input']} {conv['ai_response']}")
        
        return " ".join(context_parts)

def main():
    """الدالة الرئيسية للتشغيل"""
    print("🤖 مرحباً بك في أمؤلي-T1 المتقدم!")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    if not os.path.exists(MODEL_FILE):
        print(f"❌ ملف النموذج غير موجود: {MODEL_FILE}")
        print("يرجى تشغيل t1.py أولاً لتدريب النموذج")
        return
    
    if not os.path.exists(TOKENIZER_FILE):
        print(f"❌ ملف Tokenizer غير موجود: {TOKENIZER_FILE}")
        print("يرجى تشغيل t1.py أولاً لتدريب النموذج")
        return
    
    # تحميل النموذج والـ Tokenizer
    print("📥 تحميل النموذج المتقدم...")
    try:
        model = load_model(MODEL_FILE)
        print(f"✅ تم تحميل النموذج: {MODEL_FILE}")
        
        with open(TOKENIZER_FILE, "rb") as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل Tokenizer: {TOKENIZER_FILE}")
        
        # إنشاء قاعدة معرفة مبسطة
        knowledge_base = SimpleKnowledgeBase()
        
        print(f"🧠 النموذج جاهز مع {model.count_params():,} معامل")
        print(f"📚 حجم القاموس: {len(tokenizer.word_index):,} كلمة")
        
    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        return
    
    # حلقة التفاعل
    print("\n🎯 النموذج جاهز للتفاعل!")
    print("💡 يمكنك:")
    print("   - طرح أسئلة للحصول على إجابات ذكية")
    print("   - كتابة جملة للحصول على تكملة إبداعية")
    print("   - كتابة 'خروج' لإنهاء البرنامج")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n🔤 أدخل سؤالك أو جملتك: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ["خروج", "exit", "quit", "bye"]:
                print("👋 شكراً لاستخدام أمؤلي-T1 المتقدم!")
                break
            
            # تنظيف الإدخال
            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي صالح")
                continue
            
            # تحليل النية وتوليد الإجابة
            intent = analyze_user_intent(clean_user_input)
            
            print(f"🔍 تحليل النية: {intent}")
            print("⏳ جاري التفكير...")
            
            if any(word in clean_user_input for word in ["ما", "كيف", "متى", "أين", "لماذا", "هل", "؟"]):
                # إجابة على سؤال
                response = intelligent_response(clean_user_input, model, tokenizer, MAX_SEQUENCE_LEN)
                print(f"\n🧠 الإجابة الذكية:\n{response}")
            else:
                # تكملة جملة
                completion = generate_text_advanced(
                    clean_user_input,
                    next_words=12,
                    model=model,
                    tokenizer=tokenizer,
                    max_sequence_len=MAX_SEQUENCE_LEN,
                    temperature=0.8,
                    top_k=8
                )
                print(f"\n🎨 التكملة الإبداعية:\n{completion}")
                response = completion
            
            # حفظ المحادثة
            knowledge_base.add_conversation(clean_user_input, response)
            
            # طلب التقييم
            print("\n📊 قيم الإجابة (1-5) أو اضغط Enter للتخطي:")
            rating_input = input("التقييم: ").strip()
            
            if rating_input and rating_input.isdigit() and 1 <= int(rating_input) <= 5:
                rating = int(rating_input)
                knowledge_base.conversation_history[-1]['rating'] = rating
                
                if rating >= 4:
                    print("😊 شكراً! سعيد أن الإجابة أعجبتك")
                elif rating <= 2:
                    print("😔 سأحاول التحسن في المرات القادمة")
                else:
                    print("🙂 شكراً لتقييمك")
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. إلى اللقاء!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            continue

if __name__ == "__main__":
    main()
