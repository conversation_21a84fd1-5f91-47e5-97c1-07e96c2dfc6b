# -*- coding: utf-8 -*-
"""
ملف تشغيل النظام المبسط
"""

import time
import random

def simulate_deep_thinking():
    """محاكاة التفكير العميق"""
    print("🧠 أفكر بعمق...")
    time.sleep(1)
    print("🔍 أحلل السؤال...")
    time.sleep(0.8)
    print("📚 أبحث في قاعدة المعرفة...")
    time.sleep(1.2)
    print("🧩 أربط المعلومات...")
    time.sleep(1)
    print("✍️ أصيغ الإجابة...")
    time.sleep(0.8)

def get_response(user_input):
    """الحصول على إجابة"""
    user_input_lower = user_input.lower()
    
    # قاعدة معرفة مبسطة
    responses = {
        "مرحبا": "أهلاً وسهلاً بك! 🌟 أنا أمؤلي، مساعدك الذكي الذي يحب التفكير العميق والمناقشة. كيف يمكنني مساعدتك اليوم؟",
        "السلام عليكم": "وعليكم السلام ورحمة الله وبركاته! 🕊️ مرحباً بك أخي الكريم. أنا هنا لمساعدتك في أي موضوع تريد مناقشته.",
        "كيف حالك": "الحمد لله أنا بأفضل حال! 😊 أشعر بالحماس للتعلم والمناقشة. وأنت كيف حالك؟ هل هناك موضوع يثير اهتمامك؟",
        "من أنت": "أنا أمؤلي! 🤖 ذكاء اصطناعي عربي متطور أحب التفكير العميق والمناقشة الذاتية. أستطيع مساعدتك في مواضيع متنوعة من العلوم إلى التاريخ والثقافة.",
        "عاصمة العراق": "عاصمة العراق هي بغداد العريقة! 🏛️ مدينة السلام التي كانت مركز الخلافة العباسية وموطن بيت الحكمة. لها تاريخ عظيم يمتد لأكثر من 1200 سنة.",
        "الذكاء الاصطناعي": "الذكاء الاصطناعي تقنية رائعة! 🧠 يحاكي قدرات العقل البشري في التفكير والتعلم. له تطبيقات واسعة في الطب والتعليم والتقنية. ما رأيك في مستقبل الذكاء الاصطناعي؟",
        "ناقش": "ممتاز! أحب المناقشة العميقة! 🗣️ دعني أفكر في هذا الموضوع من زوايا متعددة وأحلله بعمق...",
        "شكرا": "العفو! 🙏 أنا سعيد جداً لمساعدتك. هذا هو هدفي - أن أكون مفيداً ومساعداً في رحلة التعلم والاستكشاف.",
        "وداعا": "👋 كان من دواعي سروري التحدث معك! أتمنى أن تكون استفدت من محادثتنا. إلى اللقاء وأطيب التمنيات! 🌟"
    }
    
    # البحث عن إجابة مناسبة
    for key, response in responses.items():
        if key in user_input_lower:
            return response
    
    # إجابة عامة للمواضيع الجديدة
    general_responses = [
        "موضوع مثير للاهتمام! 🤔 دعني أفكر فيه بعمق وأحلله من زوايا متعددة. هذا يذكرني بمواضيع مشابهة ومترابطة. ما الجانب الذي يهمك أكثر؟",
        "سؤال ذكي ومدروس! 💡 أعتقد أن هذا الموضوع له جوانب كثيرة يمكن استكشافها. كل موضوع له تفاصيل مذهلة عندما نتعمق فيه أكثر. هل تريد أن نناقشه أكثر؟",
        "هذا يستحق التفكير العميق! 🧠 أحب كيف تطرح أسئلة تحفز على التفكير. دعني أحلل هذا الموضوع وأربطه بمعرفتي. أي زاوية تريد أن نركز عليها؟"
    ]
    
    return random.choice(general_responses)

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً! أنا أمؤلي - الذكاء الاصطناعي العربي المتطور")
    print("=" * 80)
    print("🧠 مميزاتي:")
    print("   ✨ تفكير عميق ومتأني")
    print("   ✨ مناقشة ذاتية متطورة")
    print("   ✨ قاعدة معرفة واسعة")
    print("   ✨ شخصية ودودة ومتفاعلة")
    print("\n💬 يمكنك أن تسألني عن:")
    print("   🌍 الجغرافيا والدول")
    print("   🏛️ التاريخ والحضارة")
    print("   💻 التقنية والذكاء الاصطناعي")
    print("   🔬 العلوم والطب")
    print("   🎨 الثقافة والفنون")
    print("\n   - اكتب 'خروج' لإنهاء المحادثة")
    print("-" * 80)
    
    conversation_count = 0
    
    while True:
        try:
            user_input = input(f"\n[{conversation_count + 1}] 💬 تحدث معي: ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ["خروج", "exit", "quit", "انتهاء", "وداعا"]:
                print(f"\n🤖 أمؤلي:")
                print(f"👋 كان لقاءً ممتعاً ومثمراً! أتمنى أن نتحدث مرة أخرى قريباً. 🌟")
                break
            
            # محاكاة التفكير العميق
            if len(user_input.split()) > 2:  # للأسئلة الطويلة
                simulate_deep_thinking()
            
            # الحصول على الإجابة
            response = get_response(user_input)
            
            print(f"\n🤖 أمؤلي:")
            print(f"📝 {response}")
            
            # إحصائيات
            response_words = len(response.split())
            input_words = len(user_input.split())
            print(f"\n📊 إحصائيات: الإدخال {input_words} كلمة | الإجابة {response_words} كلمة")
            
            conversation_count += 1
            
            # رسائل تشجيعية
            if conversation_count % 3 == 0:
                print(f"\n🌟 رائع! لقد أجرينا {conversation_count} محادثات مفيدة!")
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. كان لقاءً ممتعاً!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            print("💡 جرب إعادة صياغة السؤال")
            continue

if __name__ == "__main__":
    main()
