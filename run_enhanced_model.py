# -*- coding: utf-8 -*-
"""
تشغيل النموذج المحسن مع الردود الذكية والتحيات
"""

import os

def main():
    print("🤖 أمؤلي-T1 المحسن مع الردود الذكية")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    model_file = "ml-T1-Simple-Advanced.h5"
    tokenizer_file = "ml-T1-Simple-Advanced_tokenizer.pkl"
    
    if not os.path.exists(model_file) or not os.path.exists(tokenizer_file):
        print("⚠️ النموذج غير مدرب أو الملفات مفقودة")
        print("\n🔧 لتدريب النموذج المحسن:")
        print("python t1_simple_advanced.py")
        
        choice = input("\nهل تريد بدء التدريب الآن؟ (y/n): ")
        if choice.lower() in ['y', 'yes', 'نعم']:
            print("\n🚀 بدء التدريب المحسن...")
            os.system("python t1_simple_advanced.py")
            return
        else:
            return
    
    print("✅ النموذج جاهز!")
    print("\n🎯 الميزات الجديدة:")
    print("   ✅ ردود ذكية على التحيات")
    print("   ✅ فهم أفضل للأسئلة")
    print("   ✅ إجابات متنوعة ومناسبة")
    print("   ✅ تفاعل طبيعي أكثر")
    
    print("\n💡 جرب هذه الأمثلة:")
    print("   - السلام عليكم")
    print("   - كيف حالك؟")
    print("   - من أنت؟")
    print("   - ما هو الذكاء الاصطناعي؟")
    print("   - كيف أتعلم البرمجة؟")
    print("   - شكراً لك")
    
    choice = input("\nهل تريد تشغيل النموذج الآن؟ (y/n): ")
    if choice.lower() in ['y', 'yes', 'نعم']:
        print("\n🚀 تشغيل النموذج المحسن...")
        os.system("python run_simple_advanced.py")
    else:
        print("👋 يمكنك تشغيله لاحقاً بالأمر: python run_simple_advanced.py")

if __name__ == "__main__":
    main()
