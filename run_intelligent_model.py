# -*- coding: utf-8 -*-
"""
تشغيل النموذج مع نظام التفكير الذكي والتعلم التفاعلي
"""

import os

def main():
    print("🧠 أمؤلي-T1 مع نظام التفكير الذكي والتعلم التفاعلي")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        "ml-T1-Simple-Advanced.h5",
        "ml-T1-Simple-Advanced_tokenizer.pkl",
        "intelligent_thinking.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        
        if "intelligent_thinking.py" in missing_files:
            print("\n✅ ملف نظام التفكير الذكي موجود")
        
        if any("ml-T1" in file for file in missing_files):
            print("\n🔧 لتدريب النموذج:")
            print("python t1_simple_advanced.py")
            
            choice = input("\nهل تريد بدء التدريب؟ (y/n): ")
            if choice.lower() in ['y', 'yes', 'نعم']:
                print("\n🚀 بدء التدريب...")
                os.system("python t1_simple_advanced.py")
                return
        return
    
    print("✅ جميع الملفات موجودة!")
    
    print("\n🎯 الميزات الذكية الجديدة:")
    print("   🧠 تفكير ذكي للأسئلة غير المعروفة")
    print("   💾 ذاكرة تعلم تفاعلية")
    print("   📚 قاعدة معرفة متنامية")
    print("   🔄 تعلم من تقييمات المستخدم")
    print("   🎯 ردود محسنة وذكية")
    
    print("\n💡 كيف يعمل النظام الذكي:")
    print("   1. يحلل سؤالك ويبحث في ذاكرته")
    print("   2. إذا لم يجد إجابة، يفكر بناءً على معرفته")
    print("   3. يتعلم من تقييمك للإجابة")
    print("   4. يحفظ الإجابات الجيدة للمستقبل")
    print("   5. يحسن إجاباته مع الوقت")
    
    print("\n🎮 أمثلة للتجربة:")
    print("   - أسئلة معروفة: 'ما هو الذكاء الاصطناعي؟'")
    print("   - أسئلة جديدة: 'ما هو الحب؟'")
    print("   - أسئلة إبداعية: 'كيف أصبح سعيداً؟'")
    print("   - تعليم مباشر: قيم الإجابة بـ 1 واكتب الإجابة الصحيحة")
    
    choice = input("\nهل تريد تشغيل النظام الذكي؟ (y/n): ")
    if choice.lower() in ['y', 'yes', 'نعم']:
        print("\n🚀 تشغيل النظام الذكي...")
        print("💡 تذكر: قيم الإجابات لمساعدة النظام على التعلم!")
        os.system("python run_simple_advanced.py")
    else:
        print("👋 يمكنك تشغيله لاحقاً بالأمر: python run_simple_advanced.py")

if __name__ == "__main__":
    main()
