# -*- coding: utf-8 -*-
"""
نظام المناقشة الذاتية والتفكير التحليلي المتعدد الأوجه
"""

import time
import random
from knowledge_database import COMPREHENSIVE_KNOWLEDGE
from advanced_knowledge import get_all_knowledge

class SelfDiscussionSystem:
    """نظام المناقشة الذاتية والتحليل المتعدد الأوجه"""
    
    def __init__(self):
        self.knowledge_base = get_all_knowledge()
        self.discussion_perspectives = [
            "المنظور العلمي",
            "المنظور التاريخي", 
            "المنظور الثقافي",
            "المنظور التقني",
            "المنظور الاجتماعي",
            "المنظور الاقتصادي",
            "المنظور البيئي",
            "المنظور المستقبلي"
        ]
        
    def conduct_self_discussion(self, topic):
        """إجراء مناقشة ذاتية حول موضوع معين"""
        print(f"🧠 بدء المناقشة الذاتية حول: {topic}")
        print("=" * 80)
        
        # البحث عن المعلومات الأساسية
        base_info = self._find_topic_info(topic)
        
        if not base_info:
            return self._discuss_unknown_topic(topic)
        
        # تحليل الموضوع من زوايا متعددة
        discussion_result = self._multi_perspective_analysis(topic, base_info)
        
        return discussion_result
    
    def _find_topic_info(self, topic):
        """البحث عن معلومات الموضوع في قاعدة المعرفة"""
        topic_lower = topic.lower()
        
        # البحث المباشر
        for key, data in self.knowledge_base.items():
            if any(word in topic_lower for word in key.lower().split()):
                return {"key": key, "data": data, "match_type": "direct"}
        
        # البحث في المواضيع المرتبطة
        for key, data in self.knowledge_base.items():
            related_topics = data.get('related_topics', [])
            if any(related.lower() in topic_lower for related in related_topics):
                return {"key": key, "data": data, "match_type": "related"}
        
        return None
    
    def _multi_perspective_analysis(self, topic, base_info):
        """تحليل متعدد الأوجه للموضوع"""
        print("🔍 أحلل الموضوع من زوايا متعددة...")
        time.sleep(1)
        
        main_data = base_info["data"]
        perspectives = []
        
        # تحليل من منظور علمي
        scientific_view = self._analyze_scientific_perspective(topic, main_data)
        if scientific_view:
            perspectives.append(("🔬 المنظور العلمي", scientific_view))
        
        # تحليل من منظور تاريخي
        historical_view = self._analyze_historical_perspective(topic, main_data)
        if historical_view:
            perspectives.append(("🏛️ المنظور التاريخي", historical_view))
        
        # تحليل من منظور تقني
        technical_view = self._analyze_technical_perspective(topic, main_data)
        if technical_view:
            perspectives.append(("💻 المنظور التقني", technical_view))
        
        # تحليل من منظور اجتماعي
        social_view = self._analyze_social_perspective(topic, main_data)
        if social_view:
            perspectives.append(("👥 المنظور الاجتماعي", social_view))
        
        # تحليل من منظور مستقبلي
        future_view = self._analyze_future_perspective(topic, main_data)
        if future_view:
            perspectives.append(("🚀 المنظور المستقبلي", future_view))
        
        return self._synthesize_discussion(topic, main_data, perspectives)
    
    def _analyze_scientific_perspective(self, topic, data):
        """تحليل من المنظور العلمي"""
        scientific_keywords = ['علم', 'بحث', 'تجربة', 'نظرية', 'اكتشاف', 'دراسة']
        answer = data.get('answer', '')
        
        if any(keyword in answer for keyword in scientific_keywords):
            return f"من الناحية العلمية، {topic} يمثل مجالاً مهماً للبحث والاستكشاف. الدراسات الحديثة تظهر تطورات مستمرة في هذا المجال، مما يفتح آفاقاً جديدة للفهم والتطبيق."
        
        return None
    
    def _analyze_historical_perspective(self, topic, data):
        """تحليل من المنظور التاريخي"""
        historical_keywords = ['تاريخ', 'قديم', 'عصر', 'حضارة', 'تراث', 'أصل']
        answer = data.get('answer', '')
        
        if any(keyword in answer for keyword in historical_keywords):
            return f"تاريخياً، {topic} له جذور عميقة تمتد عبر العصور. تطور هذا المفهوم عبر الحضارات المختلفة، وكل حقبة أضافت إليه بُعداً جديداً يعكس فهم ذلك العصر وتطلعاته."
        
        return None
    
    def _analyze_technical_perspective(self, topic, data):
        """تحليل من المنظور التقني"""
        technical_keywords = ['تقنية', 'تكنولوجيا', 'رقمي', 'ذكي', 'حاسوب', 'إنترنت']
        answer = data.get('answer', '')
        
        if any(keyword in answer for keyword in technical_keywords):
            return f"من الناحية التقنية، {topic} يستفيد من أحدث التطورات التكنولوجية. التقنيات الحديثة مثل الذكاء الاصطناعي والحوسبة السحابية تفتح إمكانيات جديدة لتطوير وتحسين هذا المجال."
        
        return None
    
    def _analyze_social_perspective(self, topic, data):
        """تحليل من المنظور الاجتماعي"""
        social_keywords = ['مجتمع', 'ناس', 'ثقافة', 'تفاعل', 'علاقات', 'تأثير']
        answer = data.get('answer', '')
        
        if any(keyword in answer for keyword in social_keywords):
            return f"اجتماعياً، {topic} يؤثر على حياة الناس بطرق متعددة. يشكل جزءاً من النسيج الاجتماعي ويساهم في تطوير العلاقات والتفاعلات بين أفراد المجتمع."
        
        return f"من المنظور الاجتماعي، {topic} له تأثير على المجتمع وطريقة تفاعل الناس مع بعضهم البعض."
    
    def _analyze_future_perspective(self, topic, data):
        """تحليل من المنظور المستقبلي"""
        future_keywords = ['مستقبل', 'تطوير', 'تقدم', 'ابتكار', 'جديد', 'متقدم']
        answer = data.get('answer', '')
        
        if any(keyword in answer for keyword in future_keywords):
            return f"مستقبلياً، {topic} يحمل إمكانيات هائلة للتطوير والنمو. التطورات المتوقعة في هذا المجال قد تغير طريقة فهمنا وتعاملنا معه بشكل جذري."
        
        return f"المستقبل يحمل تطورات مثيرة لـ {topic}، مع إمكانيات لا محدودة للابتكار والتحسين."
    
    def _synthesize_discussion(self, topic, main_data, perspectives):
        """تجميع وتلخيص المناقشة"""
        print("🧩 أجمع وجهات النظر المختلفة...")
        time.sleep(1)
        
        result = f"📝 **تحليل شامل لموضوع: {topic}**\n\n"
        
        # المعلومات الأساسية
        result += f"**المعلومات الأساسية:**\n{main_data['answer']}\n\n"
        
        # وجهات النظر المختلفة
        if perspectives:
            result += "**تحليل متعدد الأوجه:**\n\n"
            for perspective_name, perspective_content in perspectives:
                result += f"{perspective_name}:\n{perspective_content}\n\n"
        
        # الخلاصة والتوليف
        result += self._generate_synthesis(topic, perspectives)
        
        # أسئلة للتفكير العميق
        result += self._generate_deep_questions(topic, main_data)
        
        return result
    
    def _generate_synthesis(self, topic, perspectives):
        """توليد خلاصة توليفية"""
        synthesis_templates = [
            f"**الخلاصة التوليفية:**\nبعد تحليل {topic} من زوايا متعددة، نجد أنه موضوع متعدد الأبعاد يتطلب فهماً شاملاً. كل منظور يضيف بُعداً مهماً لفهمنا الكامل للموضوع.",
            
            f"**التوليف والربط:**\nالمناقشة تكشف أن {topic} ليس مجرد مفهوم منعزل، بل جزء من منظومة معقدة تتفاعل فيها عوامل علمية وتاريخية واجتماعية وتقنية.",
            
            f"**الرؤية الشاملة:**\nمن خلال النظر إلى {topic} من منظورات متعددة، نحصل على فهم أعمق وأكثر تكاملاً يساعدنا في تقدير أهميته وتأثيره الحقيقي."
        ]
        
        return random.choice(synthesis_templates) + "\n\n"
    
    def _generate_deep_questions(self, topic, main_data):
        """توليد أسئلة للتفكير العميق"""
        questions = main_data.get('questions', [])
        
        # أسئلة إضافية للتفكير العميق
        deep_questions = [
            f"كيف يمكن أن يتطور {topic} في المستقبل؟",
            f"ما التحديات الرئيسية التي تواجه {topic}؟",
            f"كيف يؤثر {topic} على حياتنا اليومية؟",
            f"ما العلاقة بين {topic} والمجالات الأخرى؟",
            f"كيف يمكن الاستفادة من {topic} بشكل أفضل؟"
        ]
        
        all_questions = questions + deep_questions
        selected_questions = random.sample(all_questions, min(3, len(all_questions)))
        
        result = "**أسئلة للتفكير العميق:**\n"
        for i, question in enumerate(selected_questions, 1):
            result += f"{i}. {question}\n"
        
        return result
    
    def _discuss_unknown_topic(self, topic):
        """مناقشة موضوع غير موجود في قاعدة المعرفة"""
        print("🤔 أفكر في الموضوع من زوايا مختلفة...")
        time.sleep(2)
        
        result = f"📝 **مناقشة استكشافية حول: {topic}**\n\n"
        
        result += "**التفكير الأولي:**\n"
        result += f"هذا موضوع مثير للاهتمام! دعني أفكر فيه من زوايا مختلفة وأستكشف الجوانب المحتملة.\n\n"
        
        # تحليل الكلمات المفتاحية
        keywords = topic.split()
        result += "**تحليل المفاهيم:**\n"
        for keyword in keywords:
            if len(keyword) > 2:
                result += f"• {keyword}: يمكن أن يرتبط بمجالات متعددة ويحمل معاني مختلفة حسب السياق.\n"
        
        result += "\n**أسئلة استكشافية:**\n"
        exploratory_questions = [
            f"ما المقصود تحديداً بـ {topic}؟",
            f"في أي سياق يُستخدم {topic}؟",
            f"ما أهمية {topic} في الوقت الحالي؟",
            f"كيف يمكن تطبيق {topic} عملياً؟",
            f"ما التحديات المرتبطة بـ {topic}؟"
        ]
        
        for i, question in enumerate(exploratory_questions, 1):
            result += f"{i}. {question}\n"
        
        result += "\n🤔 هل يمكنك تقديم المزيد من التفاصيل حول هذا الموضوع لنتمكن من مناقشة أعمق؟"
        
        return result
    
    def interactive_discussion(self, topic):
        """مناقشة تفاعلية مع عرض مراحل التفكير"""
        print(f"🎯 بدء المناقشة التفاعلية حول: {topic}")
        print("=" * 80)
        
        # مرحلة 1: جمع المعلومات
        print("📚 المرحلة 1: جمع المعلومات الأساسية...")
        time.sleep(1)
        base_info = self._find_topic_info(topic)
        
        if base_info:
            print(f"✅ تم العثور على معلومات في قاعدة المعرفة")
            print(f"🔍 نوع المطابقة: {base_info['match_type']}")
        else:
            print("⚠️ لم يتم العثور على معلومات مباشرة - سأقوم بتحليل استكشافي")
        
        time.sleep(1)
        
        # مرحلة 2: التحليل المتعدد الأوجه
        print("\n🧠 المرحلة 2: التحليل من زوايا متعددة...")
        time.sleep(1)
        
        if base_info:
            print("🔬 أحلل من المنظور العلمي...")
            time.sleep(0.5)
            print("🏛️ أحلل من المنظور التاريخي...")
            time.sleep(0.5)
            print("💻 أحلل من المنظور التقني...")
            time.sleep(0.5)
            print("👥 أحلل من المنظور الاجتماعي...")
            time.sleep(0.5)
            print("🚀 أحلل من المنظور المستقبلي...")
            time.sleep(0.5)
        
        # مرحلة 3: التوليف والخلاصة
        print("\n🧩 المرحلة 3: توليف وجهات النظر...")
        time.sleep(1)
        
        # مرحلة 4: توليد الأسئلة العميقة
        print("❓ المرحلة 4: توليد أسئلة للتفكير العميق...")
        time.sleep(1)
        
        print("\n" + "=" * 80)
        print("📋 النتيجة النهائية:")
        print("=" * 80)
        
        # إجراء المناقشة الفعلية
        return self.conduct_self_discussion(topic)
