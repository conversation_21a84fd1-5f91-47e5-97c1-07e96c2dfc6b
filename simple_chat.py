# -*- coding: utf-8 -*-
"""
تفاعل بسيط مع النموذج العربي الذكي
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model
import re

# إعدادات النموذج
MODEL_NAME = "ml-T1-Simple-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
MAX_SEQUENCE_LEN = 12

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

def generate_text(seed_text: str, next_words: int, model, tokenizer, max_sequence_len: int):
    """توليد نص ذكي"""
    output_text = seed_text
    used_words = []
    
    for i in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]
        
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        
        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]
            
            # Top-k sampling للتنوع
            top_k = min(5, len(predicted_probs))
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]
            
            # اختيار كلمة مناسبة
            selected_word = None
            for idx in sorted_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) >= 2:
                    # تجنب التكرار المفرط
                    recent_words = used_words[-3:] if len(used_words) >= 3 else used_words
                    if candidate_word not in recent_words:
                        selected_word = candidate_word
                        break
            
            if not selected_word:
                # استخدم أفضل كلمة متاحة
                predicted_index = sorted_indices[0]
                selected_word = tokenizer.index_word.get(predicted_index, "")
            
            if not selected_word:
                break
            
            output_text += " " + selected_word
            used_words.append(selected_word)
            
            # الحفاظ على آخر 5 كلمات فقط
            if len(used_words) > 5:
                used_words = used_words[-5:]
        
        except Exception:
            break
    
    return output_text

def analyze_intent(text: str) -> str:
    """تحليل نية المستخدم"""
    text_lower = text.lower()
    
    # التحيات
    if any(word in text_lower for word in ["السلام عليكم", "مرحبا", "أهلا", "هلا", "صباح", "مساء"]):
        return "تحية"
    elif any(word in text_lower for word in ["كيف حالك", "شلونك", "كيفك"]):
        return "سؤال عن الحال"
    elif any(word in text_lower for word in ["من أنت", "ما اسمك"]):
        return "سؤال هوية"
    elif any(word in text_lower for word in ["ما هو", "ما هي", "تعريف"]):
        return "تعريف"
    elif any(word in text_lower for word in ["عاصمة", "مكان", "أين"]):
        return "جغرافيا"
    elif any(word in text_lower for word in ["شكرا", "شكراً"]):
        return "شكر"
    else:
        return "عام"

def get_smart_response(user_input: str, intent: str, model, tokenizer, max_sequence_len: int) -> str:
    """توليد إجابة ذكية"""
    
    # ردود جاهزة للتحيات
    if intent == "تحية":
        responses = [
            "وعليكم السلام ورحمة الله وبركاته",
            "أهلاً وسهلاً بك",
            "مرحباً بك كيف يمكنني مساعدتك",
            "صباح النور",
            "مساء النور"
        ]
        return np.random.choice(responses)
    
    elif intent == "سؤال عن الحال":
        responses = [
            "الحمد لله بخير وأنت كيف حالك",
            "تمام الحمد لله وأنت شلونك",
            "منيح الحمد لله كيف صحتك"
        ]
        return np.random.choice(responses)
    
    elif intent == "سؤال هوية":
        responses = [
            "أنا أمؤلي نموذج ذكاء اصطناعي عربي",
            "اسمي أمؤلي وأنا مساعد ذكي باللغة العربية",
            "أنا نموذج ذكاء اصطناعي مصمم لمساعدتك"
        ]
        return np.random.choice(responses)
    
    elif intent == "شكر":
        responses = [
            "العفو لا شكر على واجب",
            "أهلاً وسهلاً",
            "حياك الله"
        ]
        return np.random.choice(responses)
    
    # للأسئلة الأخرى استخدم النموذج
    else:
        return generate_text(
            user_input,
            next_words=8,
            model=model,
            tokenizer=tokenizer,
            max_sequence_len=max_sequence_len
        )

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً بك في أمؤلي-T1 الذكي!")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    if not os.path.exists(MODEL_FILE):
        print(f"❌ ملف النموذج غير موجود: {MODEL_FILE}")
        print("يرجى تشغيل offline_arabic_model.py أولاً")
        return
    
    if not os.path.exists(TOKENIZER_FILE):
        print(f"❌ ملف Tokenizer غير موجود: {TOKENIZER_FILE}")
        print("يرجى تشغيل offline_arabic_model.py أولاً")
        return
    
    # تحميل النموذج
    print("📥 تحميل النموذج...")
    try:
        model = load_model(MODEL_FILE)
        print(f"✅ تم تحميل النموذج: {MODEL_FILE}")

        with open(TOKENIZER_FILE, "rb") as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل Tokenizer: {TOKENIZER_FILE}")

        print(f"🧠 النموذج جاهز مع {model.count_params():,} معامل")
        print(f"📚 حجم القاموس: {len(tokenizer.word_index):,} كلمة")

    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        return
    
    # حلقة التفاعل
    print("\n🎯 النموذج جاهز للتفاعل!")
    print("💡 يمكنك:")
    print("   - طرح أسئلة: 'ما هو الذكاء الاصطناعي؟'")
    print("   - السؤال عن العواصم: 'عاصمة السعودية'")
    print("   - التحية: 'مرحبا'")
    print("   - كتابة 'خروج' لإنهاء البرنامج")
    print("-" * 50)
    
    conversation_count = 0
    
    while True:
        try:
            user_input = input(f"\n[{conversation_count + 1}] 🔤 أدخل نصك: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ["خروج", "exit", "quit"]:
                print("👋 شكراً لاستخدام أمؤلي-T1!")
                break
            
            # تنظيف الإدخال
            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي صالح")
                continue
            
            # تحليل النية
            intent = analyze_intent(clean_user_input)
            print(f"🔍 نوع الإدخال: {intent}")

            # توليد الإجابة
            print("⏳ جاري التفكير...")

            response = get_smart_response(
                clean_user_input,
                intent,
                model,
                tokenizer,
                MAX_SEQUENCE_LEN
            )

            print(f"\n🤖 الإجابة:")
            print(f"📝 {response}")
            
            # إحصائيات بسيطة
            response_words = len(response.split())
            input_words = len(clean_user_input.split())
            print(f"📊 الإدخال: {input_words} كلمة | الإخراج: {response_words} كلمة")

            conversation_count += 1
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. إلى اللقاء!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            print("💡 جرب إدخال نص أبسط")
            continue

if __name__ == "__main__":
    main()
