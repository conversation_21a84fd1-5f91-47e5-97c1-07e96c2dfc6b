# -*- coding: utf-8 -*-
"""
نموذج ذكاء اصطناعي عربي ذكي ومتطور
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout
from tensorflow.keras.utils import to_categorical
import re
import random

# إعدادات النموذج
MODEL_NAME = "smart-arabic-ai"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"

# معاملات محسنة
MAX_SEQUENCE_LEN = 15
EMBEDDING_DIM = 128
LSTM_UNITS = 64
DROPOUT_RATE = 0.2
EPOCHS = 8
BATCH_SIZE = 32

class SmartArabicAI:
    """نظام ذكاء اصطناعي عربي متطور"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.conversation_memory = []
        self.knowledge_base = self.create_knowledge_base()
        
    def create_knowledge_base(self):
        """قاعدة معرفة شاملة"""
        return {
            # معلومات أساسية
            "الذكاء الاصطناعي": "الذكاء الاصطناعي هو تقنية متطورة تحاكي قدرات العقل البشري في التفكير والتعلم واتخاذ القرارات. يستخدم في الطب والتعليم والنقل وغيرها من المجالات لتحسين حياة الإنسان.",
            
            "عاصمة السعودية": "عاصمة المملكة العربية السعودية هي مدينة الرياض، وهي أكبر مدن المملكة ومركزها السياسي والاقتصادي. تقع في وسط شبه الجزيرة العربية وتضم العديد من المعالم المهمة.",
            
            "عاصمة مصر": "عاصمة جمهورية مصر العربية هي القاهرة، والتي تُلقب بأم الدنيا. تقع على ضفاف نهر النيل وتضم العديد من الآثار الفرعونية والإسلامية المهمة.",
            
            "الصحة": "الصحة هي حالة من اكتمال السلامة البدنية والعقلية والاجتماعية. للمحافظة على الصحة يجب ممارسة الرياضة وتناول الغذاء الصحي والنوم الكافي وتجنب التدخين والضغوط النفسية.",
            
            "التعليم": "التعليم هو عملية اكتساب المعرفة والمهارات والقيم. يعتبر التعليم حقاً أساسياً لكل إنسان وهو أساس التقدم والحضارة. يساعد التعليم في بناء شخصية الإنسان وتطوير قدراته.",
            
            "العلم": "العلم هو المعرفة المنظمة التي نحصل عليها من خلال الملاحظة والتجريب. العلم يساعدنا على فهم العالم من حولنا وحل المشاكل التي نواجهها في الحياة.",
            
            "الرياضة": "الرياضة هي نشاط بدني منظم يهدف إلى تحسين اللياقة البدنية والصحة العامة. تساعد الرياضة في تقوية العضلات وتحسين الدورة الدموية وتقليل التوتر والضغط النفسي."
        }
    
    def get_smart_response(self, user_input):
        """الحصول على إجابة ذكية"""
        user_input_lower = user_input.lower()
        
        # البحث في قاعدة المعرفة
        for key, value in self.knowledge_base.items():
            if any(word in user_input_lower for word in key.lower().split()):
                # إضافة سؤال تفاعلي
                follow_up = self.get_follow_up_question(key)
                return f"{value} {follow_up}"
        
        # إجابات للتحيات
        if any(word in user_input_lower for word in ["مرحبا", "أهلا", "السلام عليكم", "صباح", "مساء"]):
            greetings = [
                "أهلاً وسهلاً بك! سعيد جداً بلقائك. كيف يمكنني مساعدتك اليوم؟ هل لديك سؤال معين أم تريد أن نتحدث عن موضوع يهمك؟",
                "مرحباً بك! نورت المكان بحضورك. أتمنى أن تكون بأفضل حال. ما الموضوع الذي تود مناقشته معي اليوم؟",
                "وعليكم السلام ورحمة الله وبركاته! أهلاً وسهلاً بك. كيف حالك وكيف كان يومك؟ هل هناك شيء مثير حدث معك؟"
            ]
            return random.choice(greetings)
        
        # إجابات لأسئلة الحال
        if any(word in user_input_lower for word in ["كيف حالك", "شلونك", "كيفك"]):
            responses = [
                "الحمد لله أنا بخير تماماً وأشعر بالحماس للتعلم والمساعدة! وأنت كيف حالك؟ أتمنى أن تكون بأفضل صحة. ما الذي يجعلك سعيداً هذه الأيام؟",
                "تمام الحمد لله، أشعر بالسعادة عندما أتحدث مع أشخاص مثلك! وأنت شلونك؟ هل هناك شيء جميل تريد أن تشاركه معي؟"
            ]
            return random.choice(responses)
        
        # إجابات لأسئلة الهوية
        if any(word in user_input_lower for word in ["من أنت", "ما اسمك", "مين انت"]):
            responses = [
                "أنا أمؤلي، نموذج ذكاء اصطناعي عربي مصمم لمساعدتك والتحدث معك. أحب التعلم والنقاش في مواضيع مختلفة. ما الموضوع الذي يثير اهتمامك أكثر؟",
                "اسمي أمؤلي وأنا مساعد ذكي باللغة العربية. أستمتع بالمحادثات المفيدة وأحب مساعدة الناس. هل تريد أن نتحدث عن شيء معين؟"
            ]
            return random.choice(responses)
        
        # إجابة عامة تفاعلية
        general_responses = [
            "هذا سؤال مثير للاهتمام! أعتقد أن هذا الموضوع له جوانب كثيرة يمكن استكشافها. ما رأيك لو نناقش هذا أكثر؟ أي جانب يهمك تحديداً؟",
            "موضوع شيق جداً! أحب هذا النوع من الأسئلة لأنه يجعلني أفكر بعمق. هل لديك تجربة شخصية مع هذا الموضوع؟ أم تريد معرفة المزيد عنه؟",
            "سؤال ذكي! هذا يذكرني بمواضيع مشابهة ومهمة. أعتقد أن هناك الكثير لنتعلمه هنا. ما الذي ألهمك لطرح هذا السؤال؟",
            "نقطة مهمة جداً! أرى أنك تفكر بطريقة إبداعية. هذا يفتح المجال لأسئلة أخرى مثيرة. هل تريد أن نستكشف هذا الموضوع أكثر؟"
        ]
        return random.choice(general_responses)
    
    def get_follow_up_question(self, topic):
        """الحصول على سؤال متابعة حسب الموضوع"""
        follow_ups = {
            "الذكاء الاصطناعي": [
                "هل تريد معرفة المزيد عن تطبيقات الذكاء الاصطناعي في حياتنا اليومية؟",
                "ما رأيك في مستقبل الذكاء الاصطناعي؟ هل تعتقد أنه سيغير حياتنا كثيراً؟",
                "هل جربت أدوات الذكاء الاصطناعي من قبل؟ أي منها أعجبك أكثر؟"
            ],
            "عاصمة السعودية": [
                "هل زرت الرياض من قبل؟ أم تخطط لزيارتها؟",
                "ما الذي تعرفه عن معالم الرياض المشهورة؟",
                "هل تريد معرفة المزيد عن تاريخ المملكة العربية السعودية؟"
            ],
            "الصحة": [
                "ما هي عاداتك الصحية اليومية؟ هل تمارس الرياضة بانتظام؟",
                "ما نوع الطعام الصحي الذي تفضله؟",
                "كيف تتعامل مع الضغوط النفسية في حياتك؟"
            ],
            "التعليم": [
                "ما هو مجال دراستك أو اهتمامك؟",
                "كيف تفضل التعلم - بالقراءة أم بالممارسة العملية؟",
                "هل تستخدم التقنية في التعلم؟ أي الأدوات تجدها مفيدة؟"
            ]
        }
        
        questions = follow_ups.get(topic, [
            "ما رأيك في هذا الموضوع؟",
            "هل لديك تجربة شخصية مع هذا؟",
            "ما الذي تود معرفته أكثر؟"
        ])
        
        return random.choice(questions)
    
    def clean_input(self, text):
        """تنظيف النص"""
        text = re.sub(r'[^\u0600-\u06FF\s\?\!\.]', '', text)
        return text.strip()
    
    def chat(self):
        """بدء المحادثة"""
        print("🤖 مرحباً! أنا أمؤلي، مساعدك الذكي باللغة العربية")
        print("=" * 60)
        print("💡 يمكنني مساعدتك في:")
        print("   ✨ الإجابة على الأسئلة العامة")
        print("   ✨ معلومات عن الدول والعواصم")
        print("   ✨ نصائح حول الصحة والتعليم")
        print("   ✨ محادثة تفاعلية ومفيدة")
        print("   - اكتب 'خروج' لإنهاء المحادثة")
        print("-" * 60)
        
        conversation_count = 0
        
        while True:
            try:
                user_input = input(f"\n[{conversation_count + 1}] 💬 تحدث معي: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ["خروج", "exit", "quit", "انتهاء"]:
                    print("\n👋 كان من دواعي سروري التحدث معك!")
                    print("أتمنى أن تكون استفدت من محادثتنا. إلى اللقاء!")
                    break
                
                # تنظيف الإدخال
                clean_user_input = self.clean_input(user_input)
                if not clean_user_input:
                    print("❌ يرجى إدخال نص عربي صالح")
                    continue
                
                # الحصول على الإجابة
                print("🤔 أفكر في إجابة مفيدة...")
                response = self.get_smart_response(clean_user_input)
                
                print(f"\n🤖 أمؤلي:")
                print(f"📝 {response}")
                
                # حفظ المحادثة
                self.conversation_memory.append({
                    'user': clean_user_input,
                    'ai': response
                })
                
                # إحصائيات
                response_words = len(response.split())
                input_words = len(clean_user_input.split())
                print(f"\n📊 الإدخال: {input_words} كلمة | الإجابة: {response_words} كلمة")
                
                conversation_count += 1
                
                # نصيحة كل 5 محادثات
                if conversation_count % 5 == 0:
                    print(f"\n💡 رائع! لقد أجرينا {conversation_count} محادثات مفيدة!")
                    print("   جرب أسئلة مختلفة للحصول على معلومات متنوعة")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج. كان لقاءً ممتعاً!")
                break
            except Exception as e:
                print(f"❌ حدث خطأ: {e}")
                print("💡 جرب إعادة صياغة السؤال")
                continue

def main():
    """الدالة الرئيسية"""
    ai = SmartArabicAI()
    ai.chat()

if __name__ == "__main__":
    main()
