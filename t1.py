# -*- coding: utf-8 -*-
"""
نموذج "أمؤلي-T1" للتعلم العميق الذاتي من الإنترنت وحفظه في ملف.
تعريفات:
- يجمع نصوص مقالات ويكيبيديا العربية لمجموعة مواضيع.
- ينظف النصوص ويولد تسلسلات لتدريب شبكة LSTM أعمق.
- يحفظ ويحمّل الموديل والـTokenizer.
- يدرب نفسه ذاتيًا كل 24 ساعة، ثم يتيح التفاعل واستكمال الجمل.
"""

import os
import time
import requests
import re
import pickle
import numpy as np
from bs4 import BeautifulSoup
import datetime

# مكتبات TensorFlow/Keras للتعلم العميق المتقدم
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential, Model, load_model
from tensorflow.keras.layers import (
    Embedding, LSTM, Dense, Dropout, BatchNormalization,
    MultiHeadAttention, LayerNormalization, Input,
    GlobalAveragePooling1D, Concatenate, Bidirectional,
    GRU, Conv1D, MaxPooling1D, Flatten, Add
)
from tensorflow.keras.utils import to_categorical
try:
    from tensorflow.keras.optimizers import Adam, AdamW
except ImportError:
    from tensorflow.keras.optimizers.legacy import Adam
    from tensorflow.keras.optimizers.experimental import AdamW
from tensorflow.keras.callbacks import (
    EarlyStopping, ReduceLROnPlateau, ModelCheckpoint,
    LearningRateScheduler, TensorBoard
)
from tensorflow.keras.regularizers import l2
import tensorflow.keras.backend as K

# تحسين الأداء والذاكرة
try:
    physical_devices = tf.config.experimental.list_physical_devices('GPU')
    if physical_devices:
        tf.config.experimental.set_memory_growth(physical_devices[0], True)
        print("✅ تم تفعيل تحسين ذاكرة GPU")
except:
    print("ℹ️ لا توجد GPU متاحة، سيتم استخدام CPU")

# تفعيل التنفيذ التفاعلي لتفادي أخطاء numpy() في Graph Mode
tf.config.run_functions_eagerly(True)
try:
    tf.compat.v1.enable_eager_execution()
except:
    pass

# اسم الموديل وملفات الحفظ - النسخة المحسنة
MODEL_NAME = "ml-T1-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
ARTICLES_DUMP = "wikipedia_dump.txt"
TRAINING_HISTORY = f"{MODEL_NAME}_history.pkl"
KNOWLEDGE_BASE = f"{MODEL_NAME}_knowledge.pkl"

# إعدادات النموذج المحسن
MAX_SEQUENCE_LEN = 32   # زيادة طول التسلسل لفهم أفضل للسياق
EMBEDDING_DIM = 256     # أبعاد التضمين
LSTM_UNITS = 512        # وحدات LSTM أكبر
DENSE_UNITS = 256       # وحدات الطبقة المكتملة
DROPOUT_RATE = 0.3      # معدل الإسقاط لمنع الإفراط في التعلم
LEARNING_RATE = 0.001   # معدل التعلم
BATCH_SIZE = 64         # حجم الدفعة
EPOCHS = 10             # عدد العصور
VALIDATION_SPLIT = 0.2  # نسبة بيانات التحقق

#############################
# 1. التحقق من اتصال الإنترنت
#############################

def is_connected() -> bool:
    """
    يتحقق من وجود اتصال بالإنترنت عبر الوصول إلى google.com.
    """
    try:
        requests.get("https://www.google.com", timeout=5)
        return True
    except:
        return False

#############################
# 2. جمع المقالات من ويكيبيديا
#############################

def fetch_wikipedia_article(title: str) -> str:
    """
    يحمل نص المقالة من ويكيبيديا العربية بعنوان 'title'.
    يعيد نص الفقرات (<p>) كنص موحد.
    """
    url = f"https://ar.wikipedia.org/wiki/{title}"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            print(f"⚠️ فشل تحميل الصفحة: {title} (الحالة: {response.status_code})")
            return ""
        soup = BeautifulSoup(response.text, 'html.parser')
        paragraphs = soup.find_all('p')
        text = ""
        for p in paragraphs:
            text += p.get_text()
        return text
    except Exception as e:
        print(f"⚠️ خطأ في تحميل {title}: {e}")
        return ""

def collect_articles(topics: list, delay: float = 0.1) -> str:
    """
    يجمع نصوص جميع المقالات في قائمة 'topics' بشكل متسلسل مع تأخير بسيط.
    يحفظ النصوص في متغير ويعيده.
    """
    all_text = []
    for idx, topic in enumerate(topics):
        print(f"تحميل ({idx+1}/{len(topics)}) موضوع: {topic} ...")
        art = fetch_wikipedia_article(topic)
        if art:
            all_text.append(art)
        time.sleep(delay)
    combined = "\n".join(all_text)
    # حفظ نسخة من النصوص في ملف
    with open(ARTICLES_DUMP, "w", encoding="utf-8") as f:
        f.write(combined)
    print(f"📝 تم حفظ المقالات في: {ARTICLES_DUMP}")
    return combined

#############################
# 3. تجهيز النصوص للتدريب
#############################

def preprocess_text(text: str) -> str:
    """
    ينظف النص بطريقة متقدمة: يحذف كل ما هو ليس حرفًا عربيًا أو مسافة.
    يحافظ على علامات الترقيم المهمة ويقلص المسافات المتكررة.
    """
    # إزالة الأرقام والرموز الخاصة مع الحفاظ على علامات الترقيم العربية
    text = re.sub(r'[^\u0600-\u06FF\s\.\،\؟\!\:\؛]', ' ', text)

    # تنظيف المسافات المتكررة
    text = re.sub(r'\s+', ' ', text).strip()

    # إزالة الجمل القصيرة جداً (أقل من 3 كلمات)
    sentences = text.split('.')
    filtered_sentences = [s.strip() for s in sentences if len(s.strip().split()) >= 3]

    return '. '.join(filtered_sentences)

def advanced_text_preprocessing(text: str) -> list:
    """
    معالجة متقدمة للنصوص تتضمن:
    - تنظيف النص
    - تقسيم إلى جمل
    - فلترة الجمل القصيرة والطويلة جداً
    - إزالة التكرارات
    """
    # تنظيف أساسي
    cleaned_text = preprocess_text(text)

    # تقسيم إلى جمل
    sentences = re.split(r'[\.؟!\n]', cleaned_text)

    # فلترة الجمل
    filtered_sentences = []
    seen_sentences = set()

    for sentence in sentences:
        sentence = sentence.strip()
        words = sentence.split()

        # تجاهل الجمل القصيرة جداً أو الطويلة جداً
        if 3 <= len(words) <= 50:
            # تجاهل الجمل المكررة
            if sentence not in seen_sentences:
                filtered_sentences.append(sentence)
                seen_sentences.add(sentence)

    return filtered_sentences

def create_sequences(tokenizer: Tokenizer, corpus: list, max_sequence_len: int):
    """
    يحمّل النصوص المُعالجة عبر الـ tokenizer ثم يولّد تسلسلات N-gram:
    لكل جملة، ينشئ n-gram sequences حيث n = max_sequence_len.
    يعيد X (تسلسلات الإدخال) و y (الكلمة الهدف) بعد التصنيف إلى one-hot.
    """
    input_sequences = []
    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]
        for i in range(max_sequence_len, len(token_list) + 1):
            n_gram_sequence = token_list[i - max_sequence_len: i]
            input_sequences.append(n_gram_sequence)
    input_sequences = np.array(input_sequences)
    
    # تقسيم التسلسلات إلى X و y
    X = input_sequences[:, :-1]  # كل التسلسل عدا الكلمة الأخيرة هو المدخل
    y = input_sequences[:, -1]   # الكلمة الأخيرة هي الهدف
    
    y = to_categorical(y, num_classes=len(tokenizer.word_index) + 1)
    return X, y

#############################
# 4. بناء الشبكة العميقة
#############################

def build_advanced_model(vocab_size: int, max_sequence_len: int):
    """
    يبني نموذج متقدم للتعلم العميق مع:
    - طبقات Embedding محسنة
    - طبقات LSTM ثنائية الاتجاه
    - طبقات Attention
    - Dropout للتنظيم
    - BatchNormalization للاستقرار
    """
    model = Sequential()

    # طبقة التضمين المحسنة
    model.add(Embedding(
        input_dim=vocab_size,
        output_dim=EMBEDDING_DIM,
        input_length=max_sequence_len - 1,
        mask_zero=True  # للتعامل مع التسلسلات المتغيرة الطول
    ))

    # طبقة Dropout للتنظيم
    model.add(Dropout(DROPOUT_RATE))

    # طبقة LSTM ثنائية الاتجاه الأولى
    model.add(Bidirectional(LSTM(
        LSTM_UNITS,
        return_sequences=True,
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))

    # طبقة BatchNormalization
    model.add(BatchNormalization())

    # طبقة LSTM ثنائية الاتجاه الثانية
    model.add(Bidirectional(LSTM(
        LSTM_UNITS // 2,
        return_sequences=False,
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))

    # طبقة BatchNormalization
    model.add(BatchNormalization())

    # طبقة مكتملة الاتصال مع Dropout
    model.add(Dense(DENSE_UNITS, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))

    # طبقة الإخراج
    model.add(Dense(vocab_size, activation='softmax'))

    # تجميع النموذج مع محسن متقدم
    optimizer = AdamW(learning_rate=LEARNING_RATE, weight_decay=0.01)

    model.compile(
        loss='categorical_crossentropy',
        optimizer=optimizer,
        metrics=['accuracy', 'top_k_categorical_accuracy']
    )

    return model

def build_transformer_model(vocab_size: int, max_sequence_len: int):
    """
    يبني نموذج Transformer مبسط للتعلم العميق المتقدم
    """
    # طبقة الإدخال
    inputs = Input(shape=(max_sequence_len - 1,))

    # طبقة التضمين
    embedding = Embedding(vocab_size, EMBEDDING_DIM, mask_zero=True)(inputs)

    # طبقة Multi-Head Attention
    attention = MultiHeadAttention(
        num_heads=8,
        key_dim=EMBEDDING_DIM // 8
    )(embedding, embedding)

    # إضافة الاتصال المتبقي والتطبيع
    attention = Add()([embedding, attention])
    attention = LayerNormalization()(attention)

    # طبقة Feed Forward
    ff = Dense(DENSE_UNITS, activation='relu')(attention)
    ff = Dropout(DROPOUT_RATE)(ff)
    ff = Dense(EMBEDDING_DIM)(ff)

    # إضافة الاتصال المتبقي والتطبيع
    ff = Add()([attention, ff])
    ff = LayerNormalization()(ff)

    # طبقة التجميع العالمي
    pooled = GlobalAveragePooling1D()(ff)

    # طبقة الإخراج
    outputs = Dense(vocab_size, activation='softmax')(pooled)

    # إنشاء النموذج
    model = Model(inputs=inputs, outputs=outputs)

    # تجميع النموذج
    optimizer = AdamW(learning_rate=LEARNING_RATE, weight_decay=0.01)
    model.compile(
        loss='categorical_crossentropy',
        optimizer=optimizer,
        metrics=['accuracy', 'top_k_categorical_accuracy']
    )

    return model

# دالة لاختيار نوع النموذج
def build_model(vocab_size: int, max_sequence_len: int, model_type: str = "advanced"):
    """
    يبني النموذج حسب النوع المطلوب:
    - "simple": النموذج البسيط الأصلي
    - "advanced": النموذج المتقدم مع LSTM ثنائي الاتجاه
    - "transformer": نموذج Transformer مبسط
    """
    if model_type == "transformer":
        return build_transformer_model(vocab_size, max_sequence_len)
    elif model_type == "advanced":
        return build_advanced_model(vocab_size, max_sequence_len)
    else:
        # النموذج البسيط الأصلي
        model = Sequential()
        model.add(Embedding(input_dim=vocab_size, output_dim=128, input_length=max_sequence_len - 1))
        model.add(LSTM(256, return_sequences=False))
        model.add(Dense(vocab_size, activation='softmax'))
        model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])
        return model

#############################
# 5. دوال التدريب المتقدم والتقييم
#############################

def create_callbacks(model_file: str):
    """
    ينشئ callbacks للتدريب المتقدم
    """
    callbacks = [
        # إيقاف مبكر عند عدم التحسن
        EarlyStopping(
            monitor='val_loss',
            patience=3,
            restore_best_weights=True,
            verbose=1
        ),

        # تقليل معدل التعلم عند التوقف
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=2,
            min_lr=1e-7,
            verbose=1
        ),

        # حفظ أفضل نموذج
        ModelCheckpoint(
            filepath=model_file,
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        ),

        # TensorBoard للمراقبة
        TensorBoard(
            log_dir=f'./logs/{MODEL_NAME}',
            histogram_freq=1,
            write_graph=True
        )
    ]

    return callbacks

def advanced_training(model, X, y, callbacks):
    """
    تدريب متقدم مع تقسيم البيانات والمراقبة
    """
    print("🚀 بدء التدريب المتقدم...")

    # تدريب النموذج مع callbacks
    history = model.fit(
        X, y,
        epochs=EPOCHS,
        batch_size=BATCH_SIZE,
        validation_split=VALIDATION_SPLIT,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )

    return history

def evaluate_model_performance(model, X, y):
    """
    تقييم أداء النموذج
    """
    print("📊 تقييم أداء النموذج...")

    # تقييم على بيانات التدريب
    train_loss, train_acc, train_top_k = model.evaluate(X, y, verbose=0)

    print(f"📈 نتائج التقييم:")
    print(f"   - Loss: {train_loss:.4f}")
    print(f"   - Accuracy: {train_acc:.4f}")
    print(f"   - Top-K Accuracy: {train_top_k:.4f}")

    return {
        'loss': train_loss,
        'accuracy': train_acc,
        'top_k_accuracy': train_top_k
    }

def save_training_history(history, filename: str):
    """
    حفظ تاريخ التدريب
    """
    with open(filename, 'wb') as f:
        pickle.dump(history.history, f)
    print(f"✅ تم حفظ تاريخ التدريب في: {filename}")

def load_training_history(filename: str):
    """
    تحميل تاريخ التدريب
    """
    if os.path.exists(filename):
        with open(filename, 'rb') as f:
            history = pickle.load(f)
        print(f"✅ تم تحميل تاريخ التدريب من: {filename}")
        return history
    return None

#############################
# 6. حفظ وتحميل النموذج والـTokenizer
#############################

def save_tokenizer(tokenizer: Tokenizer, filename: str):
    with open(filename, 'wb') as f:
        pickle.dump(tokenizer, f)
    print(f"✅ تم حفظ الـ Tokenizer في: {filename}")

def load_tokenizer(filename: str) -> Tokenizer:
    if os.path.exists(filename):
        with open(filename, 'rb') as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل الـ Tokenizer من: {filename}")
        return tokenizer
    else:
        print("⚠️ ملف الـ Tokenizer غير موجود. سيتم إنشاء Tokenizer جديد.")
        return None

def save_model_file(model: tf.keras.Model, filename: str):
    model.save(filename)
    print(f"✅ تم حفظ الموديل في: {filename}")

def load_model_file(filename: str) -> tf.keras.Model:
    if os.path.exists(filename):
        model = load_model(filename)
        print(f"✅ تم تحميل الموديل من: {filename}")
        return model
    else:
        print("⚠️ لم يتم العثور على ملف الموديل. سيتم تدريب موديل جديد.")
        return None

#############################
# 6. تنظيف مدخل المستخدم
#############################

def clean_input(text: str) -> str:
    """
    ينظف مدخل المستخدم: يحذف أي رمز ليس عربيًا أو مسافة.
    """
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

#############################
# 7. دالة لتوليد نص (تنبؤ الكلمات تتابعي)
#############################

def generate_text_advanced(seed_text: str, next_words: int, model: tf.keras.Model,
                          tokenizer: Tokenizer, max_sequence_len: int,
                          temperature: float = 0.7, top_k: int = 5) -> str:
    """
    توليد نص متقدم مع:
    - Temperature sampling للتحكم في الإبداع
    - Top-K sampling لتحسين جودة النص
    - معالجة أفضل للسياق
    """
    output_text = seed_text

    for _ in range(next_words):
        # تحضير التسلسل
        token_list = tokenizer.texts_to_sequences([output_text])[0]

        # أخذ آخر max_sequence_len-1 كلمة فقط للسياق
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]

        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')

        # التنبؤ
        predicted_probs = model.predict(token_list, verbose=0)[0]

        # تطبيق Temperature
        if temperature > 0:
            predicted_probs = np.log(predicted_probs + 1e-8) / temperature
            predicted_probs = np.exp(predicted_probs)
            predicted_probs = predicted_probs / np.sum(predicted_probs)

        # Top-K sampling
        if top_k > 0:
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            top_k_probs = predicted_probs[top_k_indices]
            top_k_probs = top_k_probs / np.sum(top_k_probs)
            predicted_index = np.random.choice(top_k_indices, p=top_k_probs)
        else:
            predicted_index = np.random.choice(len(predicted_probs), p=predicted_probs)

        predicted_word = tokenizer.index_word.get(predicted_index, "")
        if not predicted_word:
            break

        output_text += " " + predicted_word

    return output_text

def generate_text(seed_text: str, next_words: int, model: tf.keras.Model,
                  tokenizer: Tokenizer, max_sequence_len: int) -> str:
    """
    دالة توليد النص الأساسية (للتوافق مع الكود القديم)
    """
    return generate_text_advanced(seed_text, next_words, model, tokenizer, max_sequence_len)

def intelligent_response(question: str, model: tf.keras.Model, tokenizer: Tokenizer,
                        max_sequence_len: int) -> str:
    """
    إجابة ذكية على الأسئلة مع فهم السياق
    """
    # تنظيف السؤال
    clean_question = clean_input(question)

    # توليد عدة إجابات مختلفة
    responses = []
    for temp in [0.3, 0.7, 1.0]:  # درجات حرارة مختلفة
        response = generate_text_advanced(
            clean_question,
            next_words=15,
            model=model,
            tokenizer=tokenizer,
            max_sequence_len=max_sequence_len,
            temperature=temp,
            top_k=10
        )
        responses.append(response)

    # اختيار أفضل إجابة (الأطول والأكثر تماسكاً)
    best_response = max(responses, key=lambda x: len(x.split()))

    return best_response

def context_aware_generation(context: str, query: str, model: tf.keras.Model,
                           tokenizer: Tokenizer, max_sequence_len: int) -> str:
    """
    توليد نص مع فهم السياق المعطى
    """
    # دمج السياق مع الاستعلام
    combined_input = f"{context} {query}"

    # تنظيف النص
    clean_input_text = clean_input(combined_input)

    # توليد الإجابة
    response = generate_text_advanced(
        clean_input_text,
        next_words=20,
        model=model,
        tokenizer=tokenizer,
        max_sequence_len=max_sequence_len,
        temperature=0.6,
        top_k=8
    )

    return response

#############################
# 8. نظام الذاكرة وقاعدة المعرفة المتقدمة
#############################

class KnowledgeBase:
    """
    قاعدة معرفة متقدمة لحفظ واسترجاع المعلومات
    """
    def __init__(self):
        self.knowledge = {}
        self.conversation_history = []
        self.user_preferences = {}
        self.topic_expertise = {}

    def add_knowledge(self, topic: str, content: str):
        """إضافة معرفة جديدة"""
        if topic not in self.knowledge:
            self.knowledge[topic] = []
        self.knowledge[topic].append(content)

    def get_knowledge(self, topic: str) -> list:
        """استرجاع المعرفة حول موضوع معين"""
        return self.knowledge.get(topic, [])

    def add_conversation(self, user_input: str, ai_response: str, rating: int = None):
        """إضافة محادثة إلى التاريخ"""
        conversation = {
            'timestamp': datetime.datetime.now(),
            'user_input': user_input,
            'ai_response': ai_response,
            'rating': rating
        }
        self.conversation_history.append(conversation)

    def get_context(self, current_input: str, max_history: int = 3) -> str:
        """الحصول على السياق من المحادثات السابقة"""
        if not self.conversation_history:
            return ""

        recent_conversations = self.conversation_history[-max_history:]
        context_parts = []

        for conv in recent_conversations:
            context_parts.append(f"المستخدم: {conv['user_input']}")
            context_parts.append(f"الذكي: {conv['ai_response']}")

        return " ".join(context_parts)

    def save_to_file(self, filename: str):
        """حفظ قاعدة المعرفة في ملف"""
        with open(filename, 'wb') as f:
            pickle.dump(self.__dict__, f)

    def load_from_file(self, filename: str):
        """تحميل قاعدة المعرفة من ملف"""
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                data = pickle.load(f)
                self.__dict__.update(data)
            return True
        return False

def analyze_user_intent(text: str) -> str:
    """
    تحليل نية المستخدم من النص
    """
    text_lower = text.lower()

    # أنواع الأسئلة
    if any(word in text_lower for word in ["ما هو", "ما هي", "تعريف", "معنى"]):
        return "definition"
    elif any(word in text_lower for word in ["كيف", "طريقة", "خطوات"]):
        return "how_to"
    elif any(word in text_lower for word in ["لماذا", "سبب", "علة"]):
        return "explanation"
    elif any(word in text_lower for word in ["متى", "وقت", "تاريخ"]):
        return "time"
    elif any(word in text_lower for word in ["أين", "مكان", "موقع"]):
        return "location"
    elif any(word in text_lower for word in ["هل", "أم", "؟"]):
        return "yes_no"
    else:
        return "general"

def enhanced_intelligent_response(question: str, model: tf.keras.Model,
                                tokenizer: Tokenizer, max_sequence_len: int,
                                knowledge_base: KnowledgeBase) -> str:
    """
    إجابة ذكية محسنة مع استخدام قاعدة المعرفة والسياق
    """
    # تحليل نية المستخدم
    intent = analyze_user_intent(question)

    # الحصول على السياق من المحادثات السابقة
    context = knowledge_base.get_context(question)

    # توليد الإجابة مع السياق
    if context:
        response = context_aware_generation(context, question, model, tokenizer, max_sequence_len)
    else:
        response = intelligent_response(question, model, tokenizer, max_sequence_len)

    # تحسين الإجابة حسب النية
    if intent == "definition":
        response = f"تعريف: {response}"
    elif intent == "how_to":
        response = f"طريقة: {response}"
    elif intent == "explanation":
        response = f"السبب: {response}"

    return response

#############################
# 9. التنفيذ الرئيسي مع حلقة التعلم الذاتي المتقدم
#############################

if __name__ == "__main__":
    # ----------------------------------------------------
    # إعداد النظام المتقدم
    # ----------------------------------------------------

    # إنشاء قاعدة المعرفة
    knowledge_base = KnowledgeBase()
    knowledge_base.load_from_file(KNOWLEDGE_BASE)

    print("🧠 تم تهيئة نظام الذكاء الاصطناعي المتقدم")
    print(f"📚 قاعدة المعرفة تحتوي على {len(knowledge_base.knowledge)} موضوع")
    print(f"💬 تاريخ المحادثات: {len(knowledge_base.conversation_history)} محادثة")

    # ----------------------------------------------------
    # قائمة المواضيع المحسنة لجمع المعلومات منها
    # ----------------------------------------------------
    topics = [
        # تقنيات الذكاء الاصطناعي
        "الذكاء_الاصطناعي", "تعلم_الآلة", "التعلم_العميق", "الشبكات_العصبية",
        "معالجة_اللغات_الطبيعية", "الرؤية_الحاسوبية", "التعلم_المعزز",

        # علوم الحاسوب
        "علوم_الحاسوب", "البرمجة", "هندسة_البرمجيات", "قواعد_البيانات",
        "أنظمة_التشغيل", "الشبكات_الحاسوبية", "الأمن_السيبراني",

        # التقنيات الحديثة
        "الحوسبة_السحابية", "إنترنت_الأشياء", "البلوكشين", "العملات_الرقمية",
        "الواقع_الافتراضي", "الواقع_المعزز", "الحوسبة_الكمية",

        # العلوم والهندسة
        "الفيزياء", "الرياضيات", "الكيمياء", "الأحياء", "الطب",
        "الهندسة_الكهربائية", "الهندسة_الميكانيكية", "الهندسة_المدنية",
        "الهندسة_البيئية", "هندسة_الطيران",

        # الطاقة والبيئة
        "الطاقة_المتجددة", "الطاقة_الشمسية", "طاقة_الرياح", "علم_البيئة",
        "التغير_المناخي", "الاستدامة",

        # الاقتصاد والأعمال
        "الاقتصاد", "إدارة_الأعمال", "التجارة_الإلكترونية", "التسويق_الرقمي",
        "ريادة_الأعمال", "الابتكار",

        # التعليم والثقافة
        "التعليم", "التعليم_عن_بعد", "التعليم_الإلكتروني", "الفلسفة",
        "علم_النفس", "علم_الاجتماع", "التاريخ", "الجغرافيا",

        # الصحة والطب
        "الطب", "الصحة_العامة", "الطب_النفسي", "طب_الأسنان",
        "الصيدلة", "التمريض", "الطب_البديل"
    ]

    # حلقة لا نهائية للتعلم الذاتي كل 24 ساعة
    while True:
        print(f"\n🕓 [{datetime.datetime.now()}] بدء دورة التعلم الذاتي للموديل '{MODEL_NAME}'")
        
        if is_connected():
            print("✅ تم اكتشاف اتصال بالإنترنت. سيتم تحديث البيانات والتدريب.\n")

            # 1) جمع المقالات من ويكيبيديا وحفظها
            raw_text = collect_articles(topics, delay=0.1)

            # 2) دمج نص محلي إن وجد (ملف extra_text.txt)
            if os.path.exists("extra_text.txt"):
                with open("extra_text.txt", "r", encoding="utf-8") as f:
                    raw_text += "\n" + f.read()
                print("📝 تم دمج محتوى extra_text.txt مع البيانات.")

            # 3) تنظيف النصوص وتجهيز قائمة الجمل بطريقة متقدمة
            sentences = advanced_text_preprocessing(raw_text)
            print(f"📝 تم معالجة {len(sentences)} جملة بعد التنظيف والفلترة")

            # 4) إعداد Tokenizer (تحميله أو إنشاء جديد)
            tokenizer = load_tokenizer(TOKENIZER_FILE)
            if tokenizer is None:
                tokenizer = Tokenizer()
                tokenizer.fit_on_texts(sentences)
                save_tokenizer(tokenizer, TOKENIZER_FILE)

            total_words = len(tokenizer.word_index) + 1
            print(f"🔤 حجم القاموس الحالي: {total_words} كلمة")
            
            # 5) إنشاء بيانات التدريب (X, y)
            X, y = create_sequences(tokenizer, sentences, MAX_SEQUENCE_LEN)
            print(f"📊 عدد عينات التدريب لهذه الدورة: {X.shape[0]}")

            # 6) تحميل الموديل أو بناء جديد (نموذج متقدم)
            model = load_model_file(MODEL_FILE)
            if model is None:
                print("📐 بناء موديل متقدم جديد ...")
                model = build_model(total_words, MAX_SEQUENCE_LEN, model_type="advanced")

                # بناء النموذج أولاً لحساب المعاملات
                dummy_input = np.zeros((1, MAX_SEQUENCE_LEN - 1))
                model(dummy_input)

                print(f"🏗️ تم بناء نموذج متقدم مع {model.count_params():,} معامل")
            else:
                print("♻️ تم تحميل الموديل الموجود سابقًا. سيتم التدريب التراكمي (Fine-Tuning).")

            # 7) إعداد callbacks للتدريب المتقدم
            callbacks = create_callbacks(MODEL_FILE)

            # 8) التدريب المتقدم مع المراقبة
            if X.shape[0] > 100:  # تأكد من وجود بيانات كافية
                history = advanced_training(model, X, y, callbacks)

                # حفظ تاريخ التدريب
                save_training_history(history, TRAINING_HISTORY)

                # تقييم الأداء
                performance = evaluate_model_performance(model, X, y)
            else:
                print("⚠️ البيانات قليلة جداً للتدريب المتقدم. سيتم استخدام التدريب البسيط.")
                model.fit(X, y, epochs=3, batch_size=32, verbose=1)

            # 8) حفظ الموديل وقاعدة المعرفة بعد انتهاء هذه الدورة
            save_model_file(model, MODEL_FILE)
            knowledge_base.save_to_file(KNOWLEDGE_BASE)

            # إضافة المعرفة الجديدة إلى قاعدة البيانات
            for i, sentence in enumerate(sentences[:100]):  # أول 100 جملة
                topic = f"training_data_{datetime.datetime.now().strftime('%Y%m%d')}"
                knowledge_base.add_knowledge(topic, sentence)

            print(f"✅ أنهت دورة التعلم الذاتي المتقدم بنجاح في {datetime.datetime.now()}")
            print(f"🧠 تم تحديث قاعدة المعرفة بـ {len(sentences)} جملة جديدة")

            # 9) تفعيل الوضع التفاعلي الذكي بعد الدورة
            print("\n🤖 موديل أمؤلي-T1 المتقدم جاهز للإجابة الذكية!")
            print("✏️ يمكنك:")
            print("   - طرح أسئلة للحصول على إجابات ذكية")
            print("   - كتابة جملة للحصول على تكملة إبداعية")
            print("   - كتابة 'تخطي' لتخطي التفاعل")
            print("   - كتابة 'خروج' لإنهاء البرنامج")

            while True:
                user_input = input("\n🔤 أدخل سؤالك أو جملتك: ")
                user_input = clean_input(user_input)

                if user_input.lower() in ["تخطي", "skip"]:
                    break
                if user_input.lower() in ["خروج", "exit", "quit"]:
                    print("✨ تم إنهاء البرنامج. إلى اللقاء!")
                    exit()
                if not user_input:
                    print("❌ من فضلك أدخل نصًا عربيًا صالحًا.")
                    continue

                # تحديد نوع الإدخال واستخدام النظام المتقدم
                if any(word in user_input for word in ["ما", "كيف", "متى", "أين", "لماذا", "هل", "؟"]):
                    # إجابة ذكية محسنة مع قاعدة المعرفة
                    response = enhanced_intelligent_response(
                        user_input, model, tokenizer, MAX_SEQUENCE_LEN, knowledge_base
                    )
                    print(f"\n🧠 أمؤلي-T1 المتقدم يجيب بذكاء عميق:\n{response}")
                else:
                    # تكملة إبداعية للجملة مع السياق
                    context = knowledge_base.get_context(user_input)
                    if context:
                        completion = context_aware_generation(
                            context, user_input, model, tokenizer, MAX_SEQUENCE_LEN
                        )
                    else:
                        completion = generate_text_advanced(
                            user_input,
                            next_words=15,
                            model=model,
                            tokenizer=tokenizer,
                            max_sequence_len=MAX_SEQUENCE_LEN,
                            temperature=0.8,
                            top_k=10
                        )
                    print(f"\n🎨 أمؤلي-T1 المتقدم يكمل بإبداع:\n{completion}")
                    response = completion

                # حفظ المحادثة في قاعدة المعرفة
                knowledge_base.add_conversation(user_input, response)

                # إضافة تقييم للإجابة مع التعلم
                print("\n📊 كيف تقيم هذه الإجابة؟ (1-5) أو اضغط Enter للتخطي:")
                try:
                    rating_input = input("التقييم: ").strip()
                    if rating_input and rating_input.isdigit() and 1 <= int(rating_input) <= 5:
                        rating = int(rating_input)
                        print(f"شكراً لك! تقييمك: {rating}/5 ⭐")

                        # تحديث التقييم في قاعدة المعرفة
                        if knowledge_base.conversation_history:
                            knowledge_base.conversation_history[-1]['rating'] = rating

                        # تعلم من التقييم
                        if rating >= 4:
                            print("😊 ممتاز! سأتذكر هذا النوع من الإجابات")
                        elif rating <= 2:
                            print("😔 سأحاول تحسين إجاباتي في المرات القادمة")
                except:
                    pass

        else:
            print("❌ لا يوجد اتصال بالإنترنت حاليًا. سيتم إعادة المحاولة في الدورة القادمة.")

        # 10) انتظر 24 ساعة (86400 ثانية) قبل بدء الدورة التالية
        print("\n⏳ الانتظار لمدة 24 ساعة قبل بدء الدورة التالية...\n")
        time.sleep(60 * 60 * 24)