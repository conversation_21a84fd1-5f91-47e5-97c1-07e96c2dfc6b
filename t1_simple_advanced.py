# -*- coding: utf-8 -*-
"""
نموذج "أمؤلي-T1 المبسط المتقدم" - نسخة محسنة ومتوافقة
"""

import os
import time
import requests
import re
import pickle
import numpy as np
from bs4 import BeautifulSoup
import datetime

# مكتبات TensorFlow/Keras
import tensorflow as tf

# إصلاح مشكلة eager execution
tf.config.run_functions_eagerly(True)

from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

# إعدادات النموذج المحسن
MODEL_NAME = "ml-T1-Simple-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
ARTICLES_DUMP = "wikipedia_dump.txt"

# معاملات محسنة للحصول على نتائج أفضل
MAX_SEQUENCE_LEN = 15   # طول أطول للسياق الأفضل
EMBEDDING_DIM = 256     # أبعاد تضمين أكبر
LSTM_UNITS = 256        # وحدات LSTM أكثر
DROPOUT_RATE = 0.3      # معدل إسقاط متوسط
EPOCHS = 15             # عصور أكثر للتعلم الأفضل
BATCH_SIZE = 64         # دفعات أكبر للكفاءة
LEARNING_RATE = 0.001   # معدل تعلم محسن

def is_connected() -> bool:
    """التحقق من اتصال الإنترنت"""
    try:
        requests.get("https://www.google.com", timeout=5)
        return True
    except:
        return False

def fetch_wikipedia_article(title: str) -> str:
    """تحميل مقالة من ويكيبيديا"""
    url = f"https://ar.wikipedia.org/wiki/{title}"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            return ""
        soup = BeautifulSoup(response.text, 'html.parser')
        paragraphs = soup.find_all('p')
        text = ""
        for p in paragraphs:
            text += p.get_text()
        return text
    except Exception as e:
        print(f"⚠️ خطأ في تحميل {title}: {e}")
        return ""

def collect_articles(topics: list, delay: float = 0.1) -> str:
    """جمع المقالات"""
    all_text = []
    for idx, topic in enumerate(topics):
        print(f"تحميل ({idx+1}/{len(topics)}) موضوع: {topic} ...")
        art = fetch_wikipedia_article(topic)
        if art:
            all_text.append(art)
        time.sleep(delay)
    combined = "\n".join(all_text)
    with open(ARTICLES_DUMP, "w", encoding="utf-8") as f:
        f.write(combined)
    print(f"📝 تم حفظ المقالات في: {ARTICLES_DUMP}")
    return combined

def advanced_text_preprocessing(text: str) -> list:
    """معالجة متقدمة للنصوص مع تحسينات للجودة والذكاء"""
    # تنظيف النص مع الحفاظ على الكلمات المهمة
    text = re.sub(r'[^\u0600-\u06FF\s\.\،\؟\!\:\؛]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()

    # تقسيم إلى جمل
    sentences = re.split(r'[\.؟!\n]', text)

    # فلترة الجمل مع معايير محسنة للذكاء
    filtered_sentences = []
    seen_sentences = set()

    # كلمات مهمة للحفاظ عليها
    important_words = {
        'الذكاء', 'الاصطناعي', 'تعلم', 'الآلة', 'البرمجة', 'الحاسوب',
        'العلم', 'التقنية', 'المعرفة', 'التعليم', 'الطب', 'الهندسة',
        'الرياضيات', 'الفيزياء', 'الكيمياء', 'الأحياء', 'التاريخ',
        'الجغرافيا', 'الاقتصاد', 'السياسة', 'الثقافة', 'اللغة'
    }

    for sentence in sentences:
        sentence = sentence.strip()
        words = sentence.split()

        # تحسين معايير الفلترة
        if 3 <= len(words) <= 25:  # نطاق أوسع للجمل
            # فحص جودة المحتوى
            has_important_content = any(word in important_words for word in words)

            # تجاهل الجمل التي تحتوي على كلمات مكررة كثيراً
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1

            # تجاهل الجمل التي تحتوي على تكرار مفرط
            max_repetition = max(word_counts.values()) if word_counts else 0

            # معايير محسنة للقبول
            if max_repetition <= 2 or has_important_content:  # أكثر مرونة للمحتوى المهم
                # تجاهل الجمل المكررة
                if sentence not in seen_sentences:
                    # تجاهل الجمل التي تحتوي على كلمات قصيرة جداً فقط
                    meaningful_words = [w for w in words if len(w) >= 2]
                    if len(meaningful_words) >= 2:  # أكثر مرونة
                        # تجنب الجمل التي تحتوي على رموز غريبة
                        if all(re.match(r'^[\u0600-\u06FF]+$', w) or w in ['في', 'من', 'إلى', 'على', 'عن'] for w in meaningful_words):
                            filtered_sentences.append(sentence)
                            seen_sentences.add(sentence)

    return filtered_sentences

def create_sequences(tokenizer: Tokenizer, corpus: list, max_sequence_len: int):
    """إنشاء تسلسلات التدريب محسنة للذكاء الاصطناعي"""
    input_sequences = []

    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]

        # إنشاء تسلسلات متداخلة للتعلم الأفضل
        for i in range(2, len(token_list) + 1):  # بدء من 2 للحصول على سياق أفضل
            if i <= max_sequence_len:
                n_gram_sequence = token_list[:i]
                # إضافة padding إذا كان التسلسل قصير
                while len(n_gram_sequence) < max_sequence_len:
                    n_gram_sequence = [0] + n_gram_sequence
                input_sequences.append(n_gram_sequence)
            else:
                # للتسلسلات الطويلة، استخدم نافذة متحركة
                for j in range(len(token_list) - max_sequence_len + 1):
                    n_gram_sequence = token_list[j:j + max_sequence_len]
                    input_sequences.append(n_gram_sequence)

    if not input_sequences:
        return np.array([]), np.array([])

    input_sequences = np.array(input_sequences)

    # تقسيم التسلسلات إلى X و y
    X = input_sequences[:, :-1]
    y = input_sequences[:, -1]

    # تحويل y إلى categorical مع معالجة الأخطاء
    vocab_size = len(tokenizer.word_index) + 1
    y = to_categorical(y, num_classes=vocab_size)

    print(f"📈 تم إنشاء {len(input_sequences)} تسلسل تدريب")
    print(f"📏 شكل X: {X.shape}, شكل y: {y.shape}")

    return X, y

def build_advanced_model(vocab_size: int, max_sequence_len: int):
    """بناء نموذج متقدم مبسط محسن للذكاء الاصطناعي العربي"""
    model = Sequential()

    # طبقة التضمين المحسنة
    model.add(Embedding(
        input_dim=vocab_size,
        output_dim=EMBEDDING_DIM,
        input_length=max_sequence_len - 1,
        mask_zero=True  # لتجاهل القيم المحشوة
    ))

    # طبقة Dropout للتنظيم
    model.add(Dropout(DROPOUT_RATE))

    # طبقات LSTM متعددة للفهم العميق
    model.add(Bidirectional(LSTM(
        LSTM_UNITS,
        return_sequences=True,  # إرجاع التسلسلات للطبقة التالية
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))

    # طبقة LSTM ثانية للتعلم العميق
    model.add(Bidirectional(LSTM(
        LSTM_UNITS // 2,
        return_sequences=False,
        dropout=DROPOUT_RATE,
        recurrent_dropout=DROPOUT_RATE
    )))

    # طبقات مكتملة الاتصال متعددة للذكاء
    model.add(Dense(512, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))

    model.add(Dense(256, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))

    model.add(Dense(128, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))

    # طبقة الإخراج
    model.add(Dense(vocab_size, activation='softmax'))

    # تجميع النموذج مع محسن متقدم
    optimizer = Adam(learning_rate=LEARNING_RATE, beta_1=0.9, beta_2=0.999)

    model.compile(
        loss='categorical_crossentropy',
        optimizer=optimizer,
        metrics=['accuracy', 'top_k_categorical_accuracy']
    )

    return model

def generate_text_advanced(seed_text: str, next_words: int, model, tokenizer, max_sequence_len: int):
    """توليد نص متقدم مع ذكاء اصطناعي محسن"""
    output_text = seed_text
    used_words = []  # قائمة للكلمات المستخدمة مؤخراً

    # كلمات الوقف العربية
    stop_words = {'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي'}

    for i in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]

        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]

        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')

        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]

            # تطبيق temperature sampling للتنوع
            temperature = 0.8
            predicted_probs = np.log(predicted_probs + 1e-8) / temperature
            predicted_probs = np.exp(predicted_probs)
            predicted_probs = predicted_probs / np.sum(predicted_probs)

            # تحسين Top-K sampling مع nucleus sampling
            top_k = min(20, len(predicted_probs))
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]

            # ترتيب حسب الاحتمالية
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]

            # تطبيق nucleus sampling (top-p)
            cumulative_probs = np.cumsum(predicted_probs[sorted_indices])
            nucleus_cutoff = np.searchsorted(cumulative_probs, 0.9)  # top-p = 0.9
            nucleus_indices = sorted_indices[:nucleus_cutoff + 1]

            # اختيار كلمة ذكية مع تجنب التكرار
            selected_word = None
            for idx in nucleus_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) >= 2:
                    # تجنب التكرار المفرط للكلمات الأخيرة
                    recent_words = used_words[-5:] if len(used_words) >= 5 else used_words

                    # السماح بكلمات الوقف ولكن تجنب تكرار الكلمات المهمة
                    if candidate_word in stop_words or candidate_word not in recent_words:
                        # تجنب الكلمات التي تحتوي على أرقام أو رموز غريبة
                        if re.match(r'^[\u0600-\u06FF]+$', candidate_word):
                            selected_word = candidate_word
                            break

            # إذا لم نجد كلمة مناسبة، استخدم أفضل كلمة متاحة
            if not selected_word:
                for idx in nucleus_indices:
                    candidate_word = tokenizer.index_word.get(idx, "")
                    if candidate_word and len(candidate_word) >= 2:
                        if re.match(r'^[\u0600-\u06FF]+$', candidate_word):
                            selected_word = candidate_word
                            break

            if not selected_word:
                break

            output_text += " " + selected_word
            used_words.append(selected_word)

            # الحفاظ على آخر 10 كلمات فقط في الذاكرة
            if len(used_words) > 10:
                used_words = used_words[-10:]

            # توقف إذا وصلنا لنهاية جملة منطقية
            if selected_word in ['النهاية', 'انتهى', 'تم', 'انتهت'] or len(output_text.split()) > 50:
                break

        except Exception as e:
            print(f"خطأ في التوليد: {e}")
            break

    return output_text.strip()

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

def save_tokenizer(tokenizer: Tokenizer, filename: str):
    with open(filename, 'wb') as f:
        pickle.dump(tokenizer, f)
    print(f"✅ تم حفظ الـ Tokenizer في: {filename}")

def load_tokenizer(filename: str):
    if os.path.exists(filename):
        with open(filename, 'rb') as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل الـ Tokenizer من: {filename}")
        return tokenizer
    else:
        print("⚠️ ملف الـ Tokenizer غير موجود. سيتم إنشاء Tokenizer جديد.")
        return None

def save_model_file(model, filename: str):
    model.save(filename)
    print(f"✅ تم حفظ الموديل في: {filename}")

def load_model_file(filename: str):
    if os.path.exists(filename):
        model = load_model(filename)
        print(f"✅ تم تحميل الموديل من: {filename}")
        return model
    else:
        print("⚠️ لم يتم العثور على ملف الموديل. سيتم تدريب موديل جديد.")
        return None

if __name__ == "__main__":
    # قائمة المواضيع المحسنة للحصول على محتوى أفضل
    topics = [
        # تقنيات حديثة
        "الذكاء_الاصطناعي", "تعلم_الآلة", "البرمجة", "الحاسوب",
        "الإنترنت", "التقنية", "الشبكات_الحاسوبية", "أمن_المعلومات",

        # علوم أساسية
        "العلوم", "الرياضيات", "الفيزياء", "الكيمياء", "الأحياء",

        # طب وصحة
        "الطب", "الصحة", "التمريض", "الصيدلة",

        # هندسة وتقنية
        "الهندسة", "الهندسة_المدنية", "الهندسة_الكهربائية",
        "الهندسة_الميكانيكية", "هندسة_البرمجيات",

        # بيئة وطاقة
        "البيئة", "الطاقة", "الطاقة_المتجددة", "التغير_المناخي",

        # فضاء وطيران
        "الفضاء", "الطيران", "الفلك",

        # اقتصاد وإدارة
        "الاقتصاد", "إدارة_الأعمال", "التسويق",

        # تعليم وثقافة
        "التعليم", "اللغة_العربية", "الأدب", "التاريخ"
    ]
    
    print(f"\n🕓 [{datetime.datetime.now()}] بدء تدريب النموذج المتقدم المبسط")
    
    if is_connected():
        print("✅ تم اكتشاف اتصال بالإنترنت. سيتم تحديث البيانات والتدريب.\n")
        
        # جمع المقالات
        raw_text = collect_articles(topics, delay=0.1)
        
        # إضافة النصوص الإضافية والعبارات الشائعة
        additional_texts = []

        # تحميل النصوص الإضافية
        if os.path.exists("extra_text.txt"):
            with open("extra_text.txt", "r", encoding="utf-8") as f:
                additional_texts.extend(f.read().split('\n'))
                print("📚 تم تحميل النصوص الإضافية")

        # تحميل العبارات الشائعة
        if os.path.exists("common_phrases.txt"):
            with open("common_phrases.txt", "r", encoding="utf-8") as f:
                phrases = f.read().split('\n')
                additional_texts.extend([p for p in phrases if p and not p.startswith('#')])
                print("💬 تم تحميل العبارات الشائعة")

        # تحميل أسئلة وإجابات
        if os.path.exists("qa_dataset.txt"):
            with open("qa_dataset.txt", "r", encoding="utf-8") as f:
                qa_lines = f.read().split('\n')
                for line in qa_lines:
                    if line and not line.startswith('#') and '.' in line:
                        # تحويل السؤال والجواب إلى جملة واحدة
                        parts = line.split('.')
                        if len(parts) >= 2:
                            question = parts[0].strip()
                            answer = parts[1].strip()
                            combined = f"{question} {answer}"
                            additional_texts.append(combined)
                print("❓ تم تحميل الأسئلة والإجابات")

        # دمج النصوص الإضافية مع النصوص المحملة
        if additional_texts:
            raw_text += "\n" + "\n".join(additional_texts)
            print(f"📖 تم إضافة {len(additional_texts)} نص إضافي")

        # معالجة النصوص
        sentences = advanced_text_preprocessing(raw_text)
        print(f"📝 تم معالجة {len(sentences)} جملة إجمالية")
        
        if len(sentences) < 10:
            print("❌ البيانات قليلة جداً للتدريب")
            exit()
        
        # إعداد Tokenizer
        tokenizer = load_tokenizer(TOKENIZER_FILE)
        if tokenizer is None:
            tokenizer = Tokenizer()
            tokenizer.fit_on_texts(sentences)
            save_tokenizer(tokenizer, TOKENIZER_FILE)
        
        total_words = len(tokenizer.word_index) + 1
        print(f"🔤 حجم القاموس: {total_words} كلمة")
        
        # إنشاء بيانات التدريب
        X, y = create_sequences(tokenizer, sentences, MAX_SEQUENCE_LEN)
        
        if X.shape[0] == 0:
            print("❌ لا توجد بيانات تدريب صالحة")
            exit()
            
        print(f"📊 عدد عينات التدريب: {X.shape[0]}")
        
        # بناء نموذج جديد دائماً للتدريب الطازج
        print("📐 بناء نموذج متقدم جديد للتدريب...")
        model = build_advanced_model(total_words, MAX_SEQUENCE_LEN)

        # بناء النموذج
        dummy_input = np.zeros((1, MAX_SEQUENCE_LEN - 1))
        model(dummy_input)
        print(f"🏗️ تم بناء نموذج مع {model.count_params():,} معامل")

        # إعداد callbacks للتدريب المحسن
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=3,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=2,
                min_lr=0.0001,
                verbose=1
            )
        ]

        # التدريب المحسن
        print("🚀 بدء التدريب المتقدم...")
        history = model.fit(
            X, y,
            epochs=EPOCHS,
            batch_size=BATCH_SIZE,
            verbose=1,
            validation_split=0.15,
            callbacks=callbacks,
            shuffle=True
        )
        
        # حفظ النموذج المدرب
        save_model_file(model, MODEL_FILE)
        print(f"✅ انتهى التدريب بنجاح في {datetime.datetime.now()}")

        # عرض إحصائيات التدريب
        if 'history' in locals():
            final_loss = history.history['loss'][-1]
            final_accuracy = history.history['accuracy'][-1]
            print(f"📊 الخسارة النهائية: {final_loss:.4f}")
            print(f"📊 الدقة النهائية: {final_accuracy:.4f}")

        # التفاعل الذكي
        print("\n🤖 النموذج الذكي جاهز للتفاعل!")
        print("💡 النموذج الآن مدرب ويمكنه توليد إجابات ذكية من المعرفة المكتسبة")
        print("اكتب سؤال أو جملة (أو 'خروج' للإنهاء):")
        print("مثال: 'ما هو الذكاء الاصطناعي؟' أو 'عاصمة السعودية'")

        while True:
            user_input = input("\n🔍 سؤالك: ")
            if user_input.lower() in ["خروج", "exit", "انتهاء"]:
                print("👋 شكراً لاستخدام النموذج الذكي!")
                break

            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي صحيح")
                continue

            print("🤔 النموذج يفكر...")

            # توليد إجابة ذكية أطول
            response = generate_text_advanced(
                clean_user_input, 15, model, tokenizer, MAX_SEQUENCE_LEN
            )

            # تنظيف الإجابة
            response = response.replace(clean_user_input, "").strip()
            if response:
                print(f"🤖 الإجابة: {response}")
            else:
                print("🤖 عذراً، لم أتمكن من فهم السؤال. حاول إعادة صياغته.")

    else:
        print("❌ لا يوجد اتصال بالإنترنت")
        print("💡 يمكنك تشغيل النموذج بدون إنترنت إذا كان مدرباً مسبقاً")
