# -*- coding: utf-8 -*-
"""
نموذج "أمؤلي-T1 المبسط المتقدم" - نسخة محسنة ومتوافقة
"""

import os
import time
import requests
import re
import pickle
import numpy as np
from bs4 import BeautifulSoup
import datetime

# مكتبات TensorFlow/Keras
import tensorflow as tf

# إصلاح مشكلة eager execution
tf.config.run_functions_eagerly(True)
try:
    tf.compat.v1.enable_eager_execution()
except:
    pass

from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional
from tensorflow.keras.utils import to_categorical

# إعدادات النموذج المحسن
MODEL_NAME = "ml-T1-Simple-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
ARTICLES_DUMP = "wikipedia_dump.txt"

# معاملات محسنة للحصول على نتائج أفضل
MAX_SEQUENCE_LEN = 12   # طول أقصر للتركيز
EMBEDDING_DIM = 200     # أبعاد تضمين أكبر
LSTM_UNITS = 128        # وحدات LSTM محسنة
DROPOUT_RATE = 0.2      # معدل إسقاط أقل
EPOCHS = 8              # عصور أكثر للتعلم الأفضل
BATCH_SIZE = 32         # دفعات أصغر للدقة

def is_connected() -> bool:
    """التحقق من اتصال الإنترنت"""
    try:
        requests.get("https://www.google.com", timeout=5)
        return True
    except:
        return False

def fetch_wikipedia_article(title: str) -> str:
    """تحميل مقالة من ويكيبيديا"""
    url = f"https://ar.wikipedia.org/wiki/{title}"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            return ""
        soup = BeautifulSoup(response.text, 'html.parser')
        paragraphs = soup.find_all('p')
        text = ""
        for p in paragraphs:
            text += p.get_text()
        return text
    except Exception as e:
        print(f"⚠️ خطأ في تحميل {title}: {e}")
        return ""

def collect_articles(topics: list, delay: float = 0.1) -> str:
    """جمع المقالات"""
    all_text = []
    for idx, topic in enumerate(topics):
        print(f"تحميل ({idx+1}/{len(topics)}) موضوع: {topic} ...")
        art = fetch_wikipedia_article(topic)
        if art:
            all_text.append(art)
        time.sleep(delay)
    combined = "\n".join(all_text)
    with open(ARTICLES_DUMP, "w", encoding="utf-8") as f:
        f.write(combined)
    print(f"📝 تم حفظ المقالات في: {ARTICLES_DUMP}")
    return combined

def advanced_text_preprocessing(text: str) -> list:
    """معالجة متقدمة للنصوص مع تحسينات للجودة"""
    # تنظيف النص مع الحفاظ على الكلمات المهمة
    text = re.sub(r'[^\u0600-\u06FF\s\.\،\؟\!\:\؛]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()

    # تقسيم إلى جمل
    sentences = re.split(r'[\.؟!\n]', text)

    # فلترة الجمل مع معايير أكثر صرامة
    filtered_sentences = []
    seen_sentences = set()

    for sentence in sentences:
        sentence = sentence.strip()
        words = sentence.split()

        # تجاهل الجمل القصيرة جداً أو الطويلة جداً
        if 4 <= len(words) <= 20:
            # تجاهل الجمل التي تحتوي على كلمات مكررة كثيراً
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1

            # تجاهل الجمل التي تحتوي على تكرار مفرط
            max_repetition = max(word_counts.values()) if word_counts else 0
            if max_repetition <= 3:  # لا تسمح بتكرار كلمة أكثر من 3 مرات
                # تجاهل الجمل المكررة
                if sentence not in seen_sentences:
                    # تجاهل الجمل التي تحتوي على كلمات قصيرة جداً فقط
                    long_words = [w for w in words if len(w) >= 2]
                    if len(long_words) >= 3:
                        filtered_sentences.append(sentence)
                        seen_sentences.add(sentence)

    return filtered_sentences

def create_sequences(tokenizer: Tokenizer, corpus: list, max_sequence_len: int):
    """إنشاء تسلسلات التدريب"""
    input_sequences = []
    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]
        for i in range(max_sequence_len, len(token_list) + 1):
            n_gram_sequence = token_list[i - max_sequence_len: i]
            input_sequences.append(n_gram_sequence)
    
    if not input_sequences:
        return np.array([]), np.array([])
        
    input_sequences = np.array(input_sequences)
    
    # تقسيم التسلسلات إلى X و y
    X = input_sequences[:, :-1]
    y = input_sequences[:, -1]
    
    y = to_categorical(y, num_classes=len(tokenizer.word_index) + 1)
    return X, y

def build_advanced_model(vocab_size: int, max_sequence_len: int):
    """بناء نموذج متقدم مبسط"""
    model = Sequential()
    
    # طبقة التضمين
    model.add(Embedding(
        input_dim=vocab_size, 
        output_dim=EMBEDDING_DIM, 
        input_length=max_sequence_len - 1
    ))
    
    # طبقة Dropout
    model.add(Dropout(DROPOUT_RATE))
    
    # طبقة LSTM ثنائية الاتجاه
    model.add(Bidirectional(LSTM(
        LSTM_UNITS, 
        return_sequences=False,
        dropout=DROPOUT_RATE
    )))
    
    # طبقة مكتملة الاتصال
    model.add(Dense(128, activation='relu'))
    model.add(Dropout(DROPOUT_RATE))
    
    # طبقة الإخراج
    model.add(Dense(vocab_size, activation='softmax'))
    
    # تجميع النموذج
    model.compile(
        loss='categorical_crossentropy',
        optimizer='adam',
        metrics=['accuracy']
    )
    
    return model

def generate_text_advanced(seed_text: str, next_words: int, model, tokenizer, max_sequence_len: int):
    """توليد نص متقدم مع تحسينات للجودة"""
    output_text = seed_text
    used_words = set(seed_text.split())  # لتجنب التكرار المفرط

    for i in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]

        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]

        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')

        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]

            # تحسين Top-K sampling
            top_k = min(10, len(predicted_probs))  # استخدام top-10 للتنوع
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]

            # ترتيب حسب الاحتمالية
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]

            # اختيار كلمة مع تجنب التكرار المفرط
            selected_word = None
            for idx in sorted_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) > 1:  # تجنب الكلمات القصيرة جداً
                    # تقليل احتمالية الكلمات المستخدمة مؤخراً
                    if candidate_word not in used_words or i > 3:
                        selected_word = candidate_word
                        break

            if not selected_word:
                # إذا لم نجد كلمة مناسبة، استخدم الأفضل
                predicted_index = sorted_indices[0]
                selected_word = tokenizer.index_word.get(predicted_index, "")

            if not selected_word or len(selected_word) < 2:
                break

            output_text += " " + selected_word
            used_words.add(selected_word)

            # تنظيف قائمة الكلمات المستخدمة كل فترة
            if len(used_words) > 15:
                used_words = set(list(used_words)[-10:])

        except Exception as e:
            print(f"خطأ في التوليد: {e}")
            break

    return output_text

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

def save_tokenizer(tokenizer: Tokenizer, filename: str):
    with open(filename, 'wb') as f:
        pickle.dump(tokenizer, f)
    print(f"✅ تم حفظ الـ Tokenizer في: {filename}")

def load_tokenizer(filename: str):
    if os.path.exists(filename):
        with open(filename, 'rb') as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل الـ Tokenizer من: {filename}")
        return tokenizer
    else:
        print("⚠️ ملف الـ Tokenizer غير موجود. سيتم إنشاء Tokenizer جديد.")
        return None

def save_model_file(model, filename: str):
    model.save(filename)
    print(f"✅ تم حفظ الموديل في: {filename}")

def load_model_file(filename: str):
    if os.path.exists(filename):
        model = load_model(filename)
        print(f"✅ تم تحميل الموديل من: {filename}")
        return model
    else:
        print("⚠️ لم يتم العثور على ملف الموديل. سيتم تدريب موديل جديد.")
        return None

if __name__ == "__main__":
    # قائمة المواضيع المحسنة للحصول على محتوى أفضل
    topics = [
        # تقنيات حديثة
        "الذكاء_الاصطناعي", "تعلم_الآلة", "البرمجة", "الحاسوب",
        "الإنترنت", "التقنية", "الشبكات_الحاسوبية", "أمن_المعلومات",

        # علوم أساسية
        "العلوم", "الرياضيات", "الفيزياء", "الكيمياء", "الأحياء",

        # طب وصحة
        "الطب", "الصحة", "التمريض", "الصيدلة",

        # هندسة وتقنية
        "الهندسة", "الهندسة_المدنية", "الهندسة_الكهربائية",
        "الهندسة_الميكانيكية", "هندسة_البرمجيات",

        # بيئة وطاقة
        "البيئة", "الطاقة", "الطاقة_المتجددة", "التغير_المناخي",

        # فضاء وطيران
        "الفضاء", "الطيران", "الفلك",

        # اقتصاد وإدارة
        "الاقتصاد", "إدارة_الأعمال", "التسويق",

        # تعليم وثقافة
        "التعليم", "اللغة_العربية", "الأدب", "التاريخ"
    ]
    
    print(f"\n🕓 [{datetime.datetime.now()}] بدء تدريب النموذج المتقدم المبسط")
    
    if is_connected():
        print("✅ تم اكتشاف اتصال بالإنترنت. سيتم تحديث البيانات والتدريب.\n")
        
        # جمع المقالات
        raw_text = collect_articles(topics, delay=0.1)
        
        # إضافة النصوص الإضافية والعبارات الشائعة
        additional_texts = []

        # تحميل النصوص الإضافية
        if os.path.exists("extra_text.txt"):
            with open("extra_text.txt", "r", encoding="utf-8") as f:
                additional_texts.extend(f.read().split('\n'))
                print("📚 تم تحميل النصوص الإضافية")

        # تحميل العبارات الشائعة
        if os.path.exists("common_phrases.txt"):
            with open("common_phrases.txt", "r", encoding="utf-8") as f:
                phrases = f.read().split('\n')
                additional_texts.extend([p for p in phrases if p and not p.startswith('#')])
                print("💬 تم تحميل العبارات الشائعة")

        # تحميل أسئلة وإجابات
        if os.path.exists("qa_dataset.txt"):
            with open("qa_dataset.txt", "r", encoding="utf-8") as f:
                qa_lines = f.read().split('\n')
                for line in qa_lines:
                    if line and not line.startswith('#') and '.' in line:
                        # تحويل السؤال والجواب إلى جملة واحدة
                        parts = line.split('.')
                        if len(parts) >= 2:
                            question = parts[0].strip()
                            answer = parts[1].strip()
                            combined = f"{question} {answer}"
                            additional_texts.append(combined)
                print("❓ تم تحميل الأسئلة والإجابات")

        # دمج النصوص الإضافية مع النصوص المحملة
        if additional_texts:
            raw_text += "\n" + "\n".join(additional_texts)
            print(f"📖 تم إضافة {len(additional_texts)} نص إضافي")

        # معالجة النصوص
        sentences = advanced_text_preprocessing(raw_text)
        print(f"📝 تم معالجة {len(sentences)} جملة إجمالية")
        
        if len(sentences) < 10:
            print("❌ البيانات قليلة جداً للتدريب")
            exit()
        
        # إعداد Tokenizer
        tokenizer = load_tokenizer(TOKENIZER_FILE)
        if tokenizer is None:
            tokenizer = Tokenizer()
            tokenizer.fit_on_texts(sentences)
            save_tokenizer(tokenizer, TOKENIZER_FILE)
        
        total_words = len(tokenizer.word_index) + 1
        print(f"🔤 حجم القاموس: {total_words} كلمة")
        
        # إنشاء بيانات التدريب
        X, y = create_sequences(tokenizer, sentences, MAX_SEQUENCE_LEN)
        
        if X.shape[0] == 0:
            print("❌ لا توجد بيانات تدريب صالحة")
            exit()
            
        print(f"📊 عدد عينات التدريب: {X.shape[0]}")
        
        # بناء أو تحميل النموذج
        model = load_model_file(MODEL_FILE)
        if model is None:
            print("📐 بناء نموذج متقدم جديد...")
            model = build_advanced_model(total_words, MAX_SEQUENCE_LEN)
            
            # بناء النموذج
            dummy_input = np.zeros((1, MAX_SEQUENCE_LEN - 1))
            model(dummy_input)
            print(f"🏗️ تم بناء نموذج مع {model.count_params():,} معامل")
        
        # التدريب
        print("🚀 بدء التدريب...")
        model.fit(X, y, epochs=EPOCHS, batch_size=BATCH_SIZE, verbose=1, validation_split=0.1)
        
        # حفظ النموذج
        save_model_file(model, MODEL_FILE)
        print(f"✅ انتهى التدريب بنجاح في {datetime.datetime.now()}")
        
        # التفاعل
        print("\n🤖 النموذج المتقدم جاهز للتفاعل!")
        print("اكتب جملة أو سؤال (أو 'خروج' للإنهاء):")
        
        while True:
            user_input = input("\n> ")
            if user_input.lower() in ["خروج", "exit"]:
                break
            
            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي")
                continue
            
            response = generate_text_advanced(
                clean_user_input, 10, model, tokenizer, MAX_SEQUENCE_LEN
            )
            print(f"🤖 {response}")
    
    else:
        print("❌ لا يوجد اتصال بالإنترنت")
