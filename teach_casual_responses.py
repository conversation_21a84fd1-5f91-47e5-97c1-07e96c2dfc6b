# -*- coding: utf-8 -*-
"""
تعليم النموذج الردود العامية والتحيات
"""

import json
import os
from datetime import datetime

def teach_casual_responses():
    """تعليم الردود العامية والتحيات"""
    
    print("🗣️ بدء تعليم الردود العامية والتحيات...")
    print("=" * 60)
    
    # تحميل الذاكرة الحالية
    try:
        with open('ml_t1_memory.json', 'r', encoding='utf-8') as f:
            knowledge = json.load(f)
    except:
        knowledge = {'concepts': {}, 'facts': {}, 'insights': [], 'casual_responses': {}}

    # إضافة الأقسام المطلوبة إذا لم تكن موجودة
    if 'casual_responses' not in knowledge:
        knowledge['casual_responses'] = {}
    if 'facts' not in knowledge:
        knowledge['facts'] = {}
    if 'concepts' not in knowledge:
        knowledge['concepts'] = {}
    if 'insights' not in knowledge:
        knowledge['insights'] = []
    
    # الردود العامية والتحيات
    casual_responses = {
         # التحيات الإسلامية
        'السلام عليكم': [
            'وعليكم السلام ورحمة الله وبركاته',
            'وعليكم السلام، أهلاً وسهلاً',
            'وعليكم السلام، مرحباً بك'
        ],
        'سلام عليكم': [
            'وعليكم السلام ورحمة الله',
            'وعليكم السلام، أهلاً بك'
        ],
        
        # التحيات العامة
        'مرحبا': [
            'أهلاً وسهلاً بك! 😊',
            'مرحباً! كيف حالك؟',
            'أهلاً بك، نورت!'
        ],
        'مرحباً': [
            'أهلاً وسهلاً! 🌟',
            'مرحباً بك، كيف يمكنني مساعدتك؟'
        ],
        'هلا': [
            'هلا والله! أهلاً بك',
            'هلا فيك! كيف الأحوال؟'
        ],
        'اهلا': [
            'أهلاً وسهلاً! مرحباً بك',
            'اهلين فيك!'
        ],
        'اهلين': [
            'اهلين فيك! نورت',
            'اهلا وسهلا بك'
        ],
        
        # السؤال عن الحال
        'كيف حالك': [
            'الحمد لله، تمام! وأنت كيف حالك؟',
            'بخير والحمد لله، شكراً لسؤالك',
            'ممتاز! أتعلم وأتطور كل يوم'
        ],
        'شلونك': [
            'زين الحمد لله! وأنت شلونك؟',
            'تمام، كله زين! شكراً'
        ],
        'كيفك': [
            'منيح الحمد لله! وأنت كيفك؟',
            'تمام، شكراً لسؤالك'
        ],
        'وش أخبارك': [
            'أخبار زينة الحمد لله! وأنت وش أخبارك؟',
            'كله تمام، أتعلم أشياء جديدة'
        ],
        'ايش اخبارك': [
            'أخبار حلوة! أتعلم كل يوم شيء جديد',
            'كله منيح، شكراً لسؤالك'
        ],
        
        # عبارات الشكر
        'شكرا': [
            'العفو! لا شكر على واجب',
            'أهلاً وسهلاً، دايماً في الخدمة',
            'تسلم، هذا واجبي'
        ],
        'شكراً': [
            'العفو! 😊',
            'لا شكر على واجب',
            'أهلاً وسهلاً بك'
        ],
        'مشكور': [
            'الله يعافيك! تسلم',
            'أهلاً وسهلاً، هذا واجبي'
        ],
        'يعطيك العافية': [
            'الله يعافيك! تسلم',
            'ويعطيك العافية، شكراً لك'
        ],
        'تسلم': [
            'الله يسلمك! شكراً',
            'تسلم، وأنت كمان'
        ],
        
        # تحيات الوقت
        'صباح الخير': [
            'صباح النور! كيف حالك اليوم؟',
            'صباح الخير والسرور! 🌅',
            'صباح النور، يوم سعيد إن شاء الله'
        ],
        'مساء الخير': [
            'مساء النور! كيف كان يومك؟',
            'مساء الخير والسرور! 🌙',
            'مساء النور، أتمنى يومك كان حلو'
        ],
        'تصبح على خير': [
            'وأنت بخير! أحلام سعيدة',
            'تصبح على خير، نوم هنيء',
            'وأنت بألف خير! 🌙'
        ],
        
        # عبارات الوداع
        'مع السلامة': [
            'مع السلامة! في أمان الله',
            'الله معك، مع السلامة',
            'في أمان الله! 👋'
        ],
        'باي': [
            'باي باي! أراك قريباً',
            'مع السلامة! 👋'
        ],
        'وداعا': [
            'وداعاً! أتطلع لرؤيتك مرة أخرى',
            'في أمان الله!'
        ],
        
        # عبارات الاعتذار
        'آسف': [
            'لا بأس، ما صار شي',
            'عادي، مافي مشكلة',
            'الله يعافيك، عادي'
        ],
        'معذرة': [
            'لا بأس عليك، عادي',
            'ما عليك زود، مافي مشكلة'
        ],
        'اعتذر': [
            'لا داعي للاعتذار، كله تمام',
            'عادي، مافي شي'
        ],
        
        # عبارات الموافقة
        'نعم': [
            'تمام! 👍',
            'زين، فهمت',
            'أوكي!'
        ],
        'أيوة': [
            'تمام! فهمت',
            'زين، واضح'
        ],
        'اوكي': [
            'أوكي! 👌',
            'تمام، فهمت'
        ],
        'تمام': [
            'تمام تمام! 😊',
            'زين، كله واضح'
        ],
        
        # عبارات الرفض المهذب
        'لا': [
            'لا بأس، كما تشاء',
            'تمام، مافي مشكلة'
        ],
        'لا شكرا': [
            'لا بأس، كما تريد',
            'تمام، أي وقت تحتاج شيء قولي'
        ],

        # عبارات إضافية للمحادثة
        'ممكن تساعدني': [
            'طبعاً! كيف أقدر أساعدك؟',
            'أكيد، أنا هنا للمساعدة',
            'بكل سرور، وش محتاج؟'
        ],
        'عندي سؤال': [
            'تفضل، اسأل وأنا أجاوبك',
            'أنا جاهز للإجابة على أسئلتك',
            'تفضل، كلي آذان صاغية'
        ],
        'ما فهمت': [
            'خليني أشرح لك بطريقة أوضح',
            'ما عليه، أقدر أشرح مرة ثانية',
            'دعني أحاول التوضيح بشكل أفضل'
        ],
        'ممتاز': [
            'يسعدني أني قدرت أساعد! 😊',
            'شكراً لك! أحاول دائماً تقديم الأفضل',
            'هذا يسعدني كثيراً!'
        ],
        'رائع': [
            'شكراً لك! 🌟',
            'سعيد أن هذا أعجبك',
            'يسعدني سماع هذا!'
        ],
        'أحسنت': [
            'شكراً جزيلاً! 👏',
            'هذا من ذوقك',
            'أشكرك على هذا التقدير'
        ],
        'بالتوفيق': [
            'الله يوفقك! 🙏',
            'شكراً لك، ولك بالمثل',
            'الله يوفق الجميع'
        ],
        'الله يسعدك': [
            'ويسعدك يارب! 💝',
            'الله يسعد أيامك',
            'تسلم، الله يسعد الجميع'
        ],
        'الله يخليك': [
            'ويخليك يارب! 🤲',
            'تسلم، الله يحفظك',
            'الله يخلي لك أحبابك'
        ],
        'الله يعطيك العافية': [
            'ويعطيك العافية! 💪',
            'الله يعافيك ويسلمك',
            'تسلم، الله يقويك'
        ],
        'الله يوفقك': [
            'آمين وياك! 🤲',
            'ولك بالمثل يارب',
            'الله يوفقنا جميعاً'
        ],
        'يا هلا': [
            'هلا وغلا! 🌺',
            'يا هلا ومرحبا',
            'هلا بك وبمن معك'
        ],
        'يا مرحبا': [
            'مراحب! نورتنا 🌟',
            'يا مرحبا بك وبمن معك',
            'مرحبتين وثلاث!'
        ],
        'صباحو': [
            'صباح النور والسرور! 🌅',
            'صباحك عسل',
            'يا صباح الخير والبركة'
        ],
        'مساءو': [
            'مساء النور والسرور! 🌙',
            'مساءك عسل',
            'يا مساء الخير والبركة'
        ],
        'تسلم يدينك': [
            'الله يسلمك! 🙌',
            'تسلم من كل شر',
            'حياك الله'
        ],
        'الله يسلمك': [
            'ويسلمك يارب! 🤲',
            'تسلم من كل شر',
            'الله يحفظك'
        ],
        'يعطيك العافية': [
            'الله يعافيك! 💪',
            'ويعطيك العافية',
            'تسلم يا رب'
        ],
        'ما قصرت': [
            'العفو! هذا واجبي 🙏',
            'تسلم، ما سوينا شي',
            'الله يسعدك'
        ],
        'بارك الله فيك': [
            'وفيك بارك الله! 🤲',
            'آمين وإياك',
            'جزاك الله خير'
        ],
        'جزاك الله خير': [
            'وإياك! جزاك الله خير 🤲',
            'وجزاك مثله وأكثر',
            'الله يجزيك كل خير'
        ],
        'الله يجزاك خير': [
            'وياك يارب! 🤲',
            'ويجزيك خير',
            'آمين يارب العالمين'
        ],
        'تسلم على هذا': [
            'الله يسلمك! العفو 🙏',
            'حياك الله، ما سوينا شي',
            'تسلم من كل شر'
        ],
        'يعجبني هذا': [
            'حلو! يسعدني هذا 😊',
            'شكراً لك! هذا يشجعني',
            'يسعدني أن هذا أعجبك'
        ],
        'حلو': [
            'شكراً لك! 😊',
            'يسعدني هذا',
            'حلو تواجدك'
        ],
        'جميل': [
            'شكراً جزيلاً! 🌟',
            'يسعدني أن هذا أعجبك',
            'أنت الأجمل'
        ],
        'عجيب': [
            'شكراً لك! 🌟',
            'يسعدني أن هذا أثار إعجابك',
            'أحاول دائماً التميز'
        ],
        'واو': [
            'شكراً لك! 🌟',
            'يسعدني ردة فعلك',
            'هذا من ذوقك'
        ],
        'برافو': [
            'شكراً جزيلاً! 👏',
            'تسلم على التشجيع',
            'هذا من ذوقك'
        ],
        'أشكرك': [
            'العفو! سعيد بخدمتك 🙏',
            'لا شكر على واجب',
            'أنا هنا لمساعدتك'
        ],
        'شكراً جزيلاً': [
            'العفو! سعيد بخدمتك 🙏',
            'لا شكر على واجب',
            'أنا هنا لمساعدتك'
        ],
        'شكراً لك': [
            'العفو! سعيد بخدمتك 🙏',
            'لا شكر على واجب',
            'أنا هنا لمساعدتك'
        ]
    }
    
    # إضافة الردود للذاكرة
    knowledge['casual_responses'].update(casual_responses)
    
    # إضافة كحقائق أيضاً
    fact_counter = len(knowledge.get('facts', {})) + 1
    
    for greeting, responses in casual_responses.items():
        fact_id = f"casual_response_{fact_counter}"
        knowledge['facts'][fact_id] = {
            'type': 'casual_response',
            'trigger': greeting,
            'responses': responses,
            'learned_at': datetime.now().isoformat(),
            'category': 'تحيات وردود عامية'
        }
        fact_counter += 1
        
        print(f"✅ تعلم رد على: '{greeting}'")
        print(f"   الردود: {responses[0]}")
        print()
    
    # حفظ الذاكرة المحدثة
    with open('ml_t1_memory.json', 'w', encoding='utf-8') as f:
        json.dump(knowledge, f, ensure_ascii=False, indent=2)
    
    print("🎉 انتهى تعليم الردود العامية!")
    print(f"📚 تم تعليم {len(casual_responses)} نوع من التحيات والردود")
    print("💾 تم حفظ كل شيء في ذاكرة النموذج")
    print()
    print("🚀 الآن النموذج يعرف كيف يرد على:")
    print("   • التحيات الإسلامية (السلام عليكم)")
    print("   • التحيات العامة (مرحبا، هلا، اهلا)")
    print("   • السؤال عن الحال (كيف حالك، شلونك)")
    print("   • عبارات الشكر (شكراً، يعطيك العافية)")
    print("   • تحيات الوقت (صباح الخير، مساء الخير)")
    print("   • عبارات الوداع (مع السلامة، باي)")
    print("   • والمزيد...")

if __name__ == "__main__":
    teach_casual_responses()
