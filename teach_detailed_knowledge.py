# -*- coding: utf-8 -*-
"""
تعليم النموذج معلومات مفصلة وشاملة
"""

import json
import os
from datetime import datetime

def teach_detailed_knowledge():
    """تعليم معلومات مفصلة"""
    
    print("📚 بدء تعليم المعلومات المفصلة...")
    print("=" * 60)
    
    # تحميل الذاكرة الحالية
    try:
        with open('ml_t1_memory.json', 'r', encoding='utf-8') as f:
            knowledge = json.load(f)
    except:
        knowledge = {'concepts': {}, 'facts': {}, 'insights': []}
    
    # التأكد من وجود الأقسام المطلوبة
    if 'facts' not in knowledge:
        knowledge['facts'] = {}
    if 'detailed_answers' not in knowledge:
        knowledge['detailed_answers'] = {}
    
    # معلومات مفصلة عن الخوارزمي
    al_khwarizmi_info = {
        "من هو الخوارزمي": """الخوارزمي هو محمد بن موسى الخوارزمي (780-850م)، عالم رياضيات وفلك وجغرافيا مسلم عاش في بغداد خلال العصر العباسي. يُعتبر من أعظم علماء الرياضيات في التاريخ وأبو علم الجبر.""",
        
        "إنجازات الخوارزمي": """
1. أسس علم الجبر وألف كتاب "الجبر والمقابلة"
2. طور الأرقام العربية (الهندية) ونشرها في أوروبا
3. ألف كتاب "الخوارزميات" في الحساب
4. رسم خرائط دقيقة للعالم المعروف آنذاك
5. حسب محيط الأرض بدقة مذهلة
6. طور طرق حل المعادلات الرياضية
7. وضع أسس علم المثلثات""",
        
        "تأثير الخوارزمي على أوروبا": """
1. نقل الأرقام العربية إلى أوروبا عبر ترجمة كتبه
2. أثر على علماء أوروبا مثل فيبوناتشي
3. كتبه دُرست في الجامعات الأوروبية لقرون
4. ساهم في النهضة الأوروبية من خلال علومه
5. كلمة "Algorithm" مشتقة من اسمه
6. كلمة "Algebra" مشتقة من كتابه "الجبر والمقابلة" """,
        
        "علاقة الخوارزمي بالرياضيات الحديثة": """
1. علم الجبر الذي أسسه أساس الرياضيات الحديثة
2. الخوارزميات التي طورها تُستخدم في البرمجة اليوم
3. طرق حل المعادلات التي وضعها ما زالت مستخدمة
4. أسس علم المثلثات المستخدم في الهندسة والفيزياء
5. نظام الأرقام العربية أساس الحاسوب والتقنية
6. مفاهيمه في الجغرافيا أساس نظم المعلومات الجغرافية"""
    }
    
    # معلومات مفصلة عن الذكاء الاصطناعي
    ai_info = {
        "ما هو الذكاء الاصطناعي": """الذكاء الاصطناعي هو فرع من علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب ذكاءً بشرياً، مثل التعلم والتفكير واتخاذ القرارات والتعرف على الأنماط.""",
        
        "كيف يعمل الذكاء الاصطناعي": """
1. التعلم الآلي: النظام يتعلم من البيانات
2. الشبكات العصبية: محاكاة عمل الدماغ البشري
3. معالجة البيانات: تحليل كميات ضخمة من المعلومات
4. التعرف على الأنماط: اكتشاف العلاقات في البيانات
5. اتخاذ القرارات: بناءً على التحليل والتعلم""",
        
        "تطبيقات الذكاء الاصطناعي": """
في الطب: تشخيص الأمراض، تطوير الأدوية، الجراحة الروبوتية
في التعليم: التعلم التكيفي، المساعدين الافتراضيين، تقييم الطلاب
في الصناعة: الأتمتة، التحكم في الجودة، الصيانة التنبؤية
في النقل: السيارات ذاتية القيادة، تحسين المرور
في المالية: كشف الاحتيال، التداول الآلي، تقييم المخاطر"""
    }
    
    # معلومات عن الحضارة الإسلامية
    islamic_civilization_info = {
        "الحضارة الإسلامية": """الحضارة الإسلامية هي حضارة عظيمة امتدت من القرن السابع إلى القرن الخامس عشر الميلادي، وشملت مناطق واسعة من العالم وأنتجت إنجازات علمية وثقافية هائلة.""",
        
        "العلماء المسلمون": """
1. الخوارزمي: أبو الجبر والخوارزميات
2. ابن سينا: أبو الطب الحديث
3. ابن رشد: فيلسوف وطبيب أندلسي
4. الرازي: طبيب وكيميائي عظيم
5. ابن الهيثم: أبو البصريات والمنهج العلمي
6. الجاحظ: عالم الأحياء والأدب""",
        
        "إنجازات الحضارة الإسلامية": """
في الطب: المستشفيات، الجراحة، الصيدلة
في الرياضيات: الجبر، الخوارزميات، الأرقام العربية
في الفلك: المراصد، حساب حجم الأرض، الخرائط النجمية
في الكيمياء: التقطير، الكحول، الأحماض
في الفلسفة: الترجمة، التطوير، التأثير على أوروبا"""
    }
    
    # دمج كل المعلومات
    all_detailed_info = {}
    all_detailed_info.update(al_khwarizmi_info)
    all_detailed_info.update(ai_info)
    all_detailed_info.update(islamic_civilization_info)
    
    # إضافة المعلومات للذاكرة
    fact_counter = len(knowledge['facts']) + 1
    
    for question, detailed_answer in all_detailed_info.items():
        fact_id = f"detailed_fact_{fact_counter}"
        knowledge['facts'][fact_id] = {
            'type': 'detailed_answer',
            'question': question,
            'answer': detailed_answer,
            'learned_at': datetime.now().isoformat(),
            'category': 'معلومات مفصلة'
        }
        
        # إضافة للإجابات المفصلة أيضاً
        knowledge['detailed_answers'][question.lower()] = detailed_answer
        
        fact_counter += 1
        print(f"✅ تم تعليم: {question}")
    
    # حفظ الذاكرة المحدثة
    with open('ml_t1_memory.json', 'w', encoding='utf-8') as f:
        json.dump(knowledge, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 انتهى تعليم المعلومات المفصلة!")
    print(f"📚 تم تعليم {len(all_detailed_info)} موضوع مفصل")
    print("💾 تم حفظ كل شيء في ذاكرة النموذج")
    print()
    print("🚀 الآن النموذج يعرف:")
    print("   • معلومات مفصلة عن الخوارزمي")
    print("   • تفاصيل الذكاء الاصطناعي")
    print("   • معلومات الحضارة الإسلامية")
    print("   • إجابات شاملة ومفيدة")

if __name__ == "__main__":
    teach_detailed_knowledge()
