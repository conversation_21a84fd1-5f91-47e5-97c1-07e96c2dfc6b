# -*- coding: utf-8 -*-
"""
اختبار النموذج مع 1000+ سؤال متشابك ومعقد
"""

import time
import json
from datetime import datetime
from advanced_question_handler import AdvancedQuestionHandler

def generate_complex_questions():
    """توليد أسئلة معقدة ومتشابكة"""
    
    # أسئلة متشابكة حول العلوم
    science_questions = [
        "ما هو الذكاء الاصطناعي وكيف يعمل وما هي تطبيقاته في الطب والتعليم والصناعة؟",
        "كيف تطورت الفيزياء الكمية وما علاقتها بالحوسبة الكمية وما تأثيرها على المستقبل؟",
        "ما الفرق بين الهندسة الوراثية والطب الشخصي وكيف يمكن استخدامهما لعلاج السرطان؟",
        "كيف تعمل الشبكات العصبية وما علاقتها بالدماغ البشري وكيف تطورت عبر التاريخ؟",
        "ما هي النانو تكنولوجي وكيف تستخدم في الطب والإلكترونيات وما مخاطرها المحتملة؟"
    ]
    
    # أسئلة متشابكة حول التاريخ والحضارة
    history_questions = [
        "من هو الخوارزمي وما إنجازاته في الرياضيات والفلك وكيف أثر على النهضة الأوروبية؟",
        "كيف تطورت الحضارة الإسلامية في الأندلس وما دور العلماء والفلاسفة فيها؟",
        "ما علاقة ابن رشد بابن سينا وكيف أثرا على الفلسفة الأوروبية والطب؟",
        "كيف ساهم بيت الحكمة في بغداد في ترجمة العلوم اليونانية وتطوير العلوم الإسلامية؟",
        "ما الفرق بين فلسفة ابن رشد وابن سينا وكيف تأثرت أوروبا بأفكارهما؟"
    ]
    
    # أسئلة متشابكة حول التقنية
    tech_questions = [
        "ما الفرق بين البرمجة التقليدية والذكاء الاصطناعي وكيف يمكن دمجهما؟",
        "كيف تطورت الحوسبة السحابية وما علاقتها بإنترنت الأشياء والأمن السيبراني؟",
        "ما هو البلوك تشين وكيف يعمل وما تطبيقاته في المال والصحة والتعليم؟",
        "كيف تؤثر الثورة الصناعية الرابعة على سوق العمل والتعليم والمجتمع؟",
        "ما مستقبل الواقع الافتراضي والواقع المعزز في التعليم والطب والترفيه؟"
    ]
    
    # أسئلة فلسفية معقدة
    philosophy_questions = [
        "ما العلاقة بين الذكاء الاصطناعي والوعي البشري وهل يمكن للآلة أن تفكر؟",
        "كيف تؤثر التقنية على طبيعة الإنسان وما التحديات الأخلاقية للذكاء الاصطناعي؟",
        "ما الفرق بين المعرفة والحكمة وكيف يمكن للذكاء الاصطناعي اكتساب الحكمة؟",
        "هل يمكن للذكاء الاصطناعي أن يبدع ويبتكر أم أنه مجرد تقليد للإنسان؟",
        "ما مستقبل العلاقة بين الإنسان والآلة وكيف نحافظ على الإنسانية؟"
    ]
    
    # أسئلة متعددة الأجزاء
    multi_part_questions = []
    
    # توليد أسئلة مركبة
    for i in range(50):
        q1 = f"ما هو المفهوم رقم {i+1} في العلوم الحديثة؟"
        q2 = f"كيف يؤثر على المجتمع؟"
        q3 = f"ما مستقبله في العقد القادم؟"
        combined = f"{q1} {q2} {q3}"
        multi_part_questions.append(combined)
    
    # دمج كل الأسئلة
    all_questions = (
        science_questions * 20 +  # 100 سؤال علمي
        history_questions * 20 +  # 100 سؤال تاريخي
        tech_questions * 20 +     # 100 سؤال تقني
        philosophy_questions * 20 + # 100 سؤال فلسفي
        multi_part_questions * 12   # 600 سؤال متعدد الأجزاء
    )
    
    return all_questions[:1000]  # أول 1000 سؤال

def test_massive_questions():
    """اختبار النموذج مع 1000+ سؤال"""
    
    print("🚀 بدء اختبار النموذج مع 1000+ سؤال متشابك ومعقد")
    print("=" * 80)
    
    # إنشاء معالج الأسئلة
    handler = AdvancedQuestionHandler()
    
    # توليد الأسئلة
    print("📝 توليد 1000 سؤال معقد...")
    questions = generate_complex_questions()
    print(f"✅ تم توليد {len(questions)} سؤال")
    
    # بدء الاختبار
    start_time = time.time()
    print(f"\n⏰ بدء الاختبار في: {datetime.now().strftime('%H:%M:%S')}")
    
    # معالجة الأسئلة على دفعات
    batch_size = 50
    all_answers = {}
    
    for i in range(0, len(questions), batch_size):
        batch = questions[i:i+batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (len(questions) + batch_size - 1) // batch_size
        
        print(f"\n📦 معالجة الدفعة {batch_num}/{total_batches} ({len(batch)} سؤال)")
        
        # معالجة الدفعة
        batch_answers = handler.handle_multiple_questions(batch)
        all_answers.update(batch_answers)
        
        # إحصائيات الدفعة
        success_rate = len(batch_answers) / len(batch) * 100
        print(f"✅ نجح في {len(batch_answers)}/{len(batch)} سؤال ({success_rate:.1f}%)")
        
        # استراحة قصيرة
        time.sleep(0.5)
    
    # انتهاء الاختبار
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n🎉 انتهى الاختبار!")
    print(f"⏱️ الوقت الإجمالي: {total_time:.2f} ثانية")
    print(f"⚡ متوسط الوقت لكل سؤال: {total_time/len(questions):.3f} ثانية")
    
    # تحليل النتائج
    print(f"\n📊 **تحليل شامل للنتائج:**")
    print("=" * 60)
    print(f"📝 إجمالي الأسئلة: {len(questions)}")
    print(f"✅ الأسئلة المجابة: {len(all_answers)}")
    print(f"📈 معدل النجاح: {len(all_answers)/len(questions)*100:.1f}%")
    
    # تصنيف حسب التعقيد
    complexity_stats = {"بسيط": 0, "متوسط": 0, "معقد": 0, "متشابك": 0}
    
    for question in questions:
        if question in handler.answer_cache:
            complexity = handler.answer_cache[question]['complexity']
            complexity_stats[complexity] += 1
    
    print(f"\n📊 **توزيع الأسئلة حسب التعقيد:**")
    for complexity, count in complexity_stats.items():
        percentage = count / len(questions) * 100
        print(f"• {complexity}: {count} سؤال ({percentage:.1f}%)")
    
    # حفظ النتائج
    results = {
        'total_questions': len(questions),
        'answered_questions': len(all_answers),
        'success_rate': len(all_answers)/len(questions)*100,
        'total_time': total_time,
        'avg_time_per_question': total_time/len(questions),
        'complexity_stats': complexity_stats,
        'timestamp': datetime.now().isoformat()
    }
    
    with open('test_1000_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ النتائج في: test_1000_results.json")
    
    # عرض عينة من الإجابات
    print(f"\n📋 **عينة من الإجابات:**")
    print("-" * 60)
    
    sample_questions = list(all_answers.keys())[:3]
    for i, question in enumerate(sample_questions, 1):
        answer = all_answers[question]
        print(f"\n❓ **السؤال {i}:** {question[:100]}...")
        print(f"💡 **الإجابة:** {answer[:200]}...")
        print("-" * 40)
    
    return results

def quick_test():
    """اختبار سريع مع 10 أسئلة"""
    print("⚡ اختبار سريع مع 10 أسئلة معقدة")
    print("=" * 50)
    
    handler = AdvancedQuestionHandler()
    
    quick_questions = [
        "ما هو الذكاء الاصطناعي وكيف يعمل؟",
        "من هو الخوارزمي وما إنجازاته؟",
        "كيف تطورت الحضارة الإسلامية؟",
        "ما الفرق بين البرمجة والذكاء الاصطناعي؟",
        "كيف تؤثر التقنية على المستقبل؟",
        "ما علاقة ابن رشد بالفلسفة الأوروبية؟",
        "كيف تعمل الشبكات العصبية؟",
        "ما مستقبل الطب الشخصي؟",
        "كيف يمكن دمج الذكاء الاصطناعي في التعليم؟",
        "ما التحديات الأخلاقية للذكاء الاصطناعي؟"
    ]
    
    start_time = time.time()
    answers = handler.handle_multiple_questions(quick_questions)
    end_time = time.time()
    
    print(f"\n⏱️ الوقت: {end_time - start_time:.2f} ثانية")
    print(f"✅ نجح في {len(answers)}/{len(quick_questions)} سؤال")
    print(f"📈 معدل النجاح: {len(answers)/len(quick_questions)*100:.1f}%")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_test()
    else:
        test_massive_questions()
