# -*- coding: utf-8 -*-
"""
اختبار معلومات الحضارة الإسلامية
"""

# قاعدة معرفة للحضارة الإسلامية
knowledge_base = {
    "الحضارة الإسلامية": "الحضارة الإسلامية لعبت دوراً عظيماً ومحورياً في نقل العلوم والمعرفة للعالم! كانت جسراً حضارياً بين الشرق والغرب، حيث ترجم العلماء المسلمون أعمال اليونان والفرس والهنود وأضافوا عليها إبداعاتهم الخاصة.",
    
    "دور الحضارة الإسلامية": "دور الحضارة الإسلامية في نقل العلوم كان استثنائياً ومؤثراً جداً! لم تكن مجرد ناقل للمعرفة بل مطور ومبدع فيها. حفظت التراث اليوناني من الضياع، وطورت علوماً جديدة، وأسست منهجية البحث العلمي التجريبي.",
    
    "بيت الحكمة": "بيت الحكمة في بغداد كان أعظم مراكز العلم والترجمة في التاريخ الإسلامي! تأسس في عهد الخليفة هارون الرشيد وازدهر في عهد المأمون في القرن التاسع الميلادي.",
    
    "نقل العلوم": "نقل العلوم في الحضارة الإسلامية كان عملية إبداعية وليس مجرد ترجمة! العلماء المسلمون ترجموا وطوروا وأضافوا على المعرفة السابقة، مما أدى لثورة علمية حقيقية."
}

def get_response(user_input):
    """الحصول على إجابة"""
    user_input_lower = user_input.lower()
    
    # البحث الدقيق
    for key, answer in knowledge_base.items():
        key_words = key.lower().split()
        if all(word in user_input_lower for word in key_words):
            return f"{answer}\n\n🤔 هل تريد معرفة المزيد عن هذا الموضوع الرائع؟"
    
    # البحث الجزئي
    for key, answer in knowledge_base.items():
        key_words = key.lower().split()
        if any(word in user_input_lower for word in key_words):
            return f"{answer}\n\n🤔 هل تريد معرفة المزيد عن هذا الموضوع الرائع؟"
    
    return "موضوع مثير! أحب النقاش حول التاريخ والحضارة. هل يمكنك توضيح سؤالك أكثر؟"

# اختبار
print("🏛️ اختبار معلومات الحضارة الإسلامية")
print("=" * 60)

test_questions = [
    "ما رأيك في دور الحضارة الإسلامية؟",
    "كيف ساهمت الحضارة الإسلامية في نقل العلوم؟",
    "أخبرني عن بيت الحكمة",
    "ما دور المسلمين في نقل العلوم؟",
    "كيف أثرت الحضارة الإسلامية على العالم؟"
]

for question in test_questions:
    print(f"\n❓ السؤال: {question}")
    response = get_response(question)
    print(f"🤖 الإجابة: {response}")
    print("-" * 60)

print("\n✅ انتهى الاختبار!")
