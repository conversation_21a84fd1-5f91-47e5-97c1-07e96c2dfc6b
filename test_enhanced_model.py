# -*- coding: utf-8 -*-
"""
اختبار النموذج المحسن مع الإجابات التفاعلية
"""

from advanced_question_handler import AdvancedQuestionHandler
from enhanced_answer_generator import EnhancedAnswerGenerator

def test_enhanced_model():
    """اختبار النموذج المحسن"""
    
    print("🚀 **اختبار النموذج المحسن مع الإجابات التفاعلية**")
    print("=" * 80)
    
    # إنشاء المعالج والمولد
    handler = AdvancedQuestionHandler()
    enhancer = EnhancedAnswerGenerator()
    
    # أسئلة للاختبار
    test_questions = [
        "من هو الخوارزمي؟",
        "ما إنجازات الخوارزمي؟",
        "كيف أثر الخوارزمي على أوروبا؟",
        "ما هو الذكاء الاصطناعي؟",
        "ما هي الحضارة الإسلامية؟"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{'='*60}")
        print(f"🧪 **اختبار {i}/5**")
        print(f"❓ **السؤال:** {question}")
        print("="*60)
        
        # الحصول على الإجابة الأساسية
        basic_answer = handler.process_complex_question(question)
        
        # تحسين الإجابة
        topic = extract_topic_from_question(question)
        enhanced_answer = enhancer.create_engaging_response(basic_answer, topic, question)
        
        print("💡 **الإجابة المحسنة:**")
        print(enhanced_answer)
        print("\n" + "-"*60)

def extract_topic_from_question(question):
    """استخراج الموضوع من السؤال"""
    topics = {
        'خوارزمي': 'الخوارزمي',
        'ذكاء اصطناعي': 'الذكاء الاصطناعي',
        'حضارة إسلامية': 'الحضارة الإسلامية',
        'إنجازات': 'الخوارزمي',
        'أثر': 'الخوارزمي'
    }
    
    question_lower = question.lower()
    for key, topic in topics.items():
        if key in question_lower:
            return topic
    
    return "موضوع عام"

if __name__ == "__main__":
    test_enhanced_model()
