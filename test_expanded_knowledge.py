# -*- coding: utf-8 -*-
"""
اختبار قاعدة المعرفة الموسعة
"""

import time
import random
from knowledge_database import COMPREHENSIVE_KNOWLEDGE
from advanced_knowledge import ADVANCED_KNOWLEDGE, get_all_knowledge

def simulate_deep_thinking(question):
    """محاكاة التفكير العميق"""
    print("🧠 بدء التفكير العميق والتحليل المتقدم...")
    time.sleep(1)
    
    print("🔍 أحلل نوع السؤال ومستوى التعقيد...")
    time.sleep(0.8)
    
    print("📚 أبحث في قاعدة المعرفة الضخمة الموسعة...")
    time.sleep(1.2)
    
    print("🧩 أربط المعلومات من مصادر متعددة...")
    time.sleep(1)
    
    print("🎯 أحدد أفضل منهج للإجابة...")
    time.sleep(0.8)
    
    print("✍️ أصيغ إجابة شاملة ومتعمقة...")
    time.sleep(1)

def search_in_expanded_knowledge(question):
    """البحث في قاعدة المعرفة الموسعة"""
    all_knowledge = get_all_knowledge()
    question_lower = question.lower()
    
    # البحث المباشر
    for key, data in all_knowledge.items():
        if any(word in question_lower for word in key.lower().split()):
            return data
    
    # البحث في المواضيع المرتبطة
    for key, data in all_knowledge.items():
        related_topics = data.get('related_topics', [])
        if any(topic.lower() in question_lower for topic in related_topics):
            return data
    
    return None

def test_knowledge_system():
    """اختبار شامل لنظام المعرفة"""
    print("🤖 مرحباً! أنا أمؤلي - النسخة المحدثة مع قاعدة معرفة ضخمة!")
    print("=" * 90)
    print("📊 إحصائيات قاعدة المعرفة:")
    
    all_knowledge = get_all_knowledge()
    basic_count = len(COMPREHENSIVE_KNOWLEDGE)
    advanced_count = len(ADVANCED_KNOWLEDGE)
    total_count = len(all_knowledge)
    
    print(f"   📚 المعرفة الأساسية: {basic_count} موضوع")
    print(f"   🔬 المعرفة المتقدمة: {advanced_count} موضوع")
    print(f"   🌟 إجمالي المواضيع: {total_count} موضوع")
    print("-" * 90)
    
    # أسئلة اختبار متنوعة
    test_questions = [
        # أسئلة أساسية
        "ما عاصمة الأردن؟",
        "أخبرني عن ابن سينا",
        "ما هو المطبخ العربي؟",
        
        # أسئلة متقدمة
        "ما هي الفيزياء الكمية؟",
        "كيف تعمل الهندسة الوراثية؟",
        "ما هو الواقع الافتراضي؟",
        
        # أسئلة تقنية
        "أخبرني عن إنترنت الأشياء",
        "ما هي العملات الرقمية؟",
        
        # أسئلة علمية
        "ما هي الثقوب السوداء؟",
        "كيف يعمل علم الأعصاب؟"
    ]
    
    categories = {
        "الجغرافيا والدول": ["عاصمة الأردن"],
        "العلماء والتاريخ": ["ابن سينا"],
        "الثقافة والطعام": ["المطبخ العربي"],
        "الفيزياء المتقدمة": ["الفيزياء الكمية", "الثقوب السوداء"],
        "التقنية الحديثة": ["الواقع الافتراضي", "إنترنت الأشياء", "العملات الرقمية"],
        "العلوم الطبية": ["الهندسة الوراثية", "علم الأعصاب"]
    }
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n[{i}] 💬 سؤال اختباري: {question}")
        
        # تحديد فئة السؤال
        category = "عام"
        for cat, keywords in categories.items():
            if any(keyword in question for keyword in keywords):
                category = cat
                break
        
        print(f"🎯 فئة السؤال: {category}")
        
        # محاكاة التفكير العميق
        simulate_deep_thinking(question)
        
        # البحث عن الإجابة
        result = search_in_expanded_knowledge(question)
        
        if result:
            answer = result['answer']
            questions_list = result.get('questions', [])
            
            print(f"\n🤖 أمؤلي:")
            print(f"📝 {answer}")
            
            if questions_list:
                follow_up = random.choice(questions_list)
                print(f"\n🤔 {follow_up}")
            
            # إحصائيات
            response_words = len(answer.split())
            input_words = len(question.split())
            print(f"\n📊 إحصائيات: الإدخال {input_words} كلمة | الإجابة {response_words} كلمة")
            
        else:
            print(f"\n🤖 أمؤلي:")
            print(f"📝 هذا سؤال مثير للاهتمام! أعتقد أن هذا الموضوع يحتاج لبحث أعمق وتحليل متعدد الجوانب. دعني أفكر فيه أكثر وأعود إليك بإجابة شاملة.")
            print(f"\n🤔 هل يمكنك إعادة صياغة السؤال أو إعطائي المزيد من التفاصيل؟")
        
        print("-" * 90)
    
    # ملخص النتائج
    print(f"\n🌟 انتهى الاختبار الشامل!")
    print(f"✅ تم اختبار {len(test_questions)} أسئلة متنوعة")
    print(f"📚 قاعدة المعرفة تغطي {total_count} موضوع")
    print(f"🧠 نظام التفكير العميق يعمل بكفاءة عالية")
    print(f"🎯 الإجابات مفصلة ومتعمقة")
    print(f"🤔 أسئلة المتابعة ذكية وتفاعلية")
    
    print("\n🚀 المميزات الجديدة:")
    print("   ✨ قاعدة معرفة موسعة تشمل مواضيع متقدمة")
    print("   ✨ تغطية شاملة للعلوم والتقنية والثقافة")
    print("   ✨ معلومات عن الدول العربية والعلماء")
    print("   ✨ مواضيع متقدمة مثل الفيزياء الكمية والهندسة الوراثية")
    print("   ✨ تقنيات مستقبلية مثل الواقع الافتراضي وإنترنت الأشياء")
    print("   ✨ علوم طبية متقدمة وعلم الأعصاب")
    
    print("\n💡 النموذج جاهز للاستخدام مع المعرفة الموسعة!")

def show_knowledge_categories():
    """عرض فئات المعرفة المتاحة"""
    print("\n📚 فئات المعرفة المتاحة:")
    print("=" * 50)
    
    categories = {
        "🌍 الجغرافيا والدول": [
            "عواصم الدول العربية",
            "معلومات تاريخية وثقافية",
            "العراق، السعودية، مصر، الأردن، لبنان، سوريا"
        ],
        
        "🏛️ التاريخ والحضارة": [
            "بيت الحكمة والحضارة الإسلامية",
            "العلماء المسلمون (ابن سينا، الخوارزمي)",
            "تاريخ بغداد والخلافة العباسية"
        ],
        
        "💻 التقنية والذكاء الاصطناعي": [
            "الذكاء الاصطناعي والبرمجة",
            "الهواتف الذكية وإنترنت الأشياء",
            "الواقع الافتراضي والواقع المعزز"
        ],
        
        "🔬 العلوم المتقدمة": [
            "الفيزياء الكمية والثقوب السوداء",
            "الهندسة الوراثية والطب الشخصي",
            "علم الأعصاب والدماغ البشري"
        ],
        
        "🍽️ الثقافة والفنون": [
            "المطبخ العربي والأطباق التقليدية",
            "الموسيقى العربية والشعر",
            "الأدب والفنون التراثية"
        ],
        
        "💰 الاقتصاد والتقنية المالية": [
            "العملات الرقمية والبلوك تشين",
            "الاقتصاد الرقمي والاستثمار",
            "التمويل اللامركزي"
        ],
        
        "🌱 البيئة والاستدامة": [
            "الطاقة المتجددة والطاقة الشمسية",
            "التغير المناخي وحماية البيئة",
            "التنمية المستدامة"
        ],
        
        "⚽ الرياضة والترفيه": [
            "كرة القدم والرياضات المختلفة",
            "الصحة واللياقة البدنية",
            "الألعاب والترفيه"
        ]
    }
    
    for category, topics in categories.items():
        print(f"\n{category}:")
        for topic in topics:
            print(f"   • {topic}")
    
    print(f"\n🎯 إجمالي: {len(get_all_knowledge())} موضوع متخصص!")

if __name__ == "__main__":
    show_knowledge_categories()
    test_knowledge_system()
