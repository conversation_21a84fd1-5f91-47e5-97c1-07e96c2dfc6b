# -*- coding: utf-8 -*-
"""
اختبار سريع للنموذج المحسن
"""

import os

def main():
    print("🚀 اختبار النموذج المحسن")
    print("=" * 40)
    
    # التحقق من وجود الملفات
    model_file = "ml-T1-Simple-Advanced.h5"
    tokenizer_file = "ml-T1-Simple-Advanced_tokenizer.pkl"
    
    if os.path.exists(model_file) and os.path.exists(tokenizer_file):
        print("✅ النموذج موجود ومدرب")
        print("\n🎯 لتشغيل النموذج:")
        print("python run_simple_advanced.py")
        
        # تشغيل مباشر
        choice = input("\nهل تريد تشغيل النموذج الآن؟ (y/n): ")
        if choice.lower() in ['y', 'yes', 'نعم']:
            os.system("python run_simple_advanced.py")
    else:
        print("⚠️ النموذج غير مدرب")
        print("\n🔧 لتدريب النموذج المحسن:")
        print("python t1_simple_advanced.py")
        
        # تدريب مباشر
        choice = input("\nهل تريد بدء التدريب الآن؟ (y/n): ")
        if choice.lower() in ['y', 'yes', 'نعم']:
            print("\n🚀 بدء التدريب...")
            os.system("python t1_simple_advanced.py")

if __name__ == "__main__":
    main()
