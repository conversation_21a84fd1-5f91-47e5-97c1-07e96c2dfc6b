# -*- coding: utf-8 -*-
"""
نظام ذكاء اصطناعي تفاعلي متطور - النسخة النهائية
مع جميع المميزات التفاعلية المتقدمة
"""

import random
import time
import json
from datetime import datetime
from collections import defaultdict

class UltimateInteractiveAI:
    """نظام ذكاء اصطناعي تفاعلي متطور"""
    
    def __init__(self):
        self.personality_modes = ['متحمس', 'ودود', 'فضولي', 'مرح', 'حكيم', 'مبدع']
        self.current_mood = 'متحمس'
        self.user_interactions = 0
        
        # ردود عاطفية متنوعة
        self.emotional_responses = {
            'excitement': ["🎉 هذا رائع!", "⚡ واو! مذهل!", "🌟 أحب هذا!", "🚀 رائع جداً!"],
            'curiosity': ["🤔 مثير للاهتمام!", "🔍 دعنا نتعمق أكثر!", "💭 هذا يجعلني أفكر!", "🧐 فضولي لمعرفة المزيد!"],
            'empathy': ["😊 أفهم تماماً", "💝 أقدر مشاعرك", "🤗 معاً سنجد الحلول", "💪 أنا هنا لدعمك"],
            'encouragement': ["👏 أحسنت!", "🌟 تفكير رائع!", "💡 تلهمني!", "🎯 ممتاز!"]
        }
        
        # عناصر تفاعلية
        self.interactive_elements = {
            'polls': ["📊 ما رأيك؟ (مثير/مفيد/معقد/رائع)", "🗳️ أي جانب يثيرك أكثر؟", "📈 قيم من 1-10!"],
            'games': ["🎮 خمن ما سأقوله!", "🧩 اربط هذا بحياتك!", "🎯 كم تطبيق يستخدم هذا؟"],
            'challenges': ["💪 تحدي الـ60 ثانية!", "🏃‍♂️ ابحث عن مثال حولك!", "🎪 ارسم أو اكتب قصة!"],
            'scenarios': ["🎭 اشرح لطفل عمره 10 سنوات!", "🚀 كيف سيكون في 2030؟", "🌍 تأثيره على العالم؟"]
        }
    
    def analyze_user_mood(self, user_input):
        """تحليل مزاج المستخدم"""
        positive_words = ['رائع', 'ممتاز', 'أحب', 'مثير', 'جميل', 'واو', 'مذهل']
        negative_words = ['صعب', 'معقد', 'لا أفهم', 'مملة', 'مشكلة', 'محير']
        curious_words = ['كيف', 'لماذا', 'ما', 'فضولي', 'أريد', 'اشرح']
        
        user_lower = user_input.lower()
        
        if any(word in user_lower for word in positive_words):
            return 'positive'
        elif any(word in user_lower for word in negative_words):
            return 'negative'
        elif any(word in user_lower for word in curious_words):
            return 'curious'
        return 'neutral'
    
    def get_personality_response(self, mood):
        """الحصول على رد شخصي حسب المزاج"""
        responses = {
            'positive': ["🔥 طاقتك الإيجابية معدية!", "😄 أشعر بحماسك!", "✨ هذا يجعلني متحمساً!"],
            'negative': ["😊 لا تقلق، سأساعدك!", "🤗 معاً سنجعل الأمر أسهل!", "💪 أنا هنا لدعمك!"],
            'curious': ["🎨 أحب فضولك!", "🔍 فضولك يلهمني!", "🤔 دعنا نستكشف معاً!"],
            'neutral': ["🌟 مرحباً صديقي!", "🚀 مستعد لرحلة ممتعة؟", "💡 دعنا نجعل هذا مثيراً!"]
        }
        return random.choice(responses[mood])
    
    def create_engaging_response(self, basic_answer, topic, user_input):
        """إنشاء رد تفاعلي جذاب"""
        self.user_interactions += 1
        user_mood = self.analyze_user_mood(user_input)
        
        response_parts = []
        
        # 1. ترحيب شخصي
        greeting = self.get_personality_response(user_mood)
        response_parts.append(greeting)
        
        # 2. الإجابة الأساسية مع تحسينات
        enhanced_answer = self.enhance_answer(basic_answer, topic)
        response_parts.append(enhanced_answer)
        
        # 3. ربط بالواقع المعاصر
        modern_connection = self.get_modern_connection(topic)
        response_parts.append(f"🌐 **الربط بعصرنا:** {modern_connection}")
        
        # 4. أمثلة عملية
        practical_examples = self.get_practical_examples(topic)
        response_parts.append(f"🔧 **أمثلة من حياتك:**\n{practical_examples}")
        
        # 5. تشبيه إبداعي
        analogy = self.create_analogy(topic)
        response_parts.append(f"🎨 **تشبيه إبداعي:** {analogy}")
        
        # 6. عنصر تفاعلي
        interactive = self.get_interactive_element()
        response_parts.append(f"🎯 **تفاعل معي:** {interactive}")
        
        # 7. سؤال تأملي
        reflection = self.get_reflection_question(topic)
        response_parts.append(f"🤔 **سؤال للتأمل:** {reflection}")
        
        # 8. تحدي أو لعبة
        challenge = self.get_challenge(topic)
        response_parts.append(f"🎮 **تحدي ممتع:** {challenge}")
        
        # 9. اقتراح متعدد الوسائط
        multimedia = self.get_multimedia_suggestion(topic)
        response_parts.append(f"📱 **اقتراح:** {multimedia}")
        
        # 10. اقتباس حكيم
        quote = self.get_wisdom_quote(topic)
        response_parts.append(f"💫 **حكمة:** {quote}")
        
        # 11. دعوة للعمل
        cta = self.get_call_to_action(topic)
        response_parts.append(f"🚀 **مهمتك:** {cta}")
        
        # 12. إنجاز (أحياناً)
        if self.user_interactions % 3 == 0:
            achievement = self.get_achievement()
            response_parts.append(f"🏆 **إنجاز جديد:** {achievement}")
        
        return "\n\n".join(response_parts)
    
    def enhance_answer(self, basic_answer, topic):
        """تحسين الإجابة الأساسية"""
        enhancements = [
            "📖 **معلومة شيقة:**",
            "🎬 **تخيل هذا المشهد:**",
            "🌟 **الحقيقة المذهلة:**"
        ]
        
        enhancement = random.choice(enhancements)
        return f"{enhancement}\n{basic_answer}"
    
    def get_modern_connection(self, topic):
        """ربط الموضوع بالعصر الحديث"""
        connections = {
            'الذكاء الاصطناعي': "كل مرة تستخدم سيري أو جوجل، تتفاعل مع الذكاء الاصطناعي!",
            'الخوارزمي': "خوارزميات فيسبوك وإنستغرام مبنية على أسس وضعها الخوارزمي!",
            'default': f"هذا المفهوم يؤثر على تقنيات اليوم بطرق مذهلة!"
        }
        return connections.get(topic, connections['default'])
    
    def get_practical_examples(self, topic):
        """أمثلة عملية من الحياة اليومية"""
        examples = {
            'الذكاء الاصطناعي': "• نتفليكس يقترح أفلام\n• واتساب يترجم الرسائل\n• الكاميرا تتعرف على وجهك",
            'الخوارزمي': "• الآلة الحاسبة في هاتفك\n• GPS يحسب أقصر طريق\n• البحث في جوجل",
            'default': f"• التطبيقات الذكية\n• الأنظمة الحديثة\n• التقنيات المتطورة"
        }
        return examples.get(topic, examples['default'])
    
    def create_analogy(self, topic):
        """إنشاء تشبيه إبداعي"""
        analogies = {
            'الذكاء الاصطناعي': "مثل طباخ ماهر يتعلم وصفات جديدة كل يوم ويبدع في الطبخ!",
            'الخوارزمي': "مثل مهندس عبقري بنى جسراً بين الماضي والمستقبل في عالم الرياضيات!",
            'default': f"{topic} مثل قطعة أحجية مهمة في لغز الحياة الكبير!"
        }
        return analogies.get(topic, analogies['default'])
    
    def get_interactive_element(self):
        """عنصر تفاعلي عشوائي"""
        element_type = random.choice(list(self.interactive_elements.keys()))
        return random.choice(self.interactive_elements[element_type])
    
    def get_reflection_question(self, topic):
        """سؤال للتأمل"""
        questions = [
            f"كيف غيّر {topic} نظرتك للعالم؟",
            f"ما أكثر شيء يثير إعجابك في {topic}؟",
            f"كيف تتخيل {topic} بعد 50 سنة؟",
            f"لو كنت تشرح {topic} لجدك، ماذا ستقول؟"
        ]
        return random.choice(questions)
    
    def get_challenge(self, topic):
        """تحدي أو لعبة"""
        challenges = [
            f"اكتب 5 كلمات تذكرك بـ {topic} في 30 ثانية!",
            f"اربط {topic} بـ 3 أشياء في غرفتك الآن!",
            f"اشرح {topic} في دقيقة واحدة لصديق!",
            f"ارسم شكلاً يمثل {topic}!"
        ]
        return random.choice(challenges)
    
    def get_multimedia_suggestion(self, topic):
        """اقتراح محتوى متعدد الوسائط"""
        suggestions = [
            f"ابحث عن فيديو 'شرح {topic} بالعربي' في يوتيوب",
            f"استمع لبودكاست عن {topic} أثناء المشي",
            f"جرب تطبيقاً يستخدم {topic}",
            f"اقرأ مقالاً قصيراً عن {topic} قبل النوم"
        ]
        return random.choice(suggestions)
    
    def get_wisdom_quote(self, topic):
        """اقتباس حكيم"""
        quotes = [
            f"'المعرفة بـ {topic} مثل النور، كلما انتشرت أضاءت عقولاً أكثر'",
            f"'من تعلم {topic} اليوم، سيقود التغيير غداً'",
            f"'الفضول حول {topic} هو أول خطوة نحو الإبداع'",
            f"'تعلم {topic} رحلة، وليس مجرد وجهة'"
        ]
        return random.choice(quotes)
    
    def get_call_to_action(self, topic):
        """دعوة للعمل"""
        actions = [
            f"ابحث عن 3 طرق يؤثر بها {topic} على حياتك اليومية!",
            f"شارك هذه المعلومة مع صديق مهتم بـ {topic}!",
            f"اكتب تغريدة من 280 حرف عن {topic}!",
            f"فكر في سؤال جديد عن {topic} واطرحه علي!"
        ]
        return random.choice(actions)
    
    def get_achievement(self):
        """إنجاز للمستخدم"""
        achievements = [
            "محادث فضولي - طرحت أسئلة رائعة!",
            "مستكشف المعرفة - تفاعلت بنشاط!",
            "مفكر عميق - أظهرت اهتماماً حقيقياً!",
            "رائد التعلم - واصلت الاستكشاف!"
        ]
        return random.choice(achievements)

def test_ultimate_ai():
    """اختبار النظام النهائي"""
    ai = UltimateInteractiveAI()
    
    print("🚀 **اختبار النظام التفاعلي النهائي**")
    print("=" * 80)
    
    test_cases = [
        ("ما هو الذكاء الاصطناعي؟ أنا فضولي جداً!", "الذكاء الاصطناعي"),
        ("هذا رائع! أريد المزيد!", "الذكاء الاصطناعي"),
        ("من هو الخوارزمي؟", "الخوارزمي")
    ]
    
    basic_answer = "الذكاء الاصطناعي هو تقنية متقدمة تحاكي التفكير البشري وتتعلم من البيانات لحل المشاكل واتخاذ القرارات الذكية."
    
    for i, (user_input, topic) in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"🧪 **اختبار {i}**")
        print(f"👤 المستخدم: {user_input}")
        print("="*60)
        
        response = ai.create_engaging_response(basic_answer, topic, user_input)
        print("🤖 **الرد التفاعلي المتطور:**")
        print(response)
        print("\n" + "-"*60)

if __name__ == "__main__":
    test_ultimate_ai()
