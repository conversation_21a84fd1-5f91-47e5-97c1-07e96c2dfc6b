# 🚀 دليل تشغيل النظام

## 📋 الملفات المطلوبة:
تأكد من وجود هذه الملفات في نفس المجلد:
- `run_ai.py` (النسخة المبسطة)
- `ultimate_arabic_ai.py` (النسخة الكاملة)
- `deep_thinking_system.py`
- `self_discussion_system.py`
- `knowledge_database.py`
- `advanced_knowledge.py`

## 🎯 طرق التشغيل:

### 1️⃣ الطريقة الأسهل (النسخة المبسطة):
```bash
python run_ai.py
```

### 2️⃣ النسخة الكاملة:
```bash
python ultimate_arabic_ai.py
```

### 3️⃣ باستخدام Python 3:
```bash
python3 run_ai.py
```

### 4️⃣ المسار الكامل:
```bash
C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.12.exe run_ai.py
```

### 5️⃣ باستخدام ملف Batch:
انقر مرتين على `start_ai.bat`

## 🖥️ من Command Prompt:
1. افتح Command Prompt (cmd)
2. انتقل للمجلد: `cd C:\Users\<USER>\Desktop\T1`
3. شغل الأمر: `python run_ai.py`

## 🐍 من PowerShell:
1. افتح PowerShell
2. انتقل للمجلد: `cd C:\Users\<USER>\Desktop\T1`
3. شغل الأمر: `python run_ai.py`

## 💻 من VS Code:
1. افتح VS Code
2. افتح المجلد `C:\Users\<USER>\Desktop\T1`
3. افتح ملف `run_ai.py`
4. اضغط F5 أو Ctrl+F5

## 🔧 حل المشاكل:

### إذا ظهرت رسالة "python is not recognized":
```bash
# جرب هذا بدلاً من python:
py run_ai.py
```

### إذا لم يعمل Python:
```bash
# استخدم المسار الكامل:
C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.12.exe run_ai.py
```

### إذا ظهرت أخطاء في الاستيراد:
تأكد من وجود جميع الملفات في نفس المجلد

## 🌟 مميزات النظام:

### النسخة المبسطة (`run_ai.py`):
- ✅ سهلة التشغيل
- ✅ تفكير عميق محاكى
- ✅ إجابات ذكية
- ✅ شخصية ودودة

### النسخة الكاملة (`ultimate_arabic_ai.py`):
- ✅ تفكير عميق حقيقي
- ✅ مناقشة ذاتية متطورة
- ✅ قاعدة معرفة ضخمة (38 موضوع)
- ✅ تحليل متعدد الأوجه

## 🎮 كيفية الاستخدام:
1. شغل النظام بإحدى الطرق أعلاه
2. انتظر ظهور رسالة الترحيب
3. ابدأ المحادثة بكتابة رسالتك
4. اكتب "خروج" لإنهاء المحادثة

## 💡 أمثلة على الأسئلة:
- "مرحبا"
- "ما عاصمة العراق؟"
- "أخبرني عن الذكاء الاصطناعي"
- "ناقش موضوع بيت الحكمة"
- "كيف حالك؟"

## 🆘 للمساعدة:
إذا واجهت أي مشكلة، تأكد من:
- ✅ تثبيت Python بشكل صحيح
- ✅ وجود جميع الملفات
- ✅ استخدام Command Prompt أو PowerShell
- ✅ كتابة الأمر بشكل صحيح
